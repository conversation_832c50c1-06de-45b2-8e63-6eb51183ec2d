#!/bin/bash

echo "🍷 Setting up Wine for Windows builds"
echo "====================================="

# Check if Wine is already installed
if command -v wine &> /dev/null; then
    echo "✅ Wine is already installed"
    wine --version
else
    echo "📦 Installing Wine..."
    
    # Update package list
    sudo apt update
    
    # Install Wine
    sudo apt install -y wine
    
    if [ $? -eq 0 ]; then
        echo "✅ Wine installed successfully"
        wine --version
    else
        echo "❌ Failed to install Wine"
        echo "💡 Try manual installation:"
        echo "   sudo apt update"
        echo "   sudo apt install wine"
        exit 1
    fi
fi

# Configure Wine (minimal setup)
echo "🔧 Configuring Wine..."
export WINEARCH=win64
export WINEPREFIX=~/.wine-shege

# Initialize Wine prefix
if [ ! -d "$WINEPREFIX" ]; then
    echo "🍷 Initializing Wine environment..."
    winecfg /v win10 2>/dev/null &
    sleep 5
    pkill winecfg 2>/dev/null
fi

echo "✅ Wine setup complete"
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this script from the SHEGE SRS project directory"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file..."
    cat > .env << EOF
SYSTEM_NAME=SHEGE SRS
MONGODB_URI=mongodb://localhost:27017/student-registration
PORT=3000
NODE_ENV=production
SESSION_SECRET=change-this-secret-key
EOF
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf dist/

# Build Windows portable
echo "🏗️  Building Windows portable application..."
echo "   This may take several minutes..."

export WINE_PREFIX=$WINEPREFIX
npm run build-portable-win

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Windows build completed successfully!"
    echo ""
    echo "📦 Built files:"
    ls -la dist/
    echo ""
    echo "📋 Windows executable details:"
    file dist/*.exe 2>/dev/null || echo "   Windows EXE files created"
    echo ""
    echo "🚀 To test on Windows:"
    echo "   1. Copy the .exe file to a Windows machine"
    echo "   2. Install MongoDB on Windows"
    echo "   3. Double-click the .exe file to run"
    echo ""
else
    echo ""
    echo "❌ Windows build failed"
    echo ""
    echo "🔍 Troubleshooting:"
    echo "   1. Check Wine installation: wine --version"
    echo "   2. Try manual build: npm run build-portable-win"
    echo "   3. Check error messages above"
    echo ""
    echo "🌐 Alternative: Use GitHub Actions for automated builds"
    echo "   Push your code to GitHub and the workflow will build both versions"
    echo ""
fi
