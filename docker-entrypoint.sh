#!/bin/sh

echo "🚀 Starting SHEGE SRS Web Application"
echo "====================================="

# Function to wait for MongoDB
wait_for_mongo() {
    echo "⏳ Waiting for MongoDB to be ready..."
    
    # If using external MongoDB, skip local startup
    if [ "$MONGODB_EXTERNAL" = "true" ]; then
        echo "📡 Using external MongoDB at: $MONGODB_URI"
        return 0
    fi
    
    # Start local MongoDB if needed
    if [ "$MONGODB_LOCAL" = "true" ]; then
        echo "🍃 Starting local MongoDB..."
        mongod --dbpath /app/data/db --logpath /app/logs/mongodb.log --fork
        
        # Wait for MongoDB to start
        for i in $(seq 1 30); do
            if mongosh --eval "db.runCommand('ping')" >/dev/null 2>&1; then
                echo "✅ MongoDB is ready"
                return 0
            fi
            echo "⏳ Waiting for MongoDB... ($i/30)"
            sleep 2
        done
        
        echo "❌ MongoDB failed to start"
        exit 1
    fi
}

# Function to setup database
setup_database() {
    echo "🔧 Setting up database..."
    
    # Create admin user if it doesn't exist
    node -e "
        const mongoose = require('mongoose');
        const bcrypt = require('bcrypt');
        
        async function setupAdmin() {
            try {
                await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/student-registration');
                
                const adminExists = await mongoose.connection.db.collection('admins').findOne({username: 'admin'});
                
                if (!adminExists) {
                    const hashedPassword = await bcrypt.hash('admin123', 10);
                    await mongoose.connection.db.collection('admins').insertOne({
                        username: 'admin',
                        password: hashedPassword,
                        role: 'administrator',
                        createdAt: new Date()
                    });
                    console.log('✅ Admin user created');
                } else {
                    console.log('✅ Admin user already exists');
                }
                
                await mongoose.disconnect();
            } catch (error) {
                console.error('❌ Database setup failed:', error.message);
                process.exit(1);
            }
        }
        
        setupAdmin();
    "
}

# Function to start the application
start_application() {
    echo "🌐 Starting SHEGE SRS web server..."
    
    # Set production environment
    export NODE_ENV=production
    export PORT=${PORT:-3000}
    
    # Start the application
    node src/app.js
}

# Main execution
echo "🔍 Checking environment..."

# Set default values
export MONGODB_URI=${MONGODB_URI:-"mongodb://localhost:27017/student-registration"}
export MONGODB_LOCAL=${MONGODB_LOCAL:-"true"}
export MONGODB_EXTERNAL=${MONGODB_EXTERNAL:-"false"}
export SYSTEM_NAME=${SYSTEM_NAME:-"SHEGE SRS"}
export SESSION_SECRET=${SESSION_SECRET:-"change-this-in-production"}

echo "📋 Configuration:"
echo "   MongoDB URI: $MONGODB_URI"
echo "   System Name: $SYSTEM_NAME"
echo "   Port: ${PORT:-3000}"
echo "   Environment: ${NODE_ENV:-production}"

# Wait for MongoDB
wait_for_mongo

# Setup database
setup_database

# Start application
start_application
