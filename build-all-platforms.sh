#!/bin/bash

echo "🚀 SHEGE SRS Multi-Platform Builder"
echo "=================================="
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to build Linux
build_linux() {
    echo "🐧 Building Linux AppImage..."
    npm run build-portable-linux
    if [ $? -eq 0 ]; then
        echo "✅ Linux build successful"
        return 0
    else
        echo "❌ Linux build failed"
        return 1
    fi
}

# Function to build Windows
build_windows() {
    echo "🪟 Building Windows executable..."
    
    if command_exists wine; then
        echo "🍷 Using Wine for Windows build..."
        export WINEARCH=win64
        npm run build-portable-win
    else
        echo "⚠️  Wine not found, trying direct build..."
        npm run build-portable-win
    fi
    
    if [ $? -eq 0 ]; then
        echo "✅ Windows build successful"
        return 0
    else
        echo "❌ Windows build failed"
        return 1
    fi
}

# Function to show build results
show_results() {
    echo ""
    echo "📦 Build Results:"
    echo "=================="
    
    if [ -d "dist" ]; then
        echo ""
        echo "📁 Files in dist/ directory:"
        ls -la dist/
        echo ""
        
        # Check for specific files
        if ls dist/*.AppImage 1> /dev/null 2>&1; then
            echo "✅ Linux AppImage: $(ls dist/*.AppImage)"
        else
            echo "❌ Linux AppImage: Not found"
        fi
        
        if ls dist/*.exe 1> /dev/null 2>&1; then
            echo "✅ Windows EXE: $(ls dist/*.exe)"
        else
            echo "❌ Windows EXE: Not found"
        fi
        
        echo ""
        echo "📊 File sizes:"
        du -h dist/* 2>/dev/null || echo "No files to measure"
        
    else
        echo "❌ No dist/ directory found"
    fi
}

# Function to create distribution package
create_distribution() {
    if [ -d "dist" ]; then
        echo ""
        echo "📦 Creating distribution package..."
        
        # Create distribution directory
        mkdir -p distribution
        
        # Copy files with better names
        if ls dist/*.AppImage 1> /dev/null 2>&1; then
            cp dist/*.AppImage distribution/SHEGE-SRS-Linux.AppImage
            echo "✅ Linux: distribution/SHEGE-SRS-Linux.AppImage"
        fi
        
        if ls dist/*.exe 1> /dev/null 2>&1; then
            cp dist/*.exe distribution/SHEGE-SRS-Windows.exe
            echo "✅ Windows: distribution/SHEGE-SRS-Windows.exe"
        fi
        
        # Create README
        cat > distribution/README.txt << EOF
SHEGE SRS Portable Applications
==============================

This package contains portable versions of SHEGE SRS for different operating systems.

Files:
- SHEGE-SRS-Linux.AppImage: For Linux systems (Ubuntu, CentOS, Debian, etc.)
- SHEGE-SRS-Windows.exe: For Windows systems (Windows 10, 11)

Prerequisites:
- MongoDB must be installed and running on the target system
- Port 3000 must be available

Installation:
1. Download the appropriate file for your operating system
2. Linux: chmod +x SHEGE-SRS-Linux.AppImage && ./SHEGE-SRS-Linux.AppImage
3. Windows: Double-click SHEGE-SRS-Windows.exe

First Login:
- Username: admin
- Password: admin123
- Change password immediately after first login

Support:
- Check that MongoDB is running: sudo systemctl status mongod (Linux)
- Ensure port 3000 is available: netstat -tlnp | grep :3000
- Application loads at: http://localhost:3000

Built on: $(date)
Version: 1.0.0
EOF
        
        echo "✅ Distribution package created in distribution/ directory"
    fi
}

# Main execution
echo "🔍 Checking prerequisites..."

# Check Node.js
if command_exists node; then
    echo "✅ Node.js: $(node --version)"
else
    echo "❌ Node.js not found. Please install Node.js 16+"
    exit 1
fi

# Check npm
if command_exists npm; then
    echo "✅ npm: $(npm --version)"
else
    echo "❌ npm not found"
    exit 1
fi

# Check if in correct directory
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found. Please run from project directory."
    exit 1
fi

echo "✅ Prerequisites check passed"
echo ""

# Install dependencies
echo "📦 Installing dependencies..."
npm install
echo ""

# Create .env if needed
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file..."
    cat > .env << EOF
SYSTEM_NAME=SHEGE SRS
MONGODB_URI=mongodb://localhost:27017/student-registration
PORT=3000
NODE_ENV=production
SESSION_SECRET=change-this-secret-key
EOF
    echo "✅ .env file created"
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf dist/ distribution/

# Build platforms
echo ""
echo "🏗️  Starting builds..."
echo ""

LINUX_SUCCESS=0
WINDOWS_SUCCESS=0

# Build Linux
build_linux
LINUX_SUCCESS=$?

echo ""

# Build Windows
build_windows
WINDOWS_SUCCESS=$?

# Show results
show_results

# Create distribution if any builds succeeded
if [ $LINUX_SUCCESS -eq 0 ] || [ $WINDOWS_SUCCESS -eq 0 ]; then
    create_distribution
fi

echo ""
echo "🎯 Build Summary:"
echo "================="

if [ $LINUX_SUCCESS -eq 0 ]; then
    echo "✅ Linux AppImage: SUCCESS"
else
    echo "❌ Linux AppImage: FAILED"
fi

if [ $WINDOWS_SUCCESS -eq 0 ]; then
    echo "✅ Windows EXE: SUCCESS"
else
    echo "❌ Windows EXE: FAILED"
    echo "💡 To build Windows on Linux:"
    echo "   1. Install Wine: sudo apt install wine"
    echo "   2. Or use GitHub Actions for automated builds"
    echo "   3. Or build on a Windows machine"
fi

echo ""

if [ $LINUX_SUCCESS -eq 0 ] || [ $WINDOWS_SUCCESS -eq 0 ]; then
    echo "🎉 Build completed! Check the distribution/ directory for final files."
else
    echo "❌ All builds failed. Check error messages above."
    exit 1
fi
