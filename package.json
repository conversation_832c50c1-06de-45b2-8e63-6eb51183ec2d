{"name": "student-registration-app", "version": "1.0.0", "description": "A student registration system using Express.js, EJS, and MongoDB.", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "setup-admin": "node src/scripts/createAdmin.js"}, "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "connect-flash": "^0.1.1", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.19.2", "express-session": "^1.18.0", "method-override": "^3.0.0", "mongoose": "^8.4.1", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.1.0"}, "keywords": ["student", "registration", "express", "mongodb", "ejs"], "author": "Your Name", "license": "ISC"}