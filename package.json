{"name": "shege-srs-desktop", "version": "1.0.0", "description": "SHEGE SRS - Student Registration System Desktop Application", "main": "electron-main.js", "homepage": "./", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "setup-admin": "node src/scripts/createAdmin.js", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "electron-debug": "electron electron-debug.js", "build": "electron-builder", "build-win": "electron-builder --win", "build-linux": "electron-builder --linux", "build-all": "electron-builder --win --linux", "build-portable-win": "node scripts/build-simple.js windows", "build-portable-linux": "node scripts/build-simple.js linux", "build-portable-all": "node scripts/build-simple.js all", "build-portable-advanced": "node scripts/build-portable.js all", "build-web": "node scripts/build-web-distribution.js", "check-deps": "node scripts/check-dependencies.js", "dist": "npm run build-all", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "connect-flash": "^0.1.1", "csv-writer": "^1.6.0", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.19.2", "express-session": "^1.18.0", "method-override": "^3.0.0", "mongoose": "^8.4.1", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.1.0", "electron": "^28.0.0", "electron-builder": "^24.9.1", "electron-reload": "^2.0.0-alpha.1", "concurrently": "^8.2.2", "wait-on": "^7.2.0"}, "build": {"appId": "com.shegesrs.desktop", "productName": "SHEGE SRS", "directories": {"output": "dist"}, "asar": false, "files": ["src/**/*", "electron-main.js", "electron-preload.js", "package.json", ".env", ".env.example", "scripts/**/*", "node_modules/**/*", "!node_modules/.cache/**/*", "!node_modules/.bin/**/*", "!**/*.md", "!docs/**/*"], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}, {"target": "zip", "arch": ["x64", "ia32"]}], "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}, {"target": "tar.gz", "arch": ["x64"]}], "category": "Education", "artifactName": "${productName}-${version}-${arch}.${ext}", "desktop": {"Name": "SHEGE SRS", "Comment": "Student Registration System", "Categories": "Education;Office;"}}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "SHEGE SRS"}, "publish": null}, "keywords": ["student", "registration", "express", "mongodb", "ejs"], "author": "Your Name", "license": "ISC"}