{"name": "shege-srs-desktop", "version": "1.0.0", "description": "SHEGE SRS - Student Registration System Desktop Application", "main": "electron-main.js", "homepage": "./", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "setup-admin": "node src/scripts/createAdmin.js", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "build": "electron-builder", "build-win": "electron-builder --win", "build-linux": "electron-builder --linux", "build-all": "electron-builder --win --linux", "dist": "npm run build-all", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "connect-flash": "^0.1.1", "csv-writer": "^1.6.0", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.19.2", "express-session": "^1.18.0", "method-override": "^3.0.0", "mongoose": "^8.4.1", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.1.0", "electron": "^28.0.0", "electron-builder": "^24.9.1", "electron-reload": "^2.0.0-alpha.1", "concurrently": "^8.2.2", "wait-on": "^7.2.0"}, "build": {"appId": "com.shegesrs.desktop", "productName": "SHEGE SRS", "directories": {"output": "dist"}, "files": ["src/**/*", "electron-main.js", "electron-preload.js", "package.json", "node_modules/**/*", "!node_modules/.cache/**/*"], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Education"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "SHEGE SRS"}, "publish": null}, "keywords": ["student", "registration", "express", "mongodb", "ejs"], "author": "Your Name", "license": "ISC"}