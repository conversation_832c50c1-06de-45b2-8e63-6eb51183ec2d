# SHEGE SRS Portable Application Troubleshooting

## "A JavaScript error occurred in the main process" - SOLVED

This error typically occurs when the portable application cannot find required files or dependencies. Here's how to fix it:

## Immediate Solutions

### Solution 1: Use Debug Mode
```bash
# Run debug version to see detailed error information
npm run electron-debug
```

This will:
- Show detailed system information
- Test MongoDB connectivity
- Check file system access
- Provide clear error messages

### Solution 2: Rebuild with Fixed Configuration
```bash
# Clean previous builds
rm -rf dist/

# Rebuild with enhanced error handling
npm run build-portable-all
```

### Solution 3: Check Prerequisites
```bash
# Verify all dependencies
npm run check-deps

# Ensure MongoDB is running
sudo systemctl start mongod  # Linux
# or start MongoDB service on Windows
```

## Root Causes and Fixes

### 1. Missing Files in Portable Build
**Problem**: Required files not included in portable package
**Fix**: Updated build configuration to include all necessary files

**Files now included:**
- ✅ `src/**/*` - All application source files
- ✅ `electron-main.js` - Main process file
- ✅ `electron-preload.js` - Preload script
- ✅ `.env` - Environment configuration
- ✅ `node_modules/**/*` - All dependencies

### 2. Incorrect File Paths
**Problem**: Hardcoded paths don't work in portable builds
**Fix**: Dynamic path resolution based on execution context

```javascript
// Old (broken in portable):
const serverScript = path.join(__dirname, 'src', 'app.js');

// New (works in portable):
const appPath = process.resourcesPath ? 
    path.join(process.resourcesPath, 'app') : 
    __dirname;
const serverScript = path.join(appPath, 'src', 'app.js');
```

### 3. Missing Error Handling
**Problem**: Errors not properly caught and displayed
**Fix**: Comprehensive error handling with user-friendly messages

### 4. Preload Script Issues
**Problem**: Preload script fails in portable environment
**Fix**: Fallback API when contextBridge is unavailable

## Testing Your Build

### Step 1: Run Debug Mode
```bash
npm run electron-debug
```

This opens a debug window that shows:
- System information
- File system status
- MongoDB connectivity
- Server startup status

### Step 2: Check Build Output
```bash
# After building, check what was included
ls -la dist/

# Extract and examine portable build (Linux)
mkdir test-extract
cd test-extract
/path/to/SHEGE*.AppImage --appimage-extract
ls -la squashfs-root/

# For Windows, use 7-zip or similar to extract the .exe
```

### Step 3: Test on Clean System
Test the portable application on a system that doesn't have:
- Node.js development environment
- Source code
- Development dependencies

## Common Error Messages and Solutions

### "Cannot find module 'electron'"
**Cause**: Missing electron in portable build
**Solution**: Ensure electron is in dependencies, not devDependencies

### "ENOENT: no such file or directory"
**Cause**: File path resolution issue
**Solution**: Use the updated path resolution in electron-main.js

### "spawn ENOENT"
**Cause**: Cannot find Node.js executable
**Solution**: Ensure Node.js is installed on target system

### "MongoDB connection failed"
**Cause**: MongoDB not running or not installed
**Solution**: Install and start MongoDB service

## Build Verification Checklist

Before distributing your portable application:

- [ ] **Build completes without errors**
- [ ] **Debug mode shows no critical issues**
- [ ] **All required files are included in build**
- [ ] **Application starts without JavaScript errors**
- [ ] **MongoDB connectivity works**
- [ ] **Server starts successfully**
- [ ] **Web interface loads correctly**
- [ ] **Basic functionality works (login, navigation)**

## Advanced Debugging

### Enable Verbose Logging
```bash
# Set environment variable for detailed logs
DEBUG=* npm run electron-debug
```

### Check Electron Console
1. Run the portable application
2. If it starts, press `Ctrl+Shift+I` to open DevTools
3. Check Console tab for JavaScript errors
4. Check Network tab for failed requests

### Manual File Check
```bash
# Extract portable app and check contents
# Linux AppImage:
./SHEGE*.AppImage --appimage-extract
find squashfs-root -name "*.js" | head -20

# Windows (use 7-zip):
# Extract .exe file and check contents
```

## Creating a Support Package

If you still have issues, create a support package:

```bash
# 1. Run debug mode and save output
npm run electron-debug > debug-output.txt 2>&1

# 2. Check dependencies
npm run check-deps > deps-check.txt 2>&1

# 3. List build contents
ls -la dist/ > build-contents.txt

# 4. System information
uname -a > system-info.txt
node --version >> system-info.txt
npm --version >> system-info.txt
```

## Prevention for Future Builds

### 1. Always Test Debug Mode First
```bash
npm run electron-debug
```

### 2. Use Dependency Checker
```bash
npm run check-deps
```

### 3. Test on Multiple Systems
- Development machine
- Clean virtual machine
- Different operating systems

### 4. Validate Build Contents
```bash
# Check that all required files are included
npm run build-portable-all
ls -la dist/
```

## Quick Recovery Steps

If your portable app is broken:

1. **Delete dist folder**: `rm -rf dist/`
2. **Clean node_modules**: `rm -rf node_modules && npm install`
3. **Check dependencies**: `npm run check-deps`
4. **Test debug mode**: `npm run electron-debug`
5. **Rebuild**: `npm run build-portable-all`
6. **Test immediately**: Run the new portable app

## Getting Help

If you're still experiencing issues:

1. **Run debug mode** and note any error messages
2. **Check the console output** for specific errors
3. **Verify MongoDB is running** on the target system
4. **Test on a clean system** without development tools
5. **Contact support** with specific error messages and system information

## Success Indicators

Your portable application is working correctly when:

- ✅ No JavaScript errors on startup
- ✅ Debug mode shows all green checkmarks
- ✅ Application window opens without errors
- ✅ Server starts and connects to MongoDB
- ✅ Web interface loads at http://localhost:3000
- ✅ Login page appears and functions correctly

---

**Remember**: The portable application still requires MongoDB to be installed and running on the target system. This is not included in the portable build and must be installed separately.
