const { app, BrowserWindow, Menu, shell, dialog, ipcMain } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';
const { spawn } = require('child_process');
const fs = require('fs');
const net = require('net');

// Enhanced error handling for main process
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception in Main Process:', error);
    dialog.showErrorBox('Application Error', `An unexpected error occurred:\n\n${error.message}\n\nStack trace:\n${error.stack}`);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection in Main Process:', reason);
    dialog.showErrorBox('Application Error', `An unexpected error occurred:\n\n${reason}`);
});

// Keep a global reference of the window object
let mainWindow;
let serverProcess;

// Determine if running as portable app and handle ASAR packaging
const isPortable = process.env.PORTABLE_EXECUTABLE_DIR || process.resourcesPath;
let appPath;

if (isPortable && process.resourcesPath) {
    // In packaged app, check if using ASAR
    const asarPath = path.join(process.resourcesPath, 'app.asar');
    const unpackedPath = path.join(process.resourcesPath, 'app');

    if (fs.existsSync(asarPath)) {
        // Files are in ASAR archive, use the ASAR path
        appPath = asarPath;
        console.log('Using ASAR packaged files');
    } else if (fs.existsSync(unpackedPath)) {
        // Files are unpacked
        appPath = unpackedPath;
        console.log('Using unpacked files');
    } else {
        // Fallback to resources path
        appPath = process.resourcesPath;
        console.log('Using resources path fallback');
    }
} else {
    // Development mode
    appPath = __dirname;
    console.log('Using development path');
}

console.log('App running from:', appPath);
console.log('Is portable:', !!isPortable);
console.log('Resources path:', process.resourcesPath);
console.log('Current working directory:', process.cwd());

// Enable live reload for Electron in development
if (isDev) {
    require('electron-reload')(__dirname, {
        electron: path.join(__dirname, 'node_modules', '.bin', 'electron'),
        hardResetMethod: 'exit'
    });
}

async function createWindow() {
    try {
        // Determine correct paths for portable builds
        let preloadPath, iconPath;

        if (appPath.endsWith('.asar')) {
            // Files are in ASAR, use ASAR paths
            preloadPath = path.join(appPath, 'electron-preload.js');
            iconPath = path.join(appPath, 'assets', 'icon.png');
        } else {
            // Files are unpacked or in development
            preloadPath = path.join(appPath, 'electron-preload.js');
            iconPath = path.join(appPath, 'assets', 'icon.png');
        }

        console.log('Preload script path:', preloadPath);
        console.log('Icon path:', iconPath);

        // For ASAR files, we can't use fs.existsSync, so we'll try to load them
        let preloadExists = true;
        try {
            if (!appPath.endsWith('.asar')) {
                preloadExists = fs.existsSync(preloadPath);
            }
        } catch (e) {
            console.warn('Could not check preload script existence:', e.message);
            preloadExists = false;
        }

        if (!preloadExists) {
            console.warn('Preload script not found at:', preloadPath);
        }

        // Create the browser window
        mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: fs.existsSync(preloadPath) ? preloadPath : undefined
            },
            icon: fs.existsSync(iconPath) ? iconPath : undefined,
            show: false, // Don't show until ready
            titleBarStyle: 'default',
            autoHideMenuBar: false
        });

        console.log('Browser window created successfully');
    } catch (error) {
        console.error('Error creating browser window:', error);
        dialog.showErrorBox('Window Creation Error', `Failed to create application window:\n\n${error.message}`);
        throw error;
    }

    // Show loading screen
    mainWindow.loadURL(`data:text/html;charset=utf-8,
        <html>
            <head>
                <title>SHEGE SRS - Loading</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 100vh;
                        margin: 0;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                    }
                    .loading { text-align: center; }
                    .spinner {
                        border: 4px solid rgba(255,255,255,0.3);
                        border-radius: 50%;
                        border-top: 4px solid white;
                        width: 40px;
                        height: 40px;
                        animation: spin 1s linear infinite;
                        margin: 20px auto;
                    }
                    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
                </style>
            </head>
            <body>
                <div class="loading">
                    <h1>SHEGE SRS</h1>
                    <div class="spinner"></div>
                    <p>Starting application server...</p>
                </div>
            </body>
        </html>
    `);

    mainWindow.show();

    try {
        // Start the Express server and wait for it
        await startServer();

        // Load the actual application
        const startUrl = 'http://localhost:3000';
        await mainWindow.loadURL(startUrl);

        // Open DevTools in development
        if (isDev) {
            mainWindow.webContents.openDevTools();
        }

    } catch (error) {
        console.error('Failed to start application:', error);

        // Show error page
        mainWindow.loadURL(`data:text/html;charset=utf-8,
            <html>
                <head>
                    <title>SHEGE SRS - Error</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 100vh;
                            margin: 0;
                            background: #f8f9fa;
                            color: #333;
                        }
                        .error { text-align: center; max-width: 500px; }
                        .error h1 { color: #dc3545; }
                        .error button {
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 5px;
                            cursor: pointer;
                            margin: 10px;
                        }
                    </style>
                </head>
                <body>
                    <div class="error">
                        <h1>Application Error</h1>
                        <p>Failed to start the SHEGE SRS server.</p>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Please ensure MongoDB is running and try again.</p>
                        <button onclick="location.reload()">Retry</button>
                        <button onclick="require('electron').remote.app.quit()">Exit</button>
                    </div>
                </body>
            </html>
        `);
    }

    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
        stopServer();
    });

    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    // Create application menu
    createMenu();
}

function startServer() {
    return new Promise((resolve, reject) => {
        try {
            // Check if server is already running
            const net = require('net');
            const testConnection = net.createConnection({ port: 3000, host: 'localhost' });

            testConnection.on('connect', () => {
                console.log('Server already running on port 3000');
                testConnection.end();
                resolve();
            });

            testConnection.on('error', () => {
                // Server not running, start it
                console.log('Starting Express server...');

                let serverScript;
                if (appPath.endsWith('.asar')) {
                    // Files are in ASAR archive
                    serverScript = path.join(appPath, 'src', 'app.js');
                } else {
                    // Files are unpacked or in development
                    serverScript = path.join(appPath, 'src', 'app.js');
                }

                console.log('Looking for server script at:', serverScript);

                // For ASAR files, we can't use fs.existsSync reliably
                let serverExists = true;
                if (!appPath.endsWith('.asar')) {
                    serverExists = fs.existsSync(serverScript);
                    if (!serverExists) {
                        console.error('Server script not found at:', serverScript);
                        console.log('Available files in app directory:');
                        try {
                            const files = fs.readdirSync(appPath);
                            console.log(files);
                            if (fs.existsSync(path.join(appPath, 'src'))) {
                                const srcFiles = fs.readdirSync(path.join(appPath, 'src'));
                                console.log('Files in src directory:', srcFiles);
                            }
                        } catch (e) {
                            console.error('Error listing files:', e);
                        }
                        reject(new Error(`Server script not found: ${serverScript}`));
                        return;
                    }
                } else {
                    console.log('Using ASAR packaged server script');
                }

                console.log('Starting server process...');
                serverProcess = spawn('node', [serverScript], {
                    cwd: appPath,
                    env: {
                        ...process.env,
                        NODE_ENV: 'production',
                        PORT: '3000',
                        MONGODB_URI: 'mongodb://localhost:27017/student-registration'
                    },
                    stdio: isDev ? 'inherit' : ['pipe', 'pipe', 'pipe']
                });

                console.log('Server process spawned with PID:', serverProcess.pid);

                let serverStarted = false;

                // Monitor server output
                if (serverProcess.stdout) {
                    serverProcess.stdout.on('data', (data) => {
                        const output = data.toString();
                        console.log('Server:', output);

                        if (output.includes('Server running on') || output.includes('listening on')) {
                            if (!serverStarted) {
                                serverStarted = true;
                                resolve();
                            }
                        }
                    });
                }

                if (serverProcess.stderr) {
                    serverProcess.stderr.on('data', (data) => {
                        console.error('Server Error:', data.toString());
                    });
                }

                serverProcess.on('error', (err) => {
                    console.error('Failed to start server:', err);
                    reject(err);
                });

                serverProcess.on('exit', (code) => {
                    console.log(`Server process exited with code ${code}`);
                    if (code !== 0 && !serverStarted) {
                        reject(new Error(`Server exited with code ${code}`));
                    }
                    // Don't close the app if server exits after starting
                    if (serverStarted && mainWindow && !mainWindow.isDestroyed()) {
                        console.log('Server stopped unexpectedly, showing error page');
                        mainWindow.loadURL(`data:text/html;charset=utf-8,
                            <html>
                                <head><title>Server Stopped</title></head>
                                <body style="font-family: Arial; text-align: center; padding: 50px;">
                                    <h1>Server Stopped</h1>
                                    <p>The application server has stopped unexpectedly.</p>
                                    <button onclick="location.reload()">Restart Application</button>
                                </body>
                            </html>
                        `);
                    }
                });

                // Timeout after 10 seconds
                setTimeout(() => {
                    if (!serverStarted) {
                        reject(new Error('Server startup timeout'));
                    }
                }, 10000);

                console.log('Server started with PID:', serverProcess.pid);
            });

        } catch (error) {
            console.error('Error starting server:', error);
            reject(error);
        }
    });
}

function stopServer() {
    if (serverProcess) {
        serverProcess.kill();
        serverProcess = null;
        console.log('Server stopped');
    }
}

function createMenu() {
    const template = [
        {
            label: 'File',
            submenu: [
                {
                    label: 'New Student',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        mainWindow.loadURL('http://localhost:3000/register');
                    }
                },
                {
                    label: 'Dashboard',
                    accelerator: 'CmdOrCtrl+D',
                    click: () => {
                        mainWindow.loadURL('http://localhost:3000/dashboard');
                    }
                },
                {
                    label: 'Students List',
                    accelerator: 'CmdOrCtrl+L',
                    click: () => {
                        mainWindow.loadURL('http://localhost:3000/students');
                    }
                },
                { type: 'separator' },
                {
                    label: 'Exit',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'Edit',
            submenu: [
                { role: 'undo' },
                { role: 'redo' },
                { type: 'separator' },
                { role: 'cut' },
                { role: 'copy' },
                { role: 'paste' },
                { role: 'selectall' }
            ]
        },
        {
            label: 'View',
            submenu: [
                { role: 'reload' },
                { role: 'forceReload' },
                { role: 'toggleDevTools' },
                { type: 'separator' },
                { role: 'resetZoom' },
                { role: 'zoomIn' },
                { role: 'zoomOut' },
                { type: 'separator' },
                { role: 'togglefullscreen' }
            ]
        },
        {
            label: 'Window',
            submenu: [
                { role: 'minimize' },
                { role: 'close' }
            ]
        },
        {
            label: 'Help',
            submenu: [
                {
                    label: 'About SHEGE SRS',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'About SHEGE SRS',
                            message: 'SHEGE SRS - Student Registration System',
                            detail: 'Version 1.0.0\nA comprehensive student management system\nBuilt with Node.js, Express, and Electron'
                        });
                    }
                },
                {
                    label: 'System Information',
                    click: () => {
                        const info = `
Platform: ${process.platform}
Architecture: ${process.arch}
Electron Version: ${process.versions.electron}
Node.js Version: ${process.versions.node}
Chrome Version: ${process.versions.chrome}
                        `.trim();
                        
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'System Information',
                            message: 'System Details',
                            detail: info
                        });
                    }
                }
            ]
        }
    ];

    // macOS specific menu adjustments
    if (process.platform === 'darwin') {
        template.unshift({
            label: app.getName(),
            submenu: [
                { role: 'about' },
                { type: 'separator' },
                { role: 'services' },
                { type: 'separator' },
                { role: 'hide' },
                { role: 'hideothers' },
                { role: 'unhide' },
                { type: 'separator' },
                { role: 'quit' }
            ]
        });

        // Window menu
        template[4].submenu = [
            { role: 'close' },
            { role: 'minimize' },
            { role: 'zoom' },
            { type: 'separator' },
            { role: 'front' }
        ];
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    stopServer();
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

app.on('before-quit', () => {
    stopServer();
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        shell.openExternal(navigationUrl);
    });
});

// Handle certificate errors
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
    if (url.startsWith('http://localhost')) {
        // Allow localhost certificates
        event.preventDefault();
        callback(true);
    } else {
        callback(false);
    }
});
