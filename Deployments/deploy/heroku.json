{"name": "SHEGE SRS", "description": "Student Registration System - Web Application", "keywords": ["nodejs", "express", "mongodb", "education", "student-management"], "website": "https://github.com/yourusername/shege-srs", "repository": "https://github.com/yourusername/shege-srs", "logo": "https://your-domain.com/logo.png", "success_url": "/", "scripts": {"postdeploy": "node scripts/setup-production.js"}, "env": {"NODE_ENV": {"description": "Node.js environment", "value": "production"}, "SYSTEM_NAME": {"description": "System name displayed in the application", "value": "SHEGE SRS"}, "SESSION_SECRET": {"description": "Secret key for session management", "generator": "secret"}, "MONGODB_URI": {"description": "MongoDB connection string (use MongoDB Atlas)", "required": true}, "PORT": {"description": "Port number for the application", "value": "3000"}}, "formation": {"web": {"quantity": 1, "size": "basic"}}, "addons": [{"plan": "mongolab:sandbox", "as": "MONGODB"}], "buildpacks": [{"url": "hero<PERSON>/nodejs"}]}