# Multi-stage build for production web deployment
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/
COPY .env.example ./.env
COPY electron-main.js ./
COPY electron-preload.js ./

# Create production build directory
RUN mkdir -p /app/build && \
    cp -r src/ /app/build/ && \
    cp package*.json /app/build/ && \
    cp .env /app/build/

# Production stage
FROM node:18-alpine AS production

# Install MongoDB (optional - can use external DB)
RUN apk add --no-cache mongodb mongodb-tools

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Set working directory
WORKDIR /app

# Copy built application
COPY --from=builder /app/build ./
COPY --from=builder /app/node_modules ./node_modules

# Create necessary directories
RUN mkdir -p logs uploads data/db && \
    chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start script
COPY --chown=nodejs:nodejs docker-entrypoint.sh /app/
RUN chmod +x /app/docker-entrypoint.sh

# Start the application
CMD ["/app/docker-entrypoint.sh"]
