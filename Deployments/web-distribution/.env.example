# SHEGE SRS Web Application Configuration
# Copy this file to .env and configure for your environment

# System Configuration
SYSTEM_NAME=SHEGE SRS
SYSTEM_FULL_NAME=Student Registration System
NODE_ENV=production
PORT=3000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/student-registration
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/student-registration

# Security Configuration
SESSION_SECRET=change-this-to-a-secure-random-string
SESSION_TIMEOUT=3600000

# School Information
SCHOOL_NAME=Your School Name
SCHOOL_ADDRESS=Your School Address
SCHOOL_PHONE=******-567-8900
SCHOOL_EMAIL=<EMAIL>

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_PRINT_FEATURES=true
ENABLE_EXPORT_FEATURES=true

# File Upload Configuration
MAX_FILE_SIZE=5MB

# Admin Configuration (for initial setup)
ADMIN_PASSWORD=admin123
