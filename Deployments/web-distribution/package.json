{"name": "shege-srs-web", "version": "1.0.0", "description": "SHEGE SRS - Student Registration System (Web Version)", "main": "src/app.js", "scripts": {"start": "node src/app.js", "setup": "node scripts/setup-production.js", "health": "curl -f http://localhost:3000/health || exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "connect-flash": "^0.1.1", "csv-writer": "^1.6.0", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.19.2", "express-session": "^1.18.0", "method-override": "^3.0.0", "mongoose": "^8.4.1", "multer": "^1.4.5-lts.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["student-management", "education", "registration", "web-app"], "author": "Your Name", "license": "ISC"}