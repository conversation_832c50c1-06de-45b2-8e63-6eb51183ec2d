<%- include('../partials/header', { title: student ? 'Edit Student' : 'Register New Student' }) %>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><%= student ? 'Edit Student' : 'Register New Student' %></h1>
        <a href="/students" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    <% if (error_msg) { %>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <%= error_msg %>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <% } %>

    <form action="<%= student ? `/students/${student._id}?_method=PUT` : '/students' %>" method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
        <!-- Student Photo -->
        <%- include('partials/photo-upload') %>
        
        <!-- Personal Information -->
        <%- include('partials/personal-info') %>
        
        <!-- Academic Information -->
        <%- include('partials/academic-info') %>
        
        <!-- Parent/Guardian Information -->
        <%- include('partials/parent-info') %>
        
        <!-- Additional Information -->
        <%- include('partials/additional-info') %>
    </form>
    
    <!-- Include form validation and interactivity script -->
    <script src="/js/student-form.js"></script>
</div>

<%- include('../partials/footer') %>

<script>
    function previewImage(input, previewId, placeholderId) {
        const preview = document.getElementById(previewId);
        const placeholder = document.getElementById(placeholderId);

        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.classList.remove('hidden');
                placeholder.classList.add('hidden');
            };

            reader.readAsDataURL(input.files[0]);
        } else {
            preview.src = '';
            preview.classList.add('hidden');
            placeholder.classList.remove('hidden');
        }
    }
</script>
