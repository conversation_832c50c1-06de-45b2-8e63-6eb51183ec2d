<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students - <%= systemName || 'SHEGE SRS' %></title>
    <link rel="stylesheet" href="/styles-enhanced.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">
                <h1><i class="fas fa-graduation-cap"></i> SHEGE SRS</h1>
                <p class="logo-subtitle">Student Registration System</p>
            </div>
            <nav>
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/register"><i class="fas fa-user-plus"></i> Register Student</a>
                <a href="/students"><i class="fas fa-users"></i> View Students</a>
                <% if (typeof username !== 'undefined' && username) { %>
                    <span class="user-info">Welcome, <%= username %>!</span>
                    <a href="/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
                <% } else { %>
                    <a href="/login"><i class="fas fa-sign-in-alt"></i> Login</a>
                <% } %>
            </nav>
        </div>
    </header>
    <main>
        <div class="container">
            <% if (success_msg && success_msg.length > 0) { %>
                <div class="flash-success"><i class="fas fa-check-circle"></i> <%= success_msg %></div>
            <% } %>
            <% if (error_msg && error_msg.length > 0) { %>
                <div class="flash-error"><i class="fas fa-exclamation-circle"></i> <%= error_msg %></div>
            <% } %>

            <div class="page-header" style="overflow: visible; position: relative; z-index: 10;">
                <div class="page-title">
                    <h1><i class="fas fa-users"></i> Students Management</h1>
                    <p class="page-subtitle">Manage and organize student information</p>
                </div>
                <div class="page-actions" style="position: relative; z-index: 100; overflow: visible;">
                    <a href="/dashboard" class="btn btn-secondary">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="/register" class="btn">
                        <i class="fas fa-plus"></i> Register New Student
                    </a>
                    <div class="dropdown" style="position: relative; z-index: 1000;">
                        <button class="btn btn-secondary" onclick="toggleDropdown('exportDropdown')">
                            <i class="fas fa-download"></i> Export & Print
                        </button>
                        <div id="exportDropdown" class="dropdown-menu" style="display: none;">
                            <!-- CSV Export Options -->
                            <div class="dropdown-section">
                                <div class="dropdown-header">
                                    <i class="fas fa-file-csv"></i> CSV Export Options
                                </div>
                                <a href="#" onclick="exportToCSV()" class="dropdown-item">
                                    <i class="fas fa-download"></i> Export Current View
                                </a>
                                <a href="#" onclick="exportSelectedStudents()" class="dropdown-item">
                                    <i class="fas fa-check-square"></i> Export Selected Students
                                </a>
                                <a href="#" onclick="showExportModal()" class="dropdown-item">
                                    <i class="fas fa-filter"></i> Export by Filters
                                </a>
                            </div>

                            <!-- Print Options -->
                            <div class="dropdown-section">
                                <div class="dropdown-header">
                                    <i class="fas fa-print"></i> Print Options
                                </div>
                                <button onclick="printSelectedIDCards()" class="dropdown-item">
                                    <i class="fas fa-id-card"></i> Print Selected ID Cards
                                </button>
                                <button onclick="printAllIDCards()" class="dropdown-item">
                                    <i class="fas fa-id-badge"></i> Print All ID Cards
                                </button>
                                <button onclick="printSelectedDocuments()" class="dropdown-item">
                                    <i class="fas fa-file-alt"></i> Print Selected Documents
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats Summary -->
            <div class="quick-stats">
                <div class="quick-stat-item">
                    <div class="quick-stat-number" style="color: var(--primary-color);"><%= students.length %></div>
                    <div class="quick-stat-label">Total Students</div>
                </div>
                <div class="quick-stat-item">
                    <div class="quick-stat-number" style="color: var(--success-color);"><%= students.filter(s => s.sex === 'Male').length %></div>
                    <div class="quick-stat-label">Male</div>
                </div>
                <div class="quick-stat-item">
                    <div class="quick-stat-number" style="color: #e91e63;"><%= students.filter(s => s.sex === 'Female').length %></div>
                    <div class="quick-stat-label">Female</div>
                </div>
                <div class="quick-stat-item">
                    <div class="quick-stat-number" style="color: var(--warning-color);"><%= students.filter(s => s.quarter === 'Quarter 1').length %></div>
                    <div class="quick-stat-label">Q1</div>
                </div>
                <div class="quick-stat-item">
                    <div class="quick-stat-number" style="color: #9b59b6;"><%= students.filter(s => s.quarter === 'Quarter 2').length %></div>
                    <div class="quick-stat-label">Q2</div>
                </div>
                <div class="quick-stat-item">
                    <div class="quick-stat-number" style="color: var(--info-color);"><%= students.filter(s => s.quarter === 'Quarter 3').length %></div>
                    <div class="quick-stat-label">Q3</div>
                </div>
                <div class="quick-stat-item">
                    <div class="quick-stat-number" style="color: var(--error-color);"><%= students.filter(s => s.quarter === 'Quarter 4').length %></div>
                    <div class="quick-stat-label">Q4</div>
                </div>
            </div>

            <form method="get" action="/students" class="filter-form">
                <label>
                    <i class="fas fa-user"></i> Name
                    <input type="text" name="name" value="<%= typeof name !== 'undefined' ? name : '' %>" placeholder="Search by name">
                </label>
                <label>
                    <i class="fas fa-layer-group"></i> Grade
                    <input type="text" name="grade" value="<%= typeof grade !== 'undefined' ? grade : '' %>" placeholder="Enter grade">
                </label>
                <label>
                    <i class="fas fa-users"></i> Section
                    <input type="text" name="section" value="<%= typeof section !== 'undefined' ? section : '' %>" placeholder="Enter section">
                </label>
                <label>
                    <i class="fas fa-id-card"></i> Student ID
                    <input type="text" name="studentId" value="<%= typeof studentId !== 'undefined' ? studentId : '' %>" placeholder="Enter student ID">
                </label>
                <label>
                    <i class="fas fa-calendar-check"></i> Quarter
                    <select name="quarter">
                        <option value="">All Quarters</option>
                        <option value="Quarter 1" <%= typeof quarter !== 'undefined' && quarter === 'Quarter 1' ? 'selected' : '' %>>Quarter 1</option>
                        <option value="Quarter 2" <%= typeof quarter !== 'undefined' && quarter === 'Quarter 2' ? 'selected' : '' %>>Quarter 2</option>
                        <option value="Quarter 3" <%= typeof quarter !== 'undefined' && quarter === 'Quarter 3' ? 'selected' : '' %>>Quarter 3</option>
                        <option value="Quarter 4" <%= typeof quarter !== 'undefined' && quarter === 'Quarter 4' ? 'selected' : '' %>>Quarter 4</option>
                    </select>
                </label>
                <label>
                    <i class="fas fa-venus-mars"></i> Sex
                    <select name="sex">
                        <option value="">All</option>
                        <option value="Male" <%= typeof sex !== 'undefined' && sex === 'Male' ? 'selected' : '' %>>Male</option>
                        <option value="Female" <%= typeof sex !== 'undefined' && sex === 'Female' ? 'selected' : '' %>>Female</option>
                    </select>
                </label>
                <label>
                    <i class="fas fa-flag"></i> Status
                    <select name="status">
                        <option value="">Active Only</option>
                        <option value="active" <%= typeof status !== 'undefined' && status === 'active' ? 'selected' : '' %>>Active</option>
                        <option value="pending" <%= typeof status !== 'undefined' && status === 'pending' ? 'selected' : '' %>>Pending</option>
                        <option value="graduated" <%= typeof status !== 'undefined' && status === 'graduated' ? 'selected' : '' %>>Graduated</option>
                        <option value="transferred" <%= typeof status !== 'undefined' && status === 'transferred' ? 'selected' : '' %>>Transferred</option>
                        <option value="all" <%= typeof status !== 'undefined' && status === 'all' ? 'selected' : '' %>>All Statuses</option>
                    </select>
                </label>
                <button type="submit"><i class="fas fa-filter"></i> Filter</button>
                <a href="/students" class="btn btn-secondary"><i class="fas fa-times"></i> Clear</a>
            </form>
            <div class="table-container">
                <form id="bulkActionsForm">
                    <table>
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleAllCheckboxes()">
                            </th>
                            <th><i class="fas fa-image"></i> Photo</th>
                            <th><i class="fas fa-user"></i> Full Name</th>
                            <th><i class="fas fa-id-card"></i> Student ID</th>
                            <th><i class="fas fa-layer-group"></i> Grade</th>
                            <th><i class="fas fa-users"></i> Section</th>
                            <th><i class="fas fa-calendar-check"></i> Quarter</th>
                            <th><i class="fas fa-flag"></i> Status</th>
                            <th><i class="fas fa-calendar-alt"></i> Reg. Date</th>
                            <th><i class="fas fa-venus-mars"></i> Sex</th>
                            <th><i class="fas fa-cogs"></i> Actions</th>
                        </tr>
                    </thead>
                <tbody>
                    <% if (students.length === 0) { %>
                        <tr>
                            <td colspan="11" style="text-align: center; padding: 2rem; color: #666;">
                                <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                                No students found. <a href="/register">Register the first student</a>
                            </td>
                        </tr>
                    <% } else { %>
                        <% students.forEach(student => { %>
                            <tr>
                                <td>
                                    <input type="checkbox" name="studentIds" value="<%= student._id %>" class="student-checkbox">
                                </td>
                                <td>
                                    <% if (student.studentPic) { %>
                                        <img src="/uploads/<%= student.studentPic %>" width="60" height="60" style="border-radius: 50%; object-fit: cover;" />
                                    <% } else { %>
                                        <div style="width: 60px; height: 60px; background: #e1e8ed; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-user" style="color: #666;"></i>
                                        </div>
                                    <% } %>
                                </td>
                                <td><strong><%= student.fullName %></strong></td>
                                <td>
                                    <span style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.85rem; font-weight: 600;">
                                        <%= student.studentId || 'Generating...' %>
                                    </span>
                                </td>
                                <td><%= student.registeredGrade || 'N/A' %></td>
                                <td><%= student.section || 'N/A' %></td>
                                <td>
                                    <% if (student.quarter) { %>
                                        <span style="background: linear-gradient(135deg, #27ae60, #229954); color: white; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.85rem;">
                                            <%= student.quarter %>
                                        </span>
                                    <% } else { %>
                                        N/A
                                    <% } %>
                                </td>
                                <td>
                                    <%
                                        // Ensure we have a proper status value
                                        let status = student.status;
                                        if (!status || status === null || status === undefined || status === '') {
                                            status = 'active'; // Default to active
                                        }

                                        const statusColors = {
                                            'active': '#27ae60',
                                            'pending': '#f39c12',
                                            'graduated': '#3498db',
                                            'transferred': '#e74c3c'
                                        };
                                        const statusIcons = {
                                            'active': 'user-check',
                                            'pending': 'user-clock',
                                            'graduated': 'graduation-cap',
                                            'transferred': 'exchange-alt'
                                        };
                                    %>
                                    <span style="background: <%= statusColors[status] || '#666' %>; color: white; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.85rem; text-transform: uppercase;">
                                        <i class="fas fa-<%= statusIcons[status] || 'question' %>"></i> <%= status %>
                                    </span>
                                </td>
                                <td>
                                    <% if (student.registrationDate) { %>
                                        <%= new Date(student.registrationDate).toLocaleDateString() %>
                                    <% } else { %>
                                        N/A
                                    <% } %>
                                </td>
                                <td>
                                    <% if (student.sex === 'Male') { %>
                                        <i class="fas fa-mars" style="color: #3498db;"></i> Male
                                    <% } else if (student.sex === 'Female') { %>
                                        <i class="fas fa-venus" style="color: #e91e63;"></i> Female
                                    <% } else { %>
                                        N/A
                                    <% } %>
                                </td>
                                <td>
                                    <div class="actions">
                                        <a href="/students/<%= student._id %>"><i class="fas fa-eye"></i> View</a>
                                        <a href="/students/<%= student._id %>/edit"><i class="fas fa-edit"></i> Edit</a>
                                        <a href="/students/<%= student._id %>/status"><i class="fas fa-exchange-alt"></i> Status</a>
                                        <a href="/students/<%= student._id %>/print"><i class="fas fa-print"></i> Print</a>
                                        <a href="/students/<%= student._id %>/id-card"><i class="fas fa-id-card"></i> ID Card</a>
                                    </div>
                                </td>
                            </tr>
                        <% }) %>
                    <% } %>
                    </tbody>
                </table>
                </form>
            </div>

            <% if (students.length > 0) { %>
                <div style="text-align: center; margin-top: 2rem; padding: 1.5rem; background: rgba(255, 255, 255, 0.5); border-radius: 15px;">
                    <p style="color: #666; margin-bottom: 1rem;">
                        Showing <%= students.length %> student<%= students.length !== 1 ? 's' : '' %>
                        <% if (name || grade || section || studentId || quarter || sex) { %>
                            with applied filters
                        <% } %>
                    </p>
                    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                        <a href="/register" class="btn">
                            <i class="fas fa-plus"></i> Register New Student
                        </a>
                        <a href="/dashboard" class="btn btn-secondary">
                            <i class="fas fa-chart-bar"></i> View Dashboard
                        </a>
                        <% if (name || grade || section || studentId || quarter || sex) { %>
                            <a href="/students" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Clear Filters
                            </a>
                        <% } %>
                    </div>
                </div>
            <% } %>
        </div>
    </main>

    <!-- Export Modal -->
    <div id="exportModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-filter"></i> Export Students by Filters</h3>
                <button class="modal-close" onclick="closeExportModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="exportFilterForm">
                    <div class="filter-grid">
                        <div class="filter-group">
                            <label><i class="fas fa-calendar-check"></i> Quarter/Term</label>
                            <select name="quarter" id="exportQuarter">
                                <option value="">All Quarters</option>
                                <option value="Quarter 1">Quarter 1</option>
                                <option value="Quarter 2">Quarter 2</option>
                                <option value="Quarter 3">Quarter 3</option>
                                <option value="Quarter 4">Quarter 4</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label><i class="fas fa-layer-group"></i> Grade Level</label>
                            <select name="grade" id="exportGrade">
                                <option value="">All Grades</option>
                                <option value="Pre-K">Pre-K</option>
                                <option value="Kindergarten">Kindergarten</option>
                                <option value="Grade 1">Grade 1</option>
                                <option value="Grade 2">Grade 2</option>
                                <option value="Grade 3">Grade 3</option>
                                <option value="Grade 4">Grade 4</option>
                                <option value="Grade 5">Grade 5</option>
                                <option value="Grade 6">Grade 6</option>
                                <option value="Grade 7">Grade 7</option>
                                <option value="Grade 8">Grade 8</option>
                                <option value="Grade 9">Grade 9</option>
                                <option value="Grade 10">Grade 10</option>
                                <option value="Grade 11">Grade 11</option>
                                <option value="Grade 12">Grade 12</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label><i class="fas fa-users"></i> Section</label>
                            <input type="text" name="section" id="exportSection" placeholder="Enter section (e.g., A, B, C)">
                        </div>

                        <div class="filter-group">
                            <label><i class="fas fa-venus-mars"></i> Gender</label>
                            <select name="sex" id="exportSex">
                                <option value="">All Genders</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label><i class="fas fa-flag"></i> Status</label>
                            <select name="status" id="exportStatus">
                                <option value="">Active Only</option>
                                <option value="active">Active</option>
                                <option value="pending">Pending</option>
                                <option value="graduated">Graduated</option>
                                <option value="transferred">Transferred</option>
                                <option value="all">All Statuses</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label><i class="fas fa-sort-alpha-down"></i> Name Range</label>
                            <input type="text" name="nameRange" id="exportNameRange" placeholder="e.g., A-M or John-Mary">
                        </div>
                    </div>

                    <div class="export-options">
                        <h4><i class="fas fa-download"></i> Export Options</h4>
                        <div class="checkbox-group">
                            <label>
                                <input type="checkbox" name="exportType" value="csv" checked>
                                <i class="fas fa-file-csv"></i>
                                CSV Spreadsheet
                            </label>
                            <label>
                                <input type="checkbox" name="exportType" value="pdf">
                                <i class="fas fa-file-pdf"></i>
                                PDF Report
                            </label>
                            <label>
                                <input type="checkbox" name="exportType" value="print">
                                <i class="fas fa-print"></i>
                                Print Preview
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeExportModal()">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="button" class="btn" onclick="executeExport()">
                    <i class="fas fa-download"></i> Export Students
                </button>
            </div>
        </div>
    </div>

    <script>
        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('exportDropdown');
            const button = event.target.closest('button');
            if (!button || !button.onclick || button.onclick.toString().indexOf('toggleDropdown') === -1) {
                dropdown.style.display = 'none';
            }
        });

        function toggleAllCheckboxes() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.student-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        function getSelectedStudentIds() {
            const checkboxes = document.querySelectorAll('.student-checkbox:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        function printSelectedIDCards() {
            const selectedIds = getSelectedStudentIds();
            if (selectedIds.length === 0) {
                alert('Please select at least one student to print ID cards.');
                return;
            }

            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/students/print/id-cards';
            form.target = '_blank';

            selectedIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'studentIds';
                input.value = id;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function printAllIDCards() {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/students/print/id-cards';
            form.target = '_blank';

            // Add current filter parameters
            const urlParams = new URLSearchParams(window.location.search);
            for (const [key, value] of urlParams) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                form.appendChild(input);
            }

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function exportToCSV() {
            const urlParams = new URLSearchParams(window.location.search);
            const csvUrl = '/students/export/csv' + (urlParams.toString() ? '?' + urlParams.toString() : '');
            window.open(csvUrl, '_blank');
        }

        function exportSelectedStudents() {
            const selectedIds = getSelectedStudentIds();
            if (selectedIds.length === 0) {
                alert('Please select at least one student to export.');
                return;
            }

            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/students/export/selected';
            form.target = '_blank';

            selectedIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'studentIds';
                input.value = id;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function printSelectedDocuments() {
            const selectedIds = getSelectedStudentIds();
            if (selectedIds.length === 0) {
                alert('Please select at least one student to print documents.');
                return;
            }

            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/students/print/documents';
            form.target = '_blank';

            selectedIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'studentIds';
                input.value = id;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function showExportModal() {
            document.getElementById('exportModal').style.display = 'flex';
            // Close the dropdown
            document.getElementById('exportDropdown').style.display = 'none';
        }

        function closeExportModal() {
            document.getElementById('exportModal').style.display = 'none';
        }

        function executeExport() {
            const form = document.getElementById('exportFilterForm');
            const formData = new FormData(form);
            const exportTypes = formData.getAll('exportType');

            if (exportTypes.length === 0) {
                alert('Please select at least one export option.');
                return;
            }

            // Build query parameters
            const params = new URLSearchParams();

            // Add filter parameters
            if (formData.get('quarter')) params.append('quarter', formData.get('quarter'));
            if (formData.get('grade')) params.append('grade', formData.get('grade'));
            if (formData.get('section')) params.append('section', formData.get('section'));
            if (formData.get('sex')) params.append('sex', formData.get('sex'));
            if (formData.get('status')) params.append('status', formData.get('status'));
            if (formData.get('nameRange')) params.append('nameRange', formData.get('nameRange'));

            // Execute each selected export type
            exportTypes.forEach(type => {
                let url;
                switch(type) {
                    case 'csv':
                        url = '/students/export/csv?' + params.toString();
                        window.open(url, '_blank');
                        break;
                    case 'pdf':
                        url = '/students/export/pdf?' + params.toString();
                        window.open(url, '_blank');
                        break;
                    case 'print':
                        url = '/students/print/filtered?' + params.toString();
                        window.open(url, '_blank');
                        break;
                }
            });

            closeExportModal();
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('exportModal');
            if (event.target === modal) {
                closeExportModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeExportModal();
            }
        });
    </script>

    <style>
        /* Override dropdown styles to ensure all items show */
        #exportDropdown.dropdown-menu {
            position: absolute !important;
            top: 100% !important;
            right: 0 !important;
            background: var(--bg-white) !important;
            border: 1px solid var(--border-color) !important;
            border-radius: var(--radius-xl) !important;
            box-shadow: var(--shadow-xl) !important;
            z-index: 9999 !important;
            min-width: 320px !important;
            max-width: 400px !important;
            max-height: 80vh !important;
            overflow-y: auto !important;
            overflow-x: visible !important;
            display: none !important;
            white-space: nowrap !important;
        }

        #exportDropdown.dropdown-menu[style*="block"] {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        #exportDropdown .dropdown-item {
            display: flex !important;
            align-items: center;
            gap: var(--space-sm);
            padding: 12px 16px !important;
            color: var(--text-primary) !important;
            text-decoration: none;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            font-size: 14px !important;
            line-height: 1.4 !important;
            transition: var(--transition-fast);
            border-bottom: 1px solid var(--border-light);
            min-height: 40px !important;
            box-sizing: border-box !important;
        }

        #exportDropdown .dropdown-item:last-child {
            border-bottom: none;
        }

        #exportDropdown .dropdown-item:hover {
            background: var(--bg-gray-50) !important;
            color: var(--primary-color) !important;
        }

        #exportDropdown .dropdown-item i {
            width: 16px;
            color: var(--primary-color);
        }

        .student-checkbox {
            width: auto;
            margin: 0;
        }

        #selectAll {
            width: auto;
            margin: 0;
        }

        /* Enhanced Dropdown Sections */
        #exportDropdown .dropdown-section {
            border-bottom: 1px solid var(--border-light);
            padding: 4px 0 !important;
            margin: 0 !important;
        }

        #exportDropdown .dropdown-section:last-child {
            border-bottom: none;
        }

        #exportDropdown .dropdown-header {
            padding: 8px 16px !important;
            font-weight: 600;
            font-size: 11px !important;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background: var(--bg-gray-50);
            border-bottom: 1px solid var(--border-light);
            margin: 0 !important;
            display: flex !important;
            align-items: center;
            min-height: 32px !important;
        }

        #exportDropdown .dropdown-header i {
            margin-right: 6px !important;
            color: var(--primary-color);
            width: 14px;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: var(--bg-white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-2xl);
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-xl);
            border-bottom: 1px solid var(--border-color);
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
        }

        .modal-header h3 {
            margin: 0;
            font-size: var(--font-size-xl);
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: var(--font-size-2xl);
            cursor: pointer;
            padding: var(--space-sm);
            border-radius: var(--radius-md);
            transition: var(--transition-fast);
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: var(--space-2xl);
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-lg);
            margin-bottom: var(--space-2xl);
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-sm);
        }

        .filter-group label {
            font-weight: 600;
            color: var(--text-primary);
            font-size: var(--font-size-sm);
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .filter-group label i {
            color: var(--primary-color);
            width: 16px;
        }

        .filter-group input,
        .filter-group select {
            padding: var(--space-md);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            font-size: var(--font-size-sm);
            transition: var(--transition-base);
        }

        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .export-options {
            background: var(--bg-gray-50);
            padding: var(--space-xl);
            border-radius: var(--radius-xl);
            border: 1px solid var(--border-color);
        }

        .export-options h4 {
            margin: 0 0 var(--space-lg) 0;
            color: var(--text-primary);
            font-size: var(--font-size-lg);
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .export-options h4 i {
            color: var(--primary-color);
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: var(--space-md);
            padding: var(--space-xl);
            border-top: 1px solid var(--border-color);
            background: var(--bg-gray-50);
            border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
        }
    </style>
</body>
</html>