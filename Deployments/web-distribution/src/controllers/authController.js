const User = require('../models/user');

// Render login page
const getLogin = (req, res) => {
    if (req.session.userId) {
        return res.redirect('/');
    }
    res.render('auth/login', { 
        title: 'Login',
        error_msg: req.flash('error_msg')
    });
};

// Handle login
const login = async (req, res) => {
    const { username, password } = req.body;

    try {
        // Find user by username
        const user = await User.findOne({ username });
        
        // Check if user exists and password is correct
        if (!user || !(await user.matchPassword(password))) {
            req.flash('error_msg', 'Invalid username or password');
            return res.redirect('/login');
        }

        // Set user session
        req.session.userId = user._id;
        req.session.username = user.username;
        req.session.isAdmin = user.isAdmin;
        
        // Redirect based on user role
        const redirectTo = user.isAdmin ? '/admin/dashboard' : '/';
        res.redirect(redirectTo);

    } catch (error) {
        console.error('Login error:', error);
        req.flash('error_msg', 'An error occurred during login');
        res.redirect('/login');
    }
};

// Handle logout
const logout = (req, res) => {
    req.session.destroy(err => {
        if (err) {
            console.error('Logout error:', err);
            return res.redirect('/');
        }
        res.redirect('/login');
    });
};

// Create admin user (one-time setup)
const createAdmin = async (req, res) => {
    try {
        const { username, password } = req.body;
        
        // Check if admin already exists
        const adminExists = await User.findOne({ isAdmin: true });
        if (adminExists) {
            return res.status(400).json({ 
                success: false, 
                message: 'Admin user already exists' 
            });
        }

        // Create new admin user
        const admin = new User({
            username,
            password,
            isAdmin: true
        });

        await admin.save();
        
        res.status(201).json({ 
            success: true, 
            message: 'Admin user created successfully' 
        });
    } catch (error) {
        console.error('Create admin error:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Error creating admin user' 
        });
    }
};

module.exports = {
    getLogin,
    login,
    logout,
    createAdmin
};
