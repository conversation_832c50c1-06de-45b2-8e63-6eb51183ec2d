// System Configuration
module.exports = {
    // System Information
    systemName: process.env.SYSTEM_NAME || 'SHEGE SRS',
    systemFullName: process.env.SYSTEM_FULL_NAME || 'Student Registration System',
    systemVersion: process.env.SYSTEM_VERSION || '1.0.0',
    
    // School Information
    schoolName: process.env.SCHOOL_NAME || 'SHEGE SRS',
    schoolAddress: process.env.SCHOOL_ADDRESS || '',
    schoolPhone: process.env.SCHOOL_PHONE || '',
    schoolEmail: process.env.SCHOOL_EMAIL || '',
    schoolWebsite: process.env.SCHOOL_WEBSITE || '',
    
    // System Settings
    defaultLanguage: process.env.DEFAULT_LANGUAGE || 'en',
    timezone: process.env.TIMEZONE || 'UTC',
    dateFormat: process.env.DATE_FORMAT || 'MM/DD/YYYY',
    
    // Branding
    primaryColor: process.env.PRIMARY_COLOR || '#ff6b35',
    secondaryColor: process.env.SECONDARY_COLOR || '#2c3e50',
    logoPath: process.env.LOGO_PATH || '/images/logo.png',
    
    // Features
    enableRegistration: process.env.ENABLE_REGISTRATION !== 'false',
    enablePrintFeatures: process.env.ENABLE_PRINT_FEATURES !== 'false',
    enableExportFeatures: process.env.ENABLE_EXPORT_FEATURES !== 'false',
    
    // File Upload Settings
    maxFileSize: process.env.MAX_FILE_SIZE || '5MB',
    allowedImageTypes: ['jpg', 'jpeg', 'png', 'gif'],
    allowedDocumentTypes: ['pdf', 'doc', 'docx'],
    
    // Pagination
    defaultPageSize: parseInt(process.env.DEFAULT_PAGE_SIZE) || 20,
    maxPageSize: parseInt(process.env.MAX_PAGE_SIZE) || 100,
    
    // Security
    sessionTimeout: parseInt(process.env.SESSION_TIMEOUT) || 3600000, // 1 hour in milliseconds
    maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5,
    
    // Development
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production',
    debugMode: process.env.DEBUG_MODE === 'true',
    
    // Database
    dbName: process.env.DB_NAME || 'student-registration',
    
    // Email (if needed in future)
    emailEnabled: process.env.EMAIL_ENABLED === 'true',
    smtpHost: process.env.SMTP_HOST || '',
    smtpPort: parseInt(process.env.SMTP_PORT) || 587,
    smtpUser: process.env.SMTP_USER || '',
    smtpPass: process.env.SMTP_PASS || '',
    
    // Backup (if needed in future)
    backupEnabled: process.env.BACKUP_ENABLED === 'true',
    backupInterval: process.env.BACKUP_INTERVAL || '24h',
    backupPath: process.env.BACKUP_PATH || './backups',
    
    // API Settings
    apiVersion: process.env.API_VERSION || 'v1',
    apiRateLimit: parseInt(process.env.API_RATE_LIMIT) || 100, // requests per minute
    
    // Logging
    logLevel: process.env.LOG_LEVEL || 'info',
    logFile: process.env.LOG_FILE || './logs/app.log',
    
    // Cache
    cacheEnabled: process.env.CACHE_ENABLED !== 'false',
    cacheTTL: parseInt(process.env.CACHE_TTL) || 300, // 5 minutes
    
    // Reports
    defaultReportFormat: process.env.DEFAULT_REPORT_FORMAT || 'pdf',
    reportsPath: process.env.REPORTS_PATH || './reports',
    
    // Student ID Generation
    studentIdPrefix: process.env.STUDENT_ID_PREFIX || 'STU',
    studentIdLength: parseInt(process.env.STUDENT_ID_LENGTH) || 8,
    
    // Academic Year
    currentAcademicYear: process.env.CURRENT_ACADEMIC_YEAR || new Date().getFullYear().toString(),
    academicYearStart: process.env.ACADEMIC_YEAR_START || '09-01', // September 1st
    academicYearEnd: process.env.ACADEMIC_YEAR_END || '08-31', // August 31st
    
    // Quarters/Terms
    quarters: [
        'Quarter 1',
        'Quarter 2', 
        'Quarter 3',
        'Quarter 4'
    ],
    
    // Grade Levels
    gradeLevels: [
        'Pre-K',
        'Kindergarten',
        'Grade 1',
        'Grade 2',
        'Grade 3',
        'Grade 4',
        'Grade 5',
        'Grade 6',
        'Grade 7',
        'Grade 8',
        'Grade 9',
        'Grade 10',
        'Grade 11',
        'Grade 12'
    ],
    
    // Default Sections
    defaultSections: ['A', 'B', 'C', 'D', 'E'],
    
    // Status Options
    studentStatuses: [
        'active',
        'pending',
        'graduated',
        'transferred'
    ],
    
    // Medical Conditions
    medicalConditions: [
        'shortSight',
        'longSight',
        'allergic',
        'autism',
        'languageProblem'
    ],
    
    // Blood Types
    bloodTypes: [
        'A+', 'A-',
        'B+', 'B-',
        'AB+', 'AB-',
        'O+', 'O-'
    ],
    
    // Relationships
    guardianRelationships: [
        'Father',
        'Mother',
        'Grandfather',
        'Grandmother',
        'Uncle',
        'Aunt',
        'Brother',
        'Sister',
        'Legal Guardian',
        'Other'
    ]
};
