const mongoose = require('mongoose');
const Student = require('../models/student');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/student-registration', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

async function updateStudentStatus() {
    try {
        console.log('Starting student status update...');
        
        // Find all students without a status field or with null/undefined status
        const studentsToUpdate = await Student.find({
            $or: [
                { status: { $exists: false } },
                { status: null },
                { status: undefined },
                { status: '' }
            ]
        });
        
        console.log(`Found ${studentsToUpdate.length} students without proper status`);
        
        // Update each student to have 'active' status
        for (const student of studentsToUpdate) {
            await Student.findByIdAndUpdate(student._id, {
                status: 'active',
                statusDate: student.registrationDate || new Date(),
                statusReason: 'Initial status assignment',
                statusNotes: 'Automatically set to active during system update'
            });
            
            console.log(`Updated student: ${student.fullName} (ID: ${student._id})`);
        }
        
        console.log('Student status update completed successfully!');
        
        // Verify the update
        const allStudents = await Student.find({});
        const statusCounts = {
            active: 0,
            pending: 0,
            graduated: 0,
            transferred: 0,
            undefined: 0
        };
        
        allStudents.forEach(student => {
            const status = student.status || 'undefined';
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });
        
        console.log('Current status distribution:', statusCounts);
        
    } catch (error) {
        console.error('Error updating student status:', error);
    } finally {
        mongoose.connection.close();
    }
}

// Run the update
updateStudentStatus();
