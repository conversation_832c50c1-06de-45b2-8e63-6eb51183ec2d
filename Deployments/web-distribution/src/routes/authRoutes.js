const express = require('express');
const router = express.Router();
const { getLogin, login, logout, createAdmin } = require('../controllers/authController');
const { isAuthenticated, isAdmin } = require('../middleware/auth');

// Login routes
router.get('/login', getLogin);
router.post('/login', login);

// Logout route
router.get('/logout', logout);

// Admin setup route (one-time use)
router.post('/api/setup-admin', createAdmin);

module.exports = router;
