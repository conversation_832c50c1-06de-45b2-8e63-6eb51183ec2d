# SHEGE SRS Web Application

## Quick Start

### Prerequisites
- Node.js 16+ 
- MongoDB 4.4+
- 2GB RAM minimum

### Installation

1. **Extract the package**
   ```bash
   tar -xzf shege-srs-web.tar.gz
   cd shege-srs-web
   ```

2. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Start the application**
   ```bash
   ./start.sh
   ```

4. **Access the application**
   - Open browser to http://localhost:3000
   - Login: admin / admin123
   - Change password immediately

## Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Access at http://localhost:3000
```

## Cloud Deployment

### Heroku
[![Deploy](https://www.herokucdn.com/deploy/button.svg)](https://heroku.com/deploy?template=https://github.com/yourusername/shege-srs)

### Railway
[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/new/template?template=https://github.com/yourusername/shege-srs)

### DigitalOcean
[![Deploy to DO](https://www.deploytodo.com/do-btn-blue.svg)](https://cloud.digitalocean.com/apps/new?repo=https://github.com/yourusername/shege-srs)

## Configuration

### Database
- **Local MongoDB**: `mongodb://localhost:27017/student-registration`
- **MongoDB Atlas**: `mongodb+srv://user:<EMAIL>/student-registration`

### Security
- Change `SESSION_SECRET` to a secure random string
- Use strong admin password
- Enable HTTPS in production

## Features
- ✅ Student registration and management
- ✅ Photo uploads and document tracking
- ✅ Data export (CSV)
- ✅ ID card generation
- ✅ Print functionality
- ✅ Multi-quarter support
- ✅ Responsive web interface

## Support
- Documentation: See docs/ directory
- Issues: Contact system administrator
- Updates: Check for new releases

## License
MIT License - See LICENSE file for details
