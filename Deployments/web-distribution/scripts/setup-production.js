#!/usr/bin/env node

const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

async function setupProduction() {
    console.log('🚀 Setting up SHEGE SRS for production...');
    
    try {
        // Connect to MongoDB
        const mongoUri = process.env.MONGODB_URI || process.env.MONGOLAB_URI || 'mongodb://localhost:27017/student-registration';
        console.log('📡 Connecting to MongoDB...');
        await mongoose.connect(mongoUri);
        console.log('✅ Connected to MongoDB');
        
        // Check if admin user exists
        const adminExists = await mongoose.connection.db.collection('admins').findOne({username: 'admin'});
        
        if (!adminExists) {
            console.log('👤 Creating default admin user...');
            
            // Generate secure password
            const defaultPassword = process.env.ADMIN_PASSWORD || 'admin123';
            const hashedPassword = await bcrypt.hash(defaultPassword, 12);
            
            // Create admin user
            await mongoose.connection.db.collection('admins').insertOne({
                username: 'admin',
                password: hashedPassword,
                role: 'administrator',
                createdAt: new Date(),
                isDefault: true
            });
            
            console.log('✅ Admin user created');
            console.log('🔐 Default credentials:');
            console.log('   Username: admin');
            console.log('   Password: admin123');
            console.log('⚠️  IMPORTANT: Change the password immediately after first login!');
        } else {
            console.log('✅ Admin user already exists');
        }
        
        // Create indexes for better performance
        console.log('📊 Creating database indexes...');
        
        const studentsCollection = mongoose.connection.db.collection('students');
        await studentsCollection.createIndex({ fullName: 'text' });
        await studentsCollection.createIndex({ registeredGrade: 1 });
        await studentsCollection.createIndex({ section: 1 });
        await studentsCollection.createIndex({ quarter: 1 });
        await studentsCollection.createIndex({ registrationDate: -1 });
        
        console.log('✅ Database indexes created');
        
        // Setup system configuration
        console.log('⚙️  Setting up system configuration...');
        
        const configExists = await mongoose.connection.db.collection('config').findOne({type: 'system'});
        
        if (!configExists) {
            await mongoose.connection.db.collection('config').insertOne({
                type: 'system',
                name: process.env.SYSTEM_NAME || 'SHEGE SRS',
                version: '1.0.0',
                setupDate: new Date(),
                features: {
                    registration: true,
                    exports: true,
                    printing: true,
                    idCards: true
                },
                limits: {
                    maxFileSize: '5MB',
                    maxStudents: 10000,
                    sessionTimeout: 3600000
                }
            });
            console.log('✅ System configuration created');
        }
        
        console.log('');
        console.log('🎉 Production setup completed successfully!');
        console.log('');
        console.log('📋 Next steps:');
        console.log('1. Access your application at the provided URL');
        console.log('2. Login with username: admin, password: admin123');
        console.log('3. Change the admin password immediately');
        console.log('4. Configure school information in settings');
        console.log('5. Start registering students');
        console.log('');
        
    } catch (error) {
        console.error('❌ Production setup failed:', error);
        process.exit(1);
    } finally {
        await mongoose.disconnect();
        console.log('📡 Disconnected from MongoDB');
    }
}

// Run setup
setupProduction().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});
