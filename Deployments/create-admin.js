#!/usr/bin/env node

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

async function createAdmin() {
    try {
        console.log('🔧 Creating admin user...');
        
        // Connect to MongoDB
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/student-registration';
        console.log('Connecting to:', mongoUri);
        await mongoose.connect(mongoUri);
        console.log('✅ Connected to MongoDB');
        
        // Define User schema (same as in the app)
        const userSchema = new mongoose.Schema({
            username: { type: String, required: true, unique: true },
            password: { type: String, required: true }
        });
        
        const User = mongoose.model('User', userSchema);
        
        // Delete existing admin user if any
        await User.deleteMany({ username: 'admin' });
        console.log('🗑️  Cleared existing admin users');
        
        // Create new admin user
        console.log('👤 Creating new admin user...');
        
        // Hash the password
        const hashedPassword = await bcrypt.hash('admin123', 10);
        console.log('🔐 Password hashed');
        
        // Create admin user
        const admin = new User({
            username: 'admin',
            password: hashedPassword
        });
        
        await admin.save();
        console.log('✅ Admin user created successfully');
        
        // Verify the user was created
        const verifyUser = await User.findOne({ username: 'admin' });
        if (verifyUser) {
            console.log('✅ Admin user verified in database');
            console.log('📋 User ID:', verifyUser._id);
            
            // Test password verification
            const passwordMatch = await bcrypt.compare('admin123', verifyUser.password);
            console.log('🔐 Password verification:', passwordMatch ? 'SUCCESS' : 'FAILED');
        } else {
            console.log('❌ Admin user not found after creation');
        }
        
        // List all users
        const allUsers = await User.find();
        console.log('👥 Total users in database:', allUsers.length);
        
        await mongoose.disconnect();
        console.log('📡 Disconnected from MongoDB');
        
        console.log('');
        console.log('🎉 Setup complete!');
        console.log('📋 Login credentials:');
        console.log('   Username: admin');
        console.log('   Password: admin123');
        
    } catch (error) {
        console.error('❌ Setup failed:', error);
        process.exit(1);
    }
}

// Run setup
createAdmin();
