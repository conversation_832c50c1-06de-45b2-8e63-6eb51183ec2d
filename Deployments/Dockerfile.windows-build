# Dockerfile for building Windows executables on Linux
FROM node:18-bullseye

# Install Wine and dependencies
RUN apt-get update && \
    apt-get install -y wine && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set Wine environment
ENV WINEARCH=win64
ENV WINEPREFIX=/root/.wine
ENV DISPLAY=:99

# Initialize Wine
RUN wine --version && \
    wineboot --init

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Create .env file
RUN echo "SYSTEM_NAME=SHEGE SRS" > .env && \
    echo "MONGODB_URI=mongodb://localhost:27017/student-registration" >> .env && \
    echo "PORT=3000" >> .env && \
    echo "NODE_ENV=production" >> .env && \
    echo "SESSION_SECRET=docker-build-secret" >> .env

# Build Windows portable
RUN npm run build-portable-win

# Create output directory
RUN mkdir -p /output && \
    cp dist/*.exe /output/ 2>/dev/null || true

# Set entrypoint to copy files
CMD ["cp", "-r", "dist/", "/output/"]
