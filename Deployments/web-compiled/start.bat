@echo off
echo Starting SHEGE SRS Web Application
echo ====================================

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo Node.js is not installed
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
)

REM Install dependencies if needed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install --production
)

REM Create uploads directory
if not exist "uploads" mkdir uploads

REM Start the application
echo Starting web server...
echo Application will be available at: http://localhost:3000
echo Default login: admin / admin123
echo.
echo Press Ctrl+C to stop the server
echo.

node app.bundle.js
pause
