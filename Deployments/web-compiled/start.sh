#!/bin/bash

echo "🚀 Starting SHEGE SRS Web Application"
echo "====================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed"
    echo "Please install Node.js from https://nodejs.org"
    exit 1
fi

# Check if MongoDB is running
if ! pgrep mongod > /dev/null; then
    echo "⚠️  MongoDB is not running"
    echo "Please start MongoDB:"
    echo "  sudo systemctl start mongod"
    echo "  or install MongoDB from https://mongodb.com"
    echo ""
    echo "Continuing anyway (you can use external MongoDB)..."
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install --production
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
fi

# Create uploads directory
mkdir -p uploads

# Start the application
echo "🌐 Starting web server..."
echo "Application will be available at: http://localhost:3000"
echo "Default login: admin / admin123"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

node app.bundle.js
