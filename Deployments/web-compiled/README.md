# SHEGE SRS - Compiled Web Application

## 🚀 Quick Start

### Windows Users
1. Double-click `launch.bat`
2. Wait for browser to open automatically
3. Login with: admin / admin123

### Linux/Mac Users
1. Run: `./launch.sh`
2. Wait for browser to open automatically  
3. Login with: admin / admin123

### Manual Start
1. Run: `./start.sh` (Linux/Mac) or `start.bat` (Windows)
2. Open browser to: http://localhost:3000
3. Login with: admin / admin123

## 📋 Prerequisites

### Required
- **Node.js 16+**: Download from https://nodejs.org
- **MongoDB**: Download from https://mongodb.com

### Installation
1. Install Node.js from official website
2. Install MongoDB and start the service
3. Extract this application package
4. Run the launcher

## 🔧 Configuration

Edit the `.env` file to customize:
- School name and contact information
- Database connection settings
- System features and limits

## 🌐 Features

- ✅ Student registration and management
- ✅ Photo uploads and document tracking
- ✅ Data export (CSV format)
- ✅ ID card generation with guardian info
- ✅ Print functionality
- ✅ Multi-quarter support
- ✅ Responsive web interface

## 🔒 Security

- Source code is compiled and obfuscated
- Secure session management
- Input validation and sanitization
- File upload restrictions

## 📞 Support

- Default login: admin / admin123
- Change password after first login
- Ensure MongoDB is running
- Check firewall allows port 3000

## 🏗️ Technical Details

- **Compiled**: Source code is minified and obfuscated
- **Secure**: No source code exposure
- **Portable**: Self-contained application
- **Cross-platform**: Works on Windows, Mac, Linux

---

**Built with ❤️ for educational institutions**
