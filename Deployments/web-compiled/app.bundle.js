(()=>{var e={37:module=>{"use strict";module.exports=require("mongoose")},47:(module,e,t)=>{const r=t(37);module.exports=async()=>{try{await r.connect(process.env.MONGODB_URI||"mongodb://localhost:27017/student-registration")}catch(e){process.exit(1)}}},252:module=>{"use strict";module.exports=require("express")},256:(module,e,t)=>{const r=t(252).Router(),s=t(578),a=t(461),n=t(928),i=t(571),o=t(680).createObjectCsvWriter,d=t(896),u=a.diskStorage({destination:function(e,t,r){r(null,n.join(__dirname,"../public/uploads"))},filename:function(e,t,r){r(null,Date.now()+"-"+t.originalname)}}),l=a({storage:u});function c(e){const t={};if(e.name&&(t.fullName={$regex:e.name,$options:"i"}),e.grade&&(t.registeredGrade={$regex:e.grade,$options:"i"}),e.section&&(t.section={$regex:e.section,$options:"i"}),e.quarter&&(t.quarter=e.quarter),e.sex&&(t.sex=e.sex),e.status?"all"===e.status||(t.status=e.status):t.$or=[{status:"active"},{status:{$exists:!1}},{status:null}],e.nameRange){const r=e.nameRange.split("-");if(2===r.length){const e=r[0].trim(),s=r[1].trim();1===e.length&&1===s.length?t.fullName={$regex:`^[${e.toUpperCase()}-${s.toUpperCase()}]`,$options:"i"}:t.fullName={$gte:e,$lte:s+"z"}}}return t}r.get("/login",i.loginForm),r.post("/login",i.login),r.get("/logout",i.logout),r.get("/register",i.ensureAdmin,(e,t)=>{t.render("register")}),r.post("/register",i.ensureAdmin,l.fields([{name:"studentPic",maxCount:1},{name:"fatherPic",maxCount:1},{name:"motherPic",maxCount:1},{name:"birthCertificate",maxCount:1},{name:"transcript",maxCount:1},{name:"parentsID",maxCount:1}]),async(e,t)=>{try{const{body:r,files:a}=e,n=e=>e&&"string"==typeof e?e.trim().replace(/[<>]/g,""):"",i=e=>{if(!e)return;const t=parseFloat(e);return isNaN(t)?void 0:t},o=e=>{if(!e)return;const t=new Date(e);return isNaN(t.getTime())?void 0:t},d=e=>"boolean"==typeof e?e:"string"==typeof e&&("true"===e.toLowerCase()||"on"===e.toLowerCase()||"1"===e),u={city:n(r["address.city"]||r.addressCity),subCity:n(r["address.subCity"]||r.addressSubCity),tabia:n(r["address.tabia"]||r.addressTabia),ketena:n(r["address.ketena"]||r.addressKetena),block:n(r["address.block"]||r.addressBlock),nationality:n(r["address.nationality"]||r.addressNationality)},l=new s({registrationDate:o(r.registrationDate)||new Date,quarter:n(r.quarter),fullName:n(r.fullName),sex:n(r.sex),dob:o(r.dob),age:i(r.age),formerGrade:n(r.formerGrade),registeredGrade:n(r.registeredGrade),section:n(r.section),motherTongue:n(r.motherTongue),bloodType:n(r.bloodType),weight:i(r.weight),height:i(r.height),handUse:n(r.handUse),address:u,seriousSickness:n(r.seriousSickness),studentPic:a.studentPic?a.studentPic[0].filename:"",father:{fullName:n(r.fatherFullName),phone:n(r.fatherPhone),nationality:n(r.fatherNationality),education:n(r.fatherEducation),occupation:n(r.fatherOccupation),workplace:n(r.fatherWorkplace),photo:a.fatherPic?a.fatherPic[0].filename:""},mother:{fullName:n(r.motherFullName),phone:n(r.motherPhone),nationality:n(r.motherNationality),education:n(r.motherEducation),occupation:n(r.motherOccupation),workplace:n(r.motherWorkplace),photo:a.motherPic?a.motherPic[0].filename:""},guardian:{name:n(r.guardianName),relationship:n(r.guardianRelationship),phone:n(r.guardianPhone)},transport:{subCity:n(r.transportSubCity),tabya:n(r.transportTabya),bus:n(r.transportBus),startDate:o(r.transportStartDate)},documents:{birthCertificate:a.birthCertificate?a.birthCertificate[0].filename:"",transcript:a.transcript?a.transcript[0].filename:"",parentsID:a.parentsID?a.parentsID[0].filename:"",buyBook:d(r.buyBook)},others:{shortSight:d(r.shortSight),longSight:d(r.longSight),allergic:d(r.allergic),autism:d(r.autism),languageProblem:d(r.languageProblem)}}),c=l.validateSync();if(c){const r=Object.values(c.errors).map(e=>e.message);return e.flash("error_msg","Validation failed: "+r.join(", ")),t.redirect("/register")}await l.save(),e.flash("success_msg","Student registered successfully!"),t.redirect("/students")}catch(r){let s="Registration failed. Please try again.";if("ValidationError"===r.name){s="Validation failed: "+Object.values(r.errors).map(e=>"required"===e.kind?`${e.path} is required`:"enum"===e.kind?`${e.path} must be one of: ${e.enumValues.join(", ")}`:"Number"===e.kind?`${e.path} must be a valid number`:"Date"===e.kind?`${e.path} must be a valid date`:"Boolean"===e.kind?`${e.path} must be true or false`:e.message).join(", ")}else 11e3===r.code?s="A student with this information already exists.":"CastError"===r.name&&(s=`Invalid data format for ${r.path}: ${r.value}`);e.flash("error_msg",s),t.redirect("/register")}}),r.get("/students",i.ensureAdmin,async(e,t)=>{try{const r={};e.query.name&&(r.fullName={$regex:e.query.name,$options:"i"}),e.query.grade&&(r.registeredGrade={$regex:e.query.grade,$options:"i"}),e.query.section&&(r.section={$regex:e.query.section,$options:"i"}),e.query.studentId&&(r.studentId={$regex:e.query.studentId,$options:"i"}),e.query.quarter&&(r.quarter=e.query.quarter),e.query.sex&&(r.sex=e.query.sex),e.query.status?"all"===e.query.status||(r.status=e.query.status):r.$or=[{status:"active"},{status:{$exists:!1}},{status:null}];const a=await s.find(r).sort({registrationDate:-1});t.render("index",{students:a,name:e.query.name,grade:e.query.grade,section:e.query.section,studentId:e.query.studentId,quarter:e.query.quarter,sex:e.query.sex,status:e.query.status})}catch(r){e.flash("error_msg","Error loading students"),t.render("index",{students:[]})}}),r.get("/students/:id/edit",i.ensureAdmin,async(e,t)=>{const r=await s.findById(e.params.id);t.render("edit",{student:r})}),r.post("/students/:id",i.ensureAdmin,l.fields([{name:"studentPic",maxCount:1},{name:"fatherPic",maxCount:1},{name:"motherPic",maxCount:1},{name:"birthCertificate",maxCount:1},{name:"transcript",maxCount:1},{name:"parentsID",maxCount:1}]),async(e,t)=>{try{const{body:r,files:a}=e,n={registrationDate:r.registrationDate,quarter:r.quarter,fullName:r.fullName,sex:r.sex,dob:r.dob,age:r.age,formerGrade:r.formerGrade,registeredGrade:r.registeredGrade,section:r.section,motherTongue:r.motherTongue,bloodType:r.bloodType,weight:r.weight,height:r.height,handUse:r.handUse,seriousSickness:r.seriousSickness,studentPic:a.studentPic?a.studentPic[0].filename:void 0,parentPics:a.parentPics?a.parentPics.map(e=>e.filename):void 0,father:{fullName:r.fatherFullName,phone:r.fatherPhone,nationality:r.fatherNationality,education:r.fatherEducation,occupation:r.fatherOccupation,workplace:r.fatherWorkplace,photo:a.parentPics&&a.parentPics[0]?a.parentPics[0].filename:void 0},mother:{fullName:r.motherFullName,phone:r.motherPhone,nationality:r.motherNationality,education:r.motherEducation,occupation:r.motherOccupation,workplace:r.motherWorkplace,photo:a.parentPics&&a.parentPics[1]?a.parentPics[1].filename:void 0},guardian:{name:r.guardianName,relationship:r.guardianRelationship,phone:r.guardianPhone},transport:{subCity:r.transportSubCity,tabya:r.transportTabya,bus:r.transportBus,startDate:r.transportStartDate},documents:{birthCertificate:!!r.birthCertificate,transcript:!!r.transcript,parentsID:!!r.parentsID,studentPhoto:!!r.studentPhoto,fatherPhoto:!!r.fatherPhoto,motherPhoto:!!r.motherPhoto,buyBook:!!r.buyBook},others:{shortSight:!!r.shortSight,longSight:!!r.longSight,allergic:!!r.allergic,autism:!!r.autism,languageProblem:!!r.languageProblem}};Object.keys(n).forEach(e=>{void 0===n[e]&&delete n[e]}),await s.findByIdAndUpdate(e.params.id,n),e.flash("success_msg","Student updated successfully!"),t.redirect("/students")}catch(r){e.flash("error_msg","Update failed."),t.redirect(`/students/${e.params.id}/edit`)}}),r.get("/students/:id/id-card",i.ensureAdmin,async(e,t)=>{try{const r=await s.findById(e.params.id);if(!r)return e.flash("error_msg","Student not found"),t.redirect("/students");t.render("id-card",{student:r})}catch(r){e.flash("error_msg","Failed to generate ID card"),t.redirect("/students")}}),r.get("/students/:id",i.ensureAdmin,async(e,t)=>{try{const r=await s.findById(e.params.id);if(!r)return e.flash("error_msg","Student not found"),t.redirect("/students");t.render("students/view",{student:r})}catch(r){e.flash("error_msg","Failed to load student details"),t.redirect("/students")}}),r.get("/dashboard",i.ensureAdmin,async(e,t)=>{try{const e=await s.find({}).sort({registrationDate:-1}),r={total:e.length,active:e.filter(e=>"active"===(e.status||"active")).length,pending:e.filter(e=>"pending"===e.status).length,graduated:e.filter(e=>"graduated"===e.status).length,transferred:e.filter(e=>"transferred"===e.status).length,male:e.filter(e=>"Male"===e.sex).length,female:e.filter(e=>"Female"===e.sex).length,quarter1:e.filter(e=>"Quarter 1"===e.quarter).length,quarter2:e.filter(e=>"Quarter 2"===e.quarter).length,quarter3:e.filter(e=>"Quarter 3"===e.quarter).length,quarter4:e.filter(e=>"Quarter 4"===e.quarter).length,thisMonth:e.filter(e=>{const t=new Date(e.registrationDate),r=new Date;return t.getMonth()===r.getMonth()&&t.getFullYear()===r.getFullYear()}).length,thisWeek:e.filter(e=>{const t=new Date(e.registrationDate),r=new Date;return t>=new Date(r.getTime()-6048e5)}).length},a=e.filter(e=>"active"===e.status||!e.status).slice(0,10),n=e.filter(e=>"pending"===e.status).slice(0,10),i={};e.filter(e=>"active"===e.status||!e.status).forEach(e=>{const t=e.registeredGrade||"Unspecified";i[t]=(i[t]||0)+1}),t.render("dashboard",{stats:r,recentStudents:a,pendingStudents:n,gradeStats:i,totalStudents:e.length})}catch(r){e.flash("error_msg","Error loading dashboard"),t.render("dashboard",{stats:{},recentStudents:[],gradeStats:{},totalStudents:0})}}),r.get("/students/export/csv",i.ensureAdmin,async(e,t)=>{try{const r={};e.query.name&&(r.fullName={$regex:e.query.name,$options:"i"}),e.query.grade&&(r.registeredGrade={$regex:e.query.grade,$options:"i"}),e.query.section&&(r.section={$regex:e.query.section,$options:"i"}),e.query.studentId&&(r.studentId={$regex:e.query.studentId,$options:"i"}),e.query.quarter&&(r.quarter=e.query.quarter),e.query.sex&&(r.sex=e.query.sex);const a=await s.find(r).sort({registrationDate:-1}),i=n.join(__dirname,"../public/exports/students.csv"),u=n.dirname(i);d.existsSync(u)||d.mkdirSync(u,{recursive:!0});const l=o({path:i,header:[{id:"studentId",title:"Student ID"},{id:"fullName",title:"Full Name"},{id:"sex",title:"Sex"},{id:"dob",title:"Date of Birth"},{id:"age",title:"Age"},{id:"registeredGrade",title:"Grade"},{id:"section",title:"Section"},{id:"quarter",title:"Quarter"},{id:"registrationDate",title:"Registration Date"},{id:"motherTongue",title:"Mother Tongue"},{id:"bloodType",title:"Blood Type"},{id:"weight",title:"Weight"},{id:"height",title:"Height"},{id:"handUse",title:"Hand Use"},{id:"addressCity",title:"City"},{id:"addressSubCity",title:"Sub City"},{id:"addressTabia",title:"Tabia"},{id:"addressKetena",title:"Ketena"},{id:"addressBlock",title:"Block"},{id:"addressNationality",title:"Nationality"},{id:"seriousSickness",title:"Serious Sickness"},{id:"fatherName",title:"Father Name"},{id:"fatherPhone",title:"Father Phone"},{id:"fatherOccupation",title:"Father Occupation"},{id:"motherName",title:"Mother Name"},{id:"motherPhone",title:"Mother Phone"},{id:"motherOccupation",title:"Mother Occupation"},{id:"guardianName",title:"Guardian Name"},{id:"guardianRelationship",title:"Guardian Relationship"},{id:"guardianPhone",title:"Guardian Phone"},{id:"transportSubCity",title:"Transport Sub City"},{id:"transportBus",title:"Transport Bus"},{id:"shortSight",title:"Short Sight"},{id:"longSight",title:"Long Sight"},{id:"allergic",title:"Allergic"},{id:"autism",title:"Autism"},{id:"languageProblem",title:"Language Problem"}]}),c=a.map(e=>{var t,r,s,a,n,i,o,d,u,l,c,g,m,h,p,f,y,S,v,b,_,P;return{studentId:e.studentId||"",fullName:e.fullName||"",sex:e.sex||"",dob:e.dob?new Date(e.dob).toLocaleDateString():"",age:e.age||"",registeredGrade:e.registeredGrade||"",section:e.section||"",quarter:e.quarter||"",registrationDate:e.registrationDate?new Date(e.registrationDate).toLocaleDateString():"",motherTongue:e.motherTongue||"",bloodType:e.bloodType||"",weight:e.weight||"",height:e.height||"",handUse:e.handUse||"",addressCity:(null===(t=e.address)||void 0===t?void 0:t.city)||"",addressSubCity:(null===(r=e.address)||void 0===r?void 0:r.subCity)||"",addressTabia:(null===(s=e.address)||void 0===s?void 0:s.tabia)||"",addressKetena:(null===(a=e.address)||void 0===a?void 0:a.ketena)||"",addressBlock:(null===(n=e.address)||void 0===n?void 0:n.block)||"",addressNationality:(null===(i=e.address)||void 0===i?void 0:i.nationality)||"",seriousSickness:e.seriousSickness||"",fatherName:(null===(o=e.father)||void 0===o?void 0:o.fullName)||"",fatherPhone:(null===(d=e.father)||void 0===d?void 0:d.phone)||"",fatherOccupation:(null===(u=e.father)||void 0===u?void 0:u.occupation)||"",motherName:(null===(l=e.mother)||void 0===l?void 0:l.fullName)||"",motherPhone:(null===(c=e.mother)||void 0===c?void 0:c.phone)||"",motherOccupation:(null===(g=e.mother)||void 0===g?void 0:g.occupation)||"",guardianName:(null===(m=e.guardian)||void 0===m?void 0:m.name)||"",guardianRelationship:(null===(h=e.guardian)||void 0===h?void 0:h.relationship)||"",guardianPhone:(null===(p=e.guardian)||void 0===p?void 0:p.phone)||"",transportSubCity:(null===(f=e.transport)||void 0===f?void 0:f.subCity)||"",transportBus:(null===(y=e.transport)||void 0===y?void 0:y.bus)||"",shortSight:null!==(S=e.others)&&void 0!==S&&S.shortSight?"Yes":"No",longSight:null!==(v=e.others)&&void 0!==v&&v.longSight?"Yes":"No",allergic:null!==(b=e.others)&&void 0!==b&&b.allergic?"Yes":"No",autism:null!==(_=e.others)&&void 0!==_&&_.autism?"Yes":"No",languageProblem:null!==(P=e.others)&&void 0!==P&&P.languageProblem?"Yes":"No"}});await l.writeRecords(c);const g=`students_export_${(new Date).toISOString().split("T")[0]}.csv`;t.setHeader("Content-Type","text/csv"),t.setHeader("Content-Disposition",`attachment; filename="${g}"`),t.download(i,g,r=>{r?(e.flash("error_msg","Failed to download CSV file"),t.redirect("/students")):setTimeout(()=>{d.existsSync(i)&&d.unlinkSync(i)},5e3)})}catch(r){e.flash("error_msg","Failed to export students to CSV"),t.redirect("/students")}}),r.get("/students/:id/print",i.ensureAdmin,async(e,t)=>{try{const r=await s.findById(e.params.id);if(!r)return e.flash("error_msg","Student not found"),t.redirect("/students");t.render("print-student",{student:r})}catch(r){e.flash("error_msg","Failed to load student for printing"),t.redirect("/students")}}),r.post("/students/print/id-cards",i.ensureAdmin,async(e,t)=>{try{const{studentIds:r}=e.body;let a;if(r&&r.length>0)a=await s.find({_id:{$in:r}});else{const t={};e.body.name&&(t.fullName={$regex:e.body.name,$options:"i"}),e.body.grade&&(t.registeredGrade={$regex:e.body.grade,$options:"i"}),e.body.section&&(t.section={$regex:e.body.section,$options:"i"}),e.body.quarter&&(t.quarter=e.body.quarter),e.body.sex&&(t.sex=e.body.sex),a=await s.find(t).sort({registrationDate:-1})}t.render("print-id-cards",{students:a})}catch(r){e.flash("error_msg","Failed to load students for printing"),t.redirect("/students")}}),r.post("/students/export/selected",i.ensureAdmin,async(e,t)=>{try{const{studentIds:r}=e.body,a=await s.find({_id:{$in:r}}).sort({registrationDate:-1}),n=generateCSV(a),i=`selected_students_${(new Date).toISOString().split("T")[0]}.csv`;t.setHeader("Content-Type","text/csv"),t.setHeader("Content-Disposition",`attachment; filename="${i}"`),t.send(n)}catch(r){e.flash("error_msg","Failed to export selected students"),t.redirect("/students")}}),r.post("/students/print/documents",i.ensureAdmin,async(e,t)=>{try{const{studentIds:r}=e.body,a=await s.find({_id:{$in:r}}).sort({registrationDate:-1});t.render("print-documents",{students:a})}catch(r){e.flash("error_msg","Failed to load students for document printing"),t.redirect("/students")}}),r.get("/students/export/pdf",i.ensureAdmin,async(e,t)=>{try{const r=c(e.query),a=await s.find(r).sort({registrationDate:-1});t.render("print-filtered-students",{students:a,filters:e.query})}catch(r){e.flash("error_msg","Failed to generate PDF export"),t.redirect("/students")}}),r.get("/students/print/filtered",i.ensureAdmin,async(e,t)=>{try{const r=c(e.query),a=await s.find(r).sort({registrationDate:-1});t.render("print-filtered-students",{students:a,filters:e.query})}catch(r){e.flash("error_msg","Failed to load filtered students for printing"),t.redirect("/students")}}),r.post("/students/:id/status",i.ensureAdmin,async(e,t)=>{try{const{status:r,reason:a,notes:n}=e.body,i=await s.findById(e.params.id);if(!i)return e.flash("error_msg","Student not found"),t.redirect("/students");await s.findByIdAndUpdate(e.params.id,{status:r,statusDate:new Date,statusReason:a||"",statusNotes:n||""},{new:!0});const o="pending"===r?"marked as pending":"graduated"===r?"marked as graduated":"transferred"===r?"marked as transferred":"reactivated";e.flash("success_msg",`Student ${i.fullName} has been ${o}`),t.redirect("/students")}catch(r){e.flash("error_msg","Failed to update student status"),t.redirect("/students")}}),r.get("/students/:id/status",i.ensureAdmin,async(e,t)=>{try{const r=await s.findById(e.params.id);if(!r)return e.flash("error_msg","Student not found"),t.redirect("/students");t.render("change-status",{student:r})}catch(r){e.flash("error_msg","Failed to load status change form"),t.redirect("/students")}}),r.get("/",(e,t)=>{t.redirect("/dashboard")}),module.exports=r},320:(module,e,t)=>{const r=t(252).Router(),{isAuthenticated:s,isAdmin:a}=t(997),n=t(623);r.get("/dashboard",s,a,n.getDashboard),r.get("/users",s,a,n.getUsers),r.post("/users",s,a,n.createUser),r.delete("/users/:id",s,a,n.deleteUser),module.exports=r},438:(module,e,t)=>{const r=t(37),s=new r.Schema({username:{type:String,required:!0,unique:!0},password:{type:String,required:!0}}),a=r.model("User",s);module.exports=a},461:module=>{"use strict";module.exports=require("multer")},571:(e,exports,t)=>{t(578);const r=t(438),s=t(729);exports.register=async(e,t)=>{},exports.list=async(e,t)=>{},exports.edit=async(e,t)=>{},exports.update=async(e,t)=>{},exports.ensureAdmin=(e,t,r)=>{if(e.session&&e.session.user&&e.session.username)return r();e.flash("error_msg","Please log in to view this page."),t.redirect("/login")},exports.loginForm=(e,t)=>{t.render("auth/login")},exports.login=async(e,t)=>{try{const{username:a,password:n}=e.body,i=await r.findOne({username:a});if(i&&await s.compare(n,i.password))return e.session.user=i,e.session.username=i.username,e.session.isAdmin=!0,e.session.userId=i._id,void e.session.save(r=>r?(e.flash("error_msg","Login failed. Please try again."),t.redirect("/login")):(e.flash("success_msg","Login successful!"),t.redirect("/dashboard")));e.flash("error_msg","Invalid username or password"),t.redirect("/login")}catch(r){e.flash("error_msg","Login failed. Please try again."),t.redirect("/login")}},exports.logout=(e,t)=>{e.session.destroy(()=>{t.redirect("/login")})}},578:(module,e,t)=>{const r=t(37),s=new r.Schema({fullName:String,phone:String,nationality:String,education:String,occupation:String,workplace:String,photo:String}),a=new r.Schema({name:String,relationship:String,phone:String}),n=new r.Schema({subCity:String,tabya:String,bus:String,startDate:Date}),i=new r.Schema({birthCertificate:String,transcript:String,parentsID:String,studentPhoto:String,fatherPhoto:String,motherPhoto:String,buyBook:Boolean}),o=new r.Schema({studentPic:String,parentPics:[String],fullName:String,sex:String,dob:Date,age:Number,formerGrade:String,registeredGrade:String,section:String,motherTongue:String,bloodType:String,weight:String,height:String,handUse:String,registrationDate:{type:Date,default:Date.now},quarter:{type:String,enum:["Quarter 1","Quarter 2","Quarter 3","Quarter 4"],default:"Quarter 1"},studentId:{type:String,unique:!0},status:{type:String,enum:["active","pending","graduated","transferred"],default:"active"},statusDate:{type:Date},statusReason:{type:String},statusNotes:{type:String},address:{city:String,subCity:String,tabia:String,ketena:String,block:String,nationality:String},seriousSickness:String,others:{shortSight:Boolean,longSight:Boolean,allergic:Boolean,autism:Boolean,languageProblem:Boolean},father:s,mother:s,guardian:a,transport:n,documents:i},{timestamps:!0});o.pre("save",async function(e){if(!this.studentId){const e=(new Date).getFullYear(),t=this.quarter?this.quarter.replace("Quarter ","Q"):"Q1",r=await this.constructor.findOne({studentId:new RegExp(`^STU${e}${t}`)}).sort({studentId:-1});let s=1;if(r&&r.studentId){s=parseInt(r.studentId.slice(-4))+1}this.studentId=`STU${e}${t}${s.toString().padStart(3,"0")}`}e()});const d=r.model("Student",o);module.exports=d},623:(e,exports,t)=>{const r=t(578),s=t(438);exports.getDashboard=async(e,t)=>{try{const e=await r.countDocuments(),a=await s.countDocuments();t.render("admin/dashboard",{title:"Admin Dashboard",studentCount:e,userCount:a})}catch(r){e.flash("error_msg","Error loading dashboard"),t.redirect("/")}},exports.getUsers=async(e,t)=>{try{const r=await s.find().sort({createdAt:-1});t.render("admin/users",{title:"Manage Users",users:r,userId:e.session.userId})}catch(r){e.flash("error_msg","Error fetching users"),t.redirect("/admin/dashboard")}},exports.createUser=async(e,t)=>{const{username:r,password:a,isAdmin:n}=e.body;try{if(await s.findOne({username:r}))return e.flash("error_msg","Username already exists"),t.redirect("/admin/users");const i=new s({username:r,password:a,isAdmin:"on"===n});await i.save(),e.flash("success_msg","User created successfully"),t.redirect("/admin/users")}catch(r){e.flash("error_msg","Error creating user"),t.redirect("/admin/users")}},exports.deleteUser=async(e,t)=>{try{if((await s.findById(e.params.id))._id.toString()===e.session.userId)return e.flash("error_msg","You cannot delete your own account"),t.redirect("/admin/users");await s.findByIdAndDelete(e.params.id),e.flash("success_msg","User deleted successfully"),t.redirect("/admin/users")}catch(r){e.flash("error_msg","Error deleting user"),t.redirect("/admin/users")}}},680:module=>{"use strict";module.exports=require("csv-writer")},692:module=>{"use strict";module.exports=require("method-override")},729:module=>{"use strict";module.exports=require("bcryptjs")},818:module=>{"use strict";module.exports=require("dotenv")},896:module=>{"use strict";module.exports=require("fs")},904:module=>{module.exports={systemName:process.env.SYSTEM_NAME||"SHEGE SRS",systemFullName:process.env.SYSTEM_FULL_NAME||"Student Registration System",systemVersion:process.env.SYSTEM_VERSION||"1.0.0",schoolName:process.env.SCHOOL_NAME||"SHEGE SRS",schoolAddress:process.env.SCHOOL_ADDRESS||"",schoolPhone:process.env.SCHOOL_PHONE||"",schoolEmail:process.env.SCHOOL_EMAIL||"",schoolWebsite:process.env.SCHOOL_WEBSITE||"",defaultLanguage:process.env.DEFAULT_LANGUAGE||"en",timezone:process.env.TIMEZONE||"UTC",dateFormat:process.env.DATE_FORMAT||"MM/DD/YYYY",primaryColor:process.env.PRIMARY_COLOR||"#ff6b35",secondaryColor:process.env.SECONDARY_COLOR||"#2c3e50",logoPath:process.env.LOGO_PATH||"/images/logo.png",enableRegistration:"false"!==process.env.ENABLE_REGISTRATION,enablePrintFeatures:"false"!==process.env.ENABLE_PRINT_FEATURES,enableExportFeatures:"false"!==process.env.ENABLE_EXPORT_FEATURES,maxFileSize:process.env.MAX_FILE_SIZE||"5MB",allowedImageTypes:["jpg","jpeg","png","gif"],allowedDocumentTypes:["pdf","doc","docx"],defaultPageSize:parseInt(process.env.DEFAULT_PAGE_SIZE)||20,maxPageSize:parseInt(process.env.MAX_PAGE_SIZE)||100,sessionTimeout:parseInt(process.env.SESSION_TIMEOUT)||36e5,maxLoginAttempts:parseInt(process.env.MAX_LOGIN_ATTEMPTS)||5,isDevelopment:!1,isProduction:!0,debugMode:"true"===process.env.DEBUG_MODE,dbName:process.env.DB_NAME||"student-registration",emailEnabled:"true"===process.env.EMAIL_ENABLED,smtpHost:process.env.SMTP_HOST||"",smtpPort:parseInt(process.env.SMTP_PORT)||587,smtpUser:process.env.SMTP_USER||"",smtpPass:process.env.SMTP_PASS||"",backupEnabled:"true"===process.env.BACKUP_ENABLED,backupInterval:process.env.BACKUP_INTERVAL||"24h",backupPath:process.env.BACKUP_PATH||"./backups",apiVersion:process.env.API_VERSION||"v1",apiRateLimit:parseInt(process.env.API_RATE_LIMIT)||100,logLevel:process.env.LOG_LEVEL||"info",logFile:process.env.LOG_FILE||"./logs/app.log",cacheEnabled:"false"!==process.env.CACHE_ENABLED,cacheTTL:parseInt(process.env.CACHE_TTL)||300,defaultReportFormat:process.env.DEFAULT_REPORT_FORMAT||"pdf",reportsPath:process.env.REPORTS_PATH||"./reports",studentIdPrefix:process.env.STUDENT_ID_PREFIX||"STU",studentIdLength:parseInt(process.env.STUDENT_ID_LENGTH)||8,currentAcademicYear:process.env.CURRENT_ACADEMIC_YEAR||(new Date).getFullYear().toString(),academicYearStart:process.env.ACADEMIC_YEAR_START||"09-01",academicYearEnd:process.env.ACADEMIC_YEAR_END||"08-31",quarters:["Quarter 1","Quarter 2","Quarter 3","Quarter 4"],gradeLevels:["Pre-K","Kindergarten","Grade 1","Grade 2","Grade 3","Grade 4","Grade 5","Grade 6","Grade 7","Grade 8","Grade 9","Grade 10","Grade 11","Grade 12"],defaultSections:["A","B","C","D","E"],studentStatuses:["active","pending","graduated","transferred"],medicalConditions:["shortSight","longSight","allergic","autism","languageProblem"],bloodTypes:["A+","A-","B+","B-","AB+","AB-","O+","O-"],guardianRelationships:["Father","Mother","Grandfather","Grandmother","Uncle","Aunt","Brother","Sister","Legal Guardian","Other"]}},925:module=>{"use strict";module.exports=require("connect-flash")},928:module=>{"use strict";module.exports=require("path")},977:module=>{"use strict";module.exports=require("express-session")},997:(module,e,t)=>{const r=t(438);module.exports={isAuthenticated:(e,t,r)=>{if(e.session&&e.session.userId)return r();e.flash("error_msg","Please log in to access this page"),t.redirect("/login")},isAdmin:async(e,t,s)=>{try{const a=await r.findById(e.session.userId);if(a&&a.isAdmin)return s();e.flash("error_msg","Access denied. Admin privileges required."),t.redirect("/login")}catch(r){e.flash("error_msg","An error occurred"),t.redirect("/login")}}}}},t={};function r(s){var a=t[s];if(void 0!==a)return a.exports;var module=t[s]={exports:{}};return e[s](module,module.exports,r),module.exports}r(818).config();const s=r(252),a=r(928),n=r(977),i=r(925),o=r(692),d=r(47),u=r(904),l=s();d(),l.use(s.urlencoded({extended:!0})),l.use(s.json()),l.use(s.static(a.join(__dirname,"public"))),l.use(o("_method")),l.use(n({secret:process.env.SESSION_SECRET||"your_strong_secret_key_here",resave:!1,saveUninitialized:!1,cookie:{httpOnly:!0,secure:!1,maxAge:864e5}})),l.use(i()),l.use((e,t,r)=>{t.locals.username=e.session.username,t.locals.isAdmin=e.session.isAdmin,t.locals.success_msg=e.flash("success_msg"),t.locals.error_msg=e.flash("error_msg"),t.locals.systemConfig=u,t.locals.systemName=u.systemName,t.locals.systemFullName=u.systemFullName,t.locals.schoolName=u.schoolName,r()}),l.set("view engine","ejs"),l.set("views",a.join(__dirname,"views")),l.use((e,t,r)=>{t.locals.success_msg=e.flash("success_msg"),t.locals.error_msg=e.flash("error_msg"),r()});const c=r(256);l.use("/",c);try{const e=r(320);l.use("/admin",e)}catch(e){}l.use((e,t)=>{t.status(404).render("error",{title:"Page Not Found",message:"The page you are looking for does not exist."})}),l.use((e,t,r,s)=>{r.status(500).render("error",{title:"Server Error",message:"Something went wrong on our end. Please try again later."})});const g=process.env.PORT||3e3;l.listen(g,()=>{})})();