#!/usr/bin/env node

const { spawn } = require('child_process');
const { exec } = require('child_process');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

console.log('🚀 SHEGE SRS Web Launcher');
console.log('========================');

// Setup admin user function
async function setupAdmin() {
    try {
        console.log('🔧 Setting up admin user...');

        // Connect to MongoDB
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/student-registration';
        await mongoose.connect(mongoUri);

        // Define User schema
        const userSchema = new mongoose.Schema({
            username: { type: String, required: true, unique: true },
            password: { type: String, required: true }
        });

        const User = mongoose.model('User', userSchema);

        // Check if admin user exists
        const adminExists = await User.findOne({ username: 'admin' });

        if (!adminExists) {
            console.log('👤 Creating admin user...');

            // Hash the password
            const hashedPassword = await bcrypt.hash('admin123', 10);

            // Create admin user
            const admin = new User({
                username: 'admin',
                password: hashedPassword
            });

            await admin.save();
            console.log('✅ Admin user created');
            console.log('📋 Login: admin / admin123');
        } else {
            console.log('✅ Admin user ready');
        }

        await mongoose.disconnect();

    } catch (error) {
        console.log('⚠️  Admin setup skipped:', error.message);
    }
}

// Start the application
async function startApp() {
    // Setup admin user first
    await setupAdmin();

    console.log('🌐 Starting web server...');

    // Start the server
    const server = spawn('node', ['app.bundle.js'], {
        stdio: 'pipe'
    });

    let serverStarted = false;

    server.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(output);

        if (output.includes('Server running on') && !serverStarted) {
            serverStarted = true;
            console.log('');
            console.log('🌐 Opening web browser...');
            console.log('📋 Login credentials:');
            console.log('   Username: admin');
            console.log('   Password: admin123');
            console.log('');

            // Open browser after a short delay
            setTimeout(() => {
                const url = 'http://localhost:3000';

                // Cross-platform browser opening
                const platform = process.platform;
                let command;

                if (platform === 'darwin') {
                    command = `open ${url}`;
                } else if (platform === 'win32') {
                    command = `start ${url}`;
                } else {
                    command = `xdg-open ${url}`;
                }

                exec(command, (error) => {
                    if (error) {
                        console.log(`Please open your browser and go to: ${url}`);
                    } else {
                        console.log(`✅ Browser opened to: ${url}`);
                    }
                });
            }, 2000);
        }
    });

    server.stderr.on('data', (data) => {
        console.error(data.toString());
    });

    server.on('close', (code) => {
        console.log(`Server process exited with code ${code}`);
    });

    // Handle Ctrl+C
    process.on('SIGINT', () => {
        console.log('\n🛑 Stopping server...');
        server.kill();
        process.exit();
    });
}

// Start the application
startApp().catch(error => {
    console.error('Failed to start application:', error);
    process.exit(1);
});
