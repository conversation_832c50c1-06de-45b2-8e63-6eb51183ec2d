<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student ID Card - <%= student.fullName %></title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --orange-primary: #ff6b35;
            --orange-secondary: #ff8c42;
            --orange-light: #ffb380;
            --gray-dark: #2c3e50;
            --gray-medium: #34495e;
            --gray-light: #7f8c8d;
            --gray-lighter: #bdc3c7;
            --white: #ffffff;
            --shadow: rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .print-actions {
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--orange-primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--orange-secondary);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: var(--gray-medium);
            color: white;
        }

        .btn-secondary:hover {
            background: var(--gray-dark);
            transform: translateY(-2px);
        }

        /* ID Card Container */
        .id-card-container {
            perspective: 1000px;
        }

        .id-card {
            width: 400px;
            height: 280px;
            background: linear-gradient(135deg, var(--orange-primary) 0%, var(--orange-secondary) 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 20px 40px var(--shadow),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            transform-style: preserve-3d;
            transition: transform 0.3s ease;
        }

        .id-card:hover {
            transform: rotateY(5deg) rotateX(5deg);
        }

        /* Background Pattern */
        .id-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -30%;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            z-index: 1;
        }

        .id-card::after {
            content: '';
            position: absolute;
            bottom: -30%;
            left: -20%;
            width: 150px;
            height: 150px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            z-index: 1;
        }

        /* Header Section */
        .card-header {
            background: var(--gray-dark);
            padding: 1rem;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .school-logo {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--orange-primary);
            font-size: 1.5rem;
            font-weight: 800;
            margin-bottom: 0.25rem;
        }

        .school-name {
            color: var(--white);
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Card Content */
        .card-content {
            padding: 1.25rem;
            display: flex;
            gap: 1rem;
            position: relative;
            z-index: 2;
            height: calc(100% - 80px);
        }

        /* Student Photo */
        .photo-section {
            flex-shrink: 0;
        }

        .student-photo {
            width: 100px;
            height: 120px;
            border-radius: 12px;
            border: 3px solid var(--white);
            object-fit: cover;
            background: var(--gray-lighter);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-medium);
            font-size: 2rem;
        }

        .photo-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background: var(--gray-lighter);
            color: var(--gray-medium);
            font-size: 2rem;
        }

        /* Student Information */
        .student-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .student-name {
            color: var(--white);
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1.2;
        }

        .student-details {
            flex: 1;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            color: var(--white);
            font-size: 0.75rem;
        }

        .detail-label {
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            min-width: 60px;
        }

        .detail-value {
            font-weight: 500;
            text-align: right;
            flex: 1;
        }

        /* Student ID Badge */
        .student-id-badge {
            background: var(--gray-dark);
            color: var(--orange-primary);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 700;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            margin-top: 0.5rem;
        }

        /* Address Section */
        .address-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem;
            border-radius: 8px;
            margin-top: 0.5rem;
        }

        .address-text {
            color: var(--white);
            font-size: 0.65rem;
            line-height: 1.3;
            font-weight: 500;
        }

        /* Back Page Styling */
        .id-card-back {
            width: 400px;
            height: 280px;
            background: linear-gradient(135deg, var(--gray-dark) 0%, var(--gray-medium) 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 20px 40px var(--shadow),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            transform-style: preserve-3d;
            transition: transform 0.3s ease;
            margin-top: 2rem;
        }

        .id-card-back:hover {
            transform: rotateY(-5deg) rotateX(5deg);
        }

        /* Back Page Background Pattern */
        .id-card-back::before {
            content: '';
            position: absolute;
            top: -30%;
            left: -20%;
            width: 150px;
            height: 150px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            z-index: 1;
        }

        .id-card-back::after {
            content: '';
            position: absolute;
            bottom: -20%;
            right: -30%;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 50%;
            z-index: 1;
        }

        /* Back Header */
        .back-header {
            background: var(--orange-primary);
            padding: 1rem;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .back-header h3 {
            color: white;
            font-size: 1rem;
            font-weight: 700;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Back Content */
        .back-content {
            padding: 1.5rem;
            position: relative;
            z-index: 2;
            height: calc(100% - 60px);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        /* Guardian Section */
        .guardian-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.25rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .guardian-title {
            color: var(--orange-primary);
            font-size: 1rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .guardian-info {
            color: white;
        }

        .guardian-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .guardian-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .guardian-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.8);
        }

        .guardian-value {
            font-size: 0.875rem;
            font-weight: 500;
            color: white;
            text-align: right;
        }

        /* Emergency Section */
        .emergency-section {
            background: rgba(255, 107, 53, 0.1);
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid rgba(255, 107, 53, 0.3);
        }

        .emergency-title {
            color: var(--orange-primary);
            font-size: 0.75rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .emergency-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.75rem;
            line-height: 1.4;
        }

        /* School Info Footer */
        .school-footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.75rem;
            margin-top: auto;
        }

        /* Print Styles */
        @media print {
            body {
                background: white !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            .print-actions {
                display: none !important;
            }
            .id-card, .id-card-back {
                margin: 0 !important;
                box-shadow: none !important;
                page-break-inside: avoid;
                page-break-after: always;
            }
            .id-card-back {
                page-break-after: auto;
            }
            .id-card:hover, .id-card-back:hover {
                transform: none !important;
            }
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            body {
                padding: 1rem;
            }

            .id-card {
                width: 100%;
                max-width: 380px;
            }

            .card-content {
                padding: 1rem;
                gap: 0.75rem;
            }

            .student-photo {
                width: 80px;
                height: 100px;
            }

            .student-name {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="print-actions">
        <button class="btn btn-primary" onclick="window.print()">
            <i class="fas fa-print"></i> Print ID Card
        </button>
        <button class="btn btn-secondary" onclick="window.history.back()">
            <i class="fas fa-arrow-left"></i> Back
        </button>
    </div>

    <div class="id-card-container">
        <div class="id-card">
            <!-- Header -->
            <div class="card-header">
                <div class="school-logo">
                    <i class="fas fa-graduation-cap"></i>
                    <%= systemName || 'SHEGE SRS' %>
                </div>
                <div class="school-name"><%= systemFullName || 'Student Registration System' %></div>
            </div>

            <!-- Card Content -->
            <div class="card-content">
                <!-- Photo Section -->
                <div class="photo-section">
                    <% if (student.studentPic) { %>
                        <img src="/uploads/<%= student.studentPic %>" alt="<%= student.fullName %>" class="student-photo">
                    <% } else { %>
                        <div class="student-photo">
                            <div class="photo-placeholder">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                    <% } %>
                </div>

                <!-- Student Information -->
                <div class="student-info">
                    <div class="student-name"><%= student.fullName %></div>

                    <div class="student-details">
                        <div class="detail-row">
                            <span class="detail-label">DOB:</span>
                            <span class="detail-value">
                                <%= student.dob ? new Date(student.dob).toLocaleDateString() : 'N/A' %>
                            </span>
                        </div>

                        <div class="detail-row">
                            <span class="detail-label">Entry:</span>
                            <span class="detail-value">
                                <%= student.registrationDate ? new Date(student.registrationDate).toLocaleDateString() : 'N/A' %>
                            </span>
                        </div>

                        <div class="detail-row">
                            <span class="detail-label">Class:</span>
                            <span class="detail-value"><%= student.registeredGrade || 'N/A' %></span>
                        </div>

                        <div class="detail-row">
                            <span class="detail-label">Section:</span>
                            <span class="detail-value"><%= student.section || 'N/A' %></span>
                        </div>

                        <% if (student.address) { %>
                            <div class="address-section">
                                <div class="address-text">
                                    <strong>Address:</strong><br>
                                    <%= student.address.subCity || '' %><%= student.address.subCity && student.address.tabya ? ', ' : '' %><%= student.address.tabya || '' %>
                                    <% if (student.address.houseNumber) { %>
                                        <br>House: <%= student.address.houseNumber %>
                                    <% } %>
                                </div>
                            </div>
                        <% } %>

                        <% if (student.guardian && (student.guardian.fullName || student.guardian.phone)) { %>
                            <div class="detail-row" style="margin-top: 0.75rem;">
                                <span class="detail-label">Guardian:</span>
                                <span class="detail-value" style="font-size: 0.65rem;">
                                    <%= student.guardian.fullName || 'N/A' %>
                                    <% if (student.guardian.phone) { %>
                                        <br><%= student.guardian.phone %>
                                    <% } %>
                                </span>
                            </div>
                        <% } %>
                    </div>

                    <div class="student-id-badge">
                        ID: <%= student.studentId || 'STU' + student._id.toString().slice(-6).toUpperCase() %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ID Card Back Page -->
    <div class="id-card-container">
        <div class="id-card-back">
            <!-- Back Header -->
            <div class="back-header">
                <h3>Emergency Contact Information</h3>
            </div>

            <!-- Back Content -->
            <div class="back-content">
                <!-- Guardian Information -->
                <div class="guardian-section">
                    <div class="guardian-title">
                        <i class="fas fa-shield-alt"></i>
                        Guardian Information
                    </div>
                    <div class="guardian-info">
                        <div class="guardian-row">
                            <span class="guardian-label">Name:</span>
                            <span class="guardian-value">
                                <%= student.guardian && student.guardian.name ? student.guardian.name : 'Not Provided' %>
                            </span>
                        </div>
                        <div class="guardian-row">
                            <span class="guardian-label">Relationship:</span>
                            <span class="guardian-value">
                                <%= student.guardian && student.guardian.relationship ? student.guardian.relationship : 'Not Specified' %>
                            </span>
                        </div>
                        <div class="guardian-row">
                            <span class="guardian-label">Phone:</span>
                            <span class="guardian-value">
                                <%= student.guardian && student.guardian.phone ? student.guardian.phone : 'Not Provided' %>
                            </span>
                        </div>

                        <!-- Father Contact -->
                        <% if (student.father && student.father.phone) { %>
                            <div class="guardian-row">
                                <span class="guardian-label">Father:</span>
                                <span class="guardian-value">
                                    <%= student.father.phone %>
                                </span>
                            </div>
                        <% } %>

                        <!-- Mother Contact -->
                        <% if (student.mother && student.mother.phone) { %>
                            <div class="guardian-row">
                                <span class="guardian-label">Mother:</span>
                                <span class="guardian-value">
                                    <%= student.mother.phone %>
                                </span>
                            </div>
                        <% } %>
                    </div>
                </div>

                <!-- Emergency Instructions -->
                <div class="emergency-section">
                    <div class="emergency-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Emergency Instructions
                    </div>
                    <div class="emergency-text">
                        In case of emergency, please contact the guardian immediately.
                        If guardian is unavailable, contact the school administration.
                        <br><br>
                        <strong>School Emergency Line:</strong> <%= systemConfig.schoolPhone || 'Contact School' %>
                    </div>
                </div>

                <!-- School Footer -->
                <div class="school-footer">
                    <div style="margin-bottom: 0.5rem;">
                        <strong><%= systemName || 'SHEGE SRS' %></strong>
                    </div>
                    <div style="font-size: 0.65rem;">
                        <%= systemConfig.schoolAddress || 'School Address' %>
                    </div>
                    <div style="font-size: 0.65rem; margin-top: 0.25rem;">
                        Valid for Academic Year <%= new Date().getFullYear() %>-<%= new Date().getFullYear() + 1 %>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
