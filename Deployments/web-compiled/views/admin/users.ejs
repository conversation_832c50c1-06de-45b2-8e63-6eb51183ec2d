<%- include('../../partials/header', { title: 'Manage Users' }) %>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Manage Users</h1>
        <a href="/admin/dashboard" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Dashboard
        </a>
    </div>

    <!-- Add User Form -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Add New User</h5>
        </div>
        <div class="card-body">
            <form action="/admin/users" method="POST">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="col-md-4">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required minlength="6">
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mt-4 pt-2">
                            <input class="form-check-input" type="checkbox" id="isAdmin" name="isAdmin">
                            <label class="form-check-label" for="isAdmin">
                                Admin User
                            </label>
                        </div>
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">Add User</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">User List</h5>
        </div>
        <div class="card-body">
            <% if (users && users.length > 0) { %>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Role</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% users.forEach(user => { %>
                                <tr>
                                    <td>
                                        <%= user.username %>
                                        <% if (user._id.toString() === userId) { %>
                                            <span class="badge bg-info">You</span>
                                        <% } %>
                                    </td>
                                    <td>
                                        <% if (user.isAdmin) { %>
                                            <span class="badge bg-primary">Admin</span>
                                        <% } else { %>
                                            <span class="badge bg-secondary">User</span>
                                        <% } %>
                                    </td>
                                    <td><%= new Date(user.createdAt).toLocaleDateString() %></td>
                                    <td>
                                        <% if (user._id.toString() !== userId) { %>
                                            <form action="/admin/users/<%= user._id %>?_method=DELETE" method="POST" class="d-inline">
                                                <button type="submit" class="btn btn-sm btn-danger" 
                                                    onclick="return confirm('Are you sure you want to delete this user?')">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </form>
                                        <% } else { %>
                                            <span class="text-muted">Current user</span>
                                        <% } %>
                                    </td>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            <% } else { %>
                <div class="alert alert-info">No users found.</div>
            <% } %>
        </div>
    </div>
</div>

<%- include('../../partials/footer') %>
