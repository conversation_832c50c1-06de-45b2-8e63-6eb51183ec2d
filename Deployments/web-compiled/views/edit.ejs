<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Student - <%= student.fullName %></title>
    <link rel="stylesheet" href="/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">
                <h1><i class="fas fa-graduation-cap"></i> Student Registration System</h1>
            </div>
            <nav>
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/register"><i class="fas fa-user-plus"></i> Register Student</a>
                <a href="/students"><i class="fas fa-users"></i> View Students</a>
                <a href="/login"><i class="fas fa-sign-in-alt"></i> Login</a>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <h1><i class="fas fa-edit"></i> Edit Student</h1>
                <a href="/students" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
            </div>

            <form action="/students/<%= student._id %>" method="POST" enctype="multipart/form-data">
                <div class="form-section">
                    <h2><i class="fas fa-clipboard-list"></i> Registration Information</h2>
                    <div class="form-group">
                        <label><i class="fas fa-calendar-alt"></i> Registration Date</label>
                        <input type="date" name="registrationDate" value="<%= student.registrationDate ? student.registrationDate.toISOString().substring(0,10) : '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-calendar-check"></i> Quarter/Term</label>
                        <select name="quarter">
                            <option value="">Select Quarter/Term</option>
                            <option value="Quarter 1" <%= student.quarter === 'Quarter 1' ? 'selected' : '' %>>Quarter 1</option>
                            <option value="Quarter 2" <%= student.quarter === 'Quarter 2' ? 'selected' : '' %>>Quarter 2</option>
                            <option value="Quarter 3" <%= student.quarter === 'Quarter 3' ? 'selected' : '' %>>Quarter 3</option>
                            <option value="Quarter 4" <%= student.quarter === 'Quarter 4' ? 'selected' : '' %>>Quarter 4</option>
                        </select>
                    </div>
                    <% if (student.studentId) { %>
                        <div class="form-group">
                            <label><i class="fas fa-id-card"></i> Student ID</label>
                            <input type="text" value="<%= student.studentId %>" readonly style="background: #f8f9fa; color: #666;">
                        </div>
                    <% } %>
                </div>

                <div class="form-section">
                    <h2><i class="fas fa-user"></i> Basic Information</h2>
                    <div class="form-group">
                        <label><i class="fas fa-camera"></i> Student Photo</label>
                        <input type="file" name="studentPic" accept="image/*">
                        <% if (student.studentPic) { %>
                            <p style="color: #666; font-size: 0.9rem; margin-top: 0.5rem;">
                                Current photo: <%= student.studentPic %>
                            </p>
                        <% } %>
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-user"></i> Full Name</label>
                        <input type="text" name="fullName" value="<%= student.fullName %>" required>
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-venus-mars"></i> Sex</label>
                        <select name="sex">
                            <option value="">Select Sex</option>
                            <option value="Male" <%= student.sex === 'Male' ? 'selected' : '' %>>Male</option>
                            <option value="Female" <%= student.sex === 'Female' ? 'selected' : '' %>>Female</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-calendar"></i> Date of Birth</label>
                        <input type="date" name="dob" value="<%= student.dob ? student.dob.toISOString().substring(0,10) : '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-birthday-cake"></i> Age</label>
                        <input type="number" name="age" value="<%= student.age || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-layer-group"></i> Former Grade</label>
                        <input type="text" name="formerGrade" value="<%= student.formerGrade || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-layer-group"></i> Registered for Grade</label>
                        <input type="text" name="registeredGrade" value="<%= student.registeredGrade || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-users"></i> Section</label>
                        <input type="text" name="section" value="<%= student.section || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-language"></i> Mother Tongue/First Language</label>
                        <input type="text" name="motherTongue" value="<%= student.motherTongue || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-tint"></i> Blood Type</label>
                        <input type="text" name="bloodType" value="<%= student.bloodType || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-weight"></i> Weight</label>
                        <input type="text" name="weight" value="<%= student.weight || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-ruler-vertical"></i> Height</label>
                        <input type="text" name="height" value="<%= student.height || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-hand-paper"></i> Hand Use</label>
                        <select name="handUse">
                            <option value="">Select hand preference</option>
                            <option value="Right" <%= student.handUse === 'Right' ? 'selected' : '' %>>Right</option>
                            <option value="Left" <%= student.handUse === 'Left' ? 'selected' : '' %>>Left</option>
                        </select>
                    </div>
                </div>

                <div class="form-section">
                    <h2><i class="fas fa-map-marker-alt"></i> Address Information</h2>
                    <div class="form-group">
                        <label><i class="fas fa-city"></i> City</label>
                        <input type="text" name="address.city" value="<%= student.address?.city || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-building"></i> Sub City</label>
                        <input type="text" name="address.subCity" value="<%= student.address?.subCity || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-map"></i> Tabia</label>
                        <input type="text" name="address.tabia" value="<%= student.address?.tabia || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-home"></i> Ketena</label>
                        <input type="text" name="address.ketena" value="<%= student.address?.ketena || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-building"></i> Block</label>
                        <input type="text" name="address.block" value="<%= student.address?.block || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-flag"></i> Nationality</label>
                        <input type="text" name="address.nationality" value="<%= student.address?.nationality || '' %>">
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-notes-medical"></i> Any Serious Sickness (Optional)</label>
                        <input type="text" name="seriousSickness" value="<%= student.seriousSickness || '' %>">
                    </div>
                </div>

                <fieldset>
                    <legend><i class="fas fa-plus-circle"></i> Additional Information</legend>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="shortSight" <%= student.others?.shortSight ? 'checked' : '' %>> <i class="fas fa-eye"></i> Short Sight</label>
                        <label><input type="checkbox" name="longSight" <%= student.others?.longSight ? 'checked' : '' %>> <i class="fas fa-eye"></i> Long Sight</label>
                        <label><input type="checkbox" name="allergic" <%= student.others?.allergic ? 'checked' : '' %>> <i class="fas fa-allergies"></i> Allergic</label>
                        <label><input type="checkbox" name="autism" <%= student.others?.autism ? 'checked' : '' %>> <i class="fas fa-brain"></i> Autism</label>
                        <label><input type="checkbox" name="languageProblem" <%= student.others?.languageProblem ? 'checked' : '' %>> <i class="fas fa-comment-slash"></i> Language Problem</label>
                    </div>
                </fieldset>

                <div class="form-actions">
                    <button type="submit"><i class="fas fa-save"></i> Update Student</button>
                    <a href="/students" class="btn btn-secondary"><i class="fas fa-times"></i> Cancel</a>
                </div>
            </form>
        </div>
    </main>
</body>
</html>