<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Student Data - <%= student.fullName %></title>
    <link rel="stylesheet" href="/styles.css">
    <style>
        @media print {
            body {
                background: white !important;
                font-size: 11px;
                line-height: 1.3;
                margin: 0 !important;
                padding: 0 !important;
            }
            .no-print { display: none !important; }

            /* Front Page Styles */
            .front-page {
                height: 100vh;
                page-break-after: always;
                padding: 15px;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            /* Back Page Styles */
            .back-page {
                height: 100vh;
                padding: 15px;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            .print-header {
                text-align: center;
                border-bottom: 2px solid #000;
                padding-bottom: 10px;
                margin-bottom: 15px;
            }

            .print-header h1 {
                font-size: 16px !important;
                margin: 0 0 3px 0 !important;
                font-weight: bold;
            }

            .print-header p {
                margin: 1px 0 !important;
                font-size: 9px;
            }

            .student-header {
                display: flex;
                align-items: flex-start;
                margin-bottom: 15px;
                border: 1px solid #000;
                padding: 10px;
                background: #f9f9f9;
            }

            .student-photo-container {
                margin-right: 15px;
                flex-shrink: 0;
            }

            .student-photo-print {
                width: 80px;
                height: 100px;
                border: 1px solid #000;
                object-fit: cover;
                display: block;
            }

            .student-basic-info {
                flex: 1;
            }

            .student-name {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
                text-transform: uppercase;
            }

            .basic-details {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 8px;
                font-size: 10px;
            }

            .print-section {
                margin-bottom: 10px;
                page-break-inside: avoid;
            }

            .print-section h3 {
                background: #000 !important;
                color: white !important;
                padding: 4px 8px !important;
                margin: 0 0 8px 0 !important;
                font-size: 10px !important;
                text-transform: uppercase;
                letter-spacing: 0.3px;
            }

            .print-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 6px;
                margin-bottom: 8px;
            }

            .print-grid-three {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 6px;
                margin-bottom: 8px;
            }

            .print-field {
                margin-bottom: 4px;
                border-bottom: 1px dotted #999;
                padding-bottom: 1px;
                font-size: 9px;
            }

            .print-label {
                font-weight: bold;
                display: inline-block;
                width: 80px;
                font-size: 9px;
                text-transform: uppercase;
            }

            .print-value {
                font-size: 10px;
            }

            .parent-section {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                margin-bottom: 15px;
            }

            .parent-info {
                border: 1px solid #ccc;
                padding: 10px;
                background: #fafafa;
            }

            .parent-title {
                font-weight: bold;
                font-size: 11px;
                margin-bottom: 8px;
                text-align: center;
                background: #e0e0e0;
                padding: 4px;
                text-transform: uppercase;
            }

            .conditions-list {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                margin-top: 5px;
            }

            .condition-tag {
                background: #e0e0e0;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 8px;
                border: 1px solid #999;
            }

            .documents-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
                margin-bottom: 10px;
            }

            .document-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 6px 8px;
                border: 1px solid #ccc;
                font-size: 9px;
                background: #f9f9f9;
            }

            .document-label {
                font-weight: 500;
                flex: 1;
            }

            .document-status {
                font-weight: bold;
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 3px;
                min-width: 40px;
                text-align: center;
            }

            .document-status.yes {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }

            .document-status.no {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
        }
        
        .print-container {
            max-width: 800px;
            margin: 2rem auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .print-actions {
            text-align: center;
            margin-bottom: 2rem;
            gap: 1rem;
            display: flex;
            justify-content: center;
        }
        
        .print-header {
            text-align: center;
            border-bottom: 2px solid #2c3e50;
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }
        
        .print-header h1 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .print-header p {
            color: #666;
            margin: 0;
        }
        
        .print-section {
            margin-bottom: 2rem;
        }
        
        .print-section h3 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 8px;
        }
        
        .print-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .print-field {
            margin-bottom: 0.8rem;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .print-label {
            font-weight: 600;
            color: #2c3e50;
            display: block;
            margin-bottom: 0.2rem;
        }
        
        .print-value {
            color: #666;
        }
        
        .student-photo-container {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .student-photo-print {
            width: 150px;
            height: 150px;
            border: 3px solid #2c3e50;
            border-radius: 10px;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <!-- Print Actions (No Print) -->
    <div class="print-actions no-print">
        <button onclick="window.print()" class="btn">
            <i class="fas fa-print"></i> Print Student Data (Front & Back)
        </button>
        <button onclick="window.history.back()" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back
        </button>
    </div>

    <!-- FRONT PAGE -->
    <div class="front-page">
        <div class="print-header">
            <h1>SHEGE SRS</h1>
            <p>Student Information Report</p>
            <p>Generated on: <%= new Date().toLocaleDateString() %> | Page 1 of 2</p>
        </div>

        <!-- Student Header with Photo and Basic Info -->
        <div class="student-header">
            <div class="student-photo-container">
                <% if (student.studentPic) { %>
                    <img src="/uploads/<%= student.studentPic %>" alt="<%= student.fullName %>" class="student-photo-print">
                <% } else { %>
                    <div class="student-photo-print" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #999;">
                        NO PHOTO
                    </div>
                <% } %>
            </div>
            <div class="student-basic-info">
                <div class="student-name"><%= student.fullName %></div>
                <div class="basic-details">
                    <div><strong>Student ID:</strong> <%= student.studentId || 'N/A' %></div>
                    <div><strong>Sex:</strong> <%= student.sex || 'N/A' %></div>
                    <div><strong>Date of Birth:</strong> <%= student.dob ? new Date(student.dob).toLocaleDateString() : 'N/A' %></div>
                    <div><strong>Age:</strong> <%= student.age || 'N/A' %></div>
                    <div><strong>Blood Type:</strong> <%= student.bloodType || 'N/A' %></div>
                    <div><strong>Hand Use:</strong> <%= student.handUse || 'N/A' %></div>
                </div>
            </div>
        </div>
        
        <!-- Academic Information -->
        <div class="print-section">
            <h3>Academic Information</h3>
            <div class="print-grid">
                <div class="print-field">
                    <span class="print-label">Former Grade:</span>
                    <span class="print-value"><%= student.formerGrade || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Registered Grade:</span>
                    <span class="print-value"><%= student.registeredGrade || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Section:</span>
                    <span class="print-value"><%= student.section || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Quarter:</span>
                    <span class="print-value"><%= student.quarter || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Registration Date:</span>
                    <span class="print-value"><%= student.registrationDate ? new Date(student.registrationDate).toLocaleDateString() : 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Mother Tongue:</span>
                    <span class="print-value"><%= student.motherTongue || 'N/A' %></span>
                </div>
            </div>
        </div>

        <!-- Physical Information -->
        <div class="print-section">
            <h3>Physical Information</h3>
            <div class="print-grid">
                <div class="print-field">
                    <span class="print-label">Weight:</span>
                    <span class="print-value"><%= student.weight || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Height:</span>
                    <span class="print-value"><%= student.height || 'N/A' %></span>
                </div>
            </div>
        </div>
        
        <!-- Address Information -->
        <% if (student.address) { %>
            <div class="print-section">
                <h3>Address Information</h3>
                <div class="print-grid-three">
                    <div class="print-field">
                        <span class="print-label">City:</span>
                        <span class="print-value"><%= student.address.city || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Sub City:</span>
                        <span class="print-value"><%= student.address.subCity || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Tabia:</span>
                        <span class="print-value"><%= student.address.tabia || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Ketena:</span>
                        <span class="print-value"><%= student.address.ketena || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Block:</span>
                        <span class="print-value"><%= student.address.block || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Nationality:</span>
                        <span class="print-value"><%= student.address.nationality || 'N/A' %></span>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Medical Information -->
        <div class="print-section">
            <h3>Medical Information</h3>
            <% if (student.seriousSickness) { %>
                <div class="print-field">
                    <span class="print-label">Serious Sickness:</span>
                    <span class="print-value"><%= student.seriousSickness %></span>
                </div>
            <% } %>

            <% if (student.others && (student.others.shortSight || student.others.longSight || student.others.allergic || student.others.autism || student.others.languageProblem)) { %>
                <div class="print-field">
                    <span class="print-label">Special Conditions:</span>
                    <div class="conditions-list">
                        <% if (student.others.shortSight) { %><span class="condition-tag">Short Sight</span><% } %>
                        <% if (student.others.longSight) { %><span class="condition-tag">Long Sight</span><% } %>
                        <% if (student.others.allergic) { %><span class="condition-tag">Allergic</span><% } %>
                        <% if (student.others.autism) { %><span class="condition-tag">Autism</span><% } %>
                        <% if (student.others.languageProblem) { %><span class="condition-tag">Language Problem</span><% } %>
                    </div>
                </div>
            <% } %>
        </div>
    </div>
        
    <!-- BACK PAGE -->
    <div class="back-page">
        <div class="print-header">
            <h1>SHEGE SRS</h1>
            <p>Student Information Report - <%= student.fullName %></p>
            <p>Student ID: <%= student.studentId || 'N/A' %> | Page 2 of 2</p>
        </div>
        
        <!-- Parent Information -->
        <div class="print-section">
            <h3>Parent Information</h3>
            <div class="parent-section">
                <!-- Father -->
                <div class="parent-info">
                    <div class="parent-title">Father's Information</div>
                    <div class="print-field">
                        <span class="print-label">Name:</span>
                        <span class="print-value"><%= student.father?.fullName || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Phone:</span>
                        <span class="print-value"><%= student.father?.phone || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Nationality:</span>
                        <span class="print-value"><%= student.father?.nationality || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Education:</span>
                        <span class="print-value"><%= student.father?.education || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Occupation:</span>
                        <span class="print-value"><%= student.father?.occupation || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Workplace:</span>
                        <span class="print-value"><%= student.father?.workplace || 'N/A' %></span>
                    </div>
                </div>

                <!-- Mother -->
                <div class="parent-info">
                    <div class="parent-title">Mother's Information</div>
                    <div class="print-field">
                        <span class="print-label">Name:</span>
                        <span class="print-value"><%= student.mother?.fullName || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Phone:</span>
                        <span class="print-value"><%= student.mother?.phone || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Nationality:</span>
                        <span class="print-value"><%= student.mother?.nationality || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Education:</span>
                        <span class="print-value"><%= student.mother?.education || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Occupation:</span>
                        <span class="print-value"><%= student.mother?.occupation || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Workplace:</span>
                        <span class="print-value"><%= student.mother?.workplace || 'N/A' %></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Guardian Information -->
        <% if (student.guardian?.name) { %>
            <div class="print-section">
                <h3>Guardian Information</h3>
                <div class="print-grid-three">
                    <div class="print-field">
                        <span class="print-label">Name:</span>
                        <span class="print-value"><%= student.guardian.name %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Relationship:</span>
                        <span class="print-value"><%= student.guardian.relationship %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Phone:</span>
                        <span class="print-value"><%= student.guardian.phone %></span>
                    </div>
                </div>
            </div>
        <% } %>
        
        <!-- Transportation -->
        <% if (student.transport && (student.transport.subCity || student.transport.bus)) { %>
            <div class="print-section">
                <h3>Transportation Information</h3>
                <div class="print-grid">
                    <div class="print-field">
                        <span class="print-label">Sub City:</span>
                        <span class="print-value"><%= student.transport.subCity || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Tabya:</span>
                        <span class="print-value"><%= student.transport.tabya || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Bus:</span>
                        <span class="print-value"><%= student.transport.bus || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Start Date:</span>
                        <span class="print-value"><%= student.transport.startDate ? new Date(student.transport.startDate).toLocaleDateString() : 'N/A' %></span>
                    </div>
                </div>
            </div>
        <% } %>
        
        <!-- Documents Status -->
        <div class="print-section">
            <h3>Documents & Requirements Status</h3>
            <div class="documents-grid">
                <div class="document-item">
                    <div class="document-label">Birth Certificate</div>
                    <div class="document-status <%= (student.documents && student.documents.birthCertificate) ? 'yes' : 'no' %>">
                        <%= (student.documents && student.documents.birthCertificate) ? '✓ YES' : '✗ NO' %>
                    </div>
                </div>
                <div class="document-item">
                    <div class="document-label">Academic Transcript</div>
                    <div class="document-status <%= (student.documents && student.documents.transcript) ? 'yes' : 'no' %>">
                        <%= (student.documents && student.documents.transcript) ? '✓ YES' : '✗ NO' %>
                    </div>
                </div>
                <div class="document-item">
                    <div class="document-label">Parents ID Documents</div>
                    <div class="document-status <%= (student.documents && student.documents.parentsID) ? 'yes' : 'no' %>">
                        <%= (student.documents && student.documents.parentsID) ? '✓ YES' : '✗ NO' %>
                    </div>
                </div>
                <div class="document-item">
                    <div class="document-label">School Books Purchase</div>
                    <div class="document-status <%= (student.documents && student.documents.buyBook) ? 'yes' : 'no' %>">
                        <%= (student.documents && student.documents.buyBook) ? '✓ YES' : '✗ NO' %>
                    </div>
                </div>
                <div class="document-item">
                    <div class="document-label">Student Photo</div>
                    <div class="document-status <%= student.studentPic ? 'yes' : 'no' %>">
                        <%= student.studentPic ? '✓ YES' : '✗ NO' %>
                    </div>
                </div>
                <div class="document-item">
                    <div class="document-label">Parent Photos</div>
                    <div class="document-status <%= (student.father && student.father.photo) || (student.mother && student.mother.photo) ? 'yes' : 'no' %>">
                        <%= (student.father && student.father.photo) || (student.mother && student.mother.photo) ? '✓ YES' : '✗ NO' %>
                    </div>
                </div>
            </div>
        </div>

        <!-- Signatures Section -->
        <div class="print-section" style="margin-top: auto;">
            <h3>Signatures & Verification</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-top: 15px;">
                <div style="text-align: center;">
                    <div style="border-bottom: 1px solid #000; height: 25px; margin-bottom: 3px;"></div>
                    <div style="font-size: 8px; font-weight: bold;">REGISTRAR SIGNATURE</div>
                    <div style="font-size: 7px;">Date: ___________</div>
                </div>
                <div style="text-align: center;">
                    <div style="border-bottom: 1px solid #000; height: 25px; margin-bottom: 3px;"></div>
                    <div style="font-size: 8px; font-weight: bold;">PARENT/GUARDIAN SIGNATURE</div>
                    <div style="font-size: 7px;">Date: ___________</div>
                </div>
                <div style="text-align: center;">
                    <div style="border-bottom: 1px solid #000; height: 25px; margin-bottom: 3px;"></div>
                    <div style="font-size: 8px; font-weight: bold;">SCHOOL STAMP</div>
                    <div style="font-size: 7px;">Date: ___________</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
