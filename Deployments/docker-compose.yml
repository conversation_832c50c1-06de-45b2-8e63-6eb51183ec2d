version: '3.8'

services:
  # SHEGE SRS Web Application
  shege-srs:
    build: .
    container_name: shege-srs-web
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/student-registration
      - MONGODB_EXTERNAL=true
      - SYSTEM_NAME=SHEGE SRS
      - SESSION_SECRET=your-secure-session-secret-here
      - PORT=3000
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    depends_on:
      - mongodb
    restart: unless-stopped
    networks:
      - shege-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: shege-srs-db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=secure-password-here
      - MONG<PERSON>_INITDB_DATABASE=student-registration
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    ports:
      - "27017:27017"
    restart: unless-stopped
    networks:
      - shege-network
    command: mongod --auth

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: shege-srs-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - shege-srs
    restart: unless-stopped
    networks:
      - shege-network

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  uploads:
    driver: local
  logs:
    driver: local

networks:
  shege-network:
    driver: bridge
