#!/usr/bin/env node

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

async function debugLogin() {
    try {
        console.log('🔍 Debugging login process...');
        
        // Connect to MongoDB
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/student-registration';
        console.log('Connecting to:', mongoUri);
        await mongoose.connect(mongoUri);
        console.log('✅ Connected to MongoDB');
        
        // Define User schema (same as in the app)
        const userSchema = new mongoose.Schema({
            username: { type: String, required: true, unique: true },
            password: { type: String, required: true }
        });
        
        const User = mongoose.model('User', userSchema);
        
        // Test the login process
        console.log('🔐 Testing login process...');
        
        const username = 'admin';
        const password = 'admin123';
        
        console.log('1. Looking for user:', username);
        const user = await User.findOne({ username });
        
        if (!user) {
            console.log('❌ User not found');
            return;
        }
        
        console.log('✅ User found:', user._id);
        console.log('📋 User data:', {
            id: user._id,
            username: user.username,
            passwordHash: user.password.substring(0, 20) + '...'
        });
        
        console.log('2. Testing password comparison...');
        const passwordMatch = await bcrypt.compare(password, user.password);
        console.log('🔐 Password match:', passwordMatch);
        
        if (passwordMatch) {
            console.log('✅ Login would succeed');
            console.log('📋 Session data that would be set:');
            console.log('   - req.session.user:', user._id);
            console.log('   - req.session.username:', user.username);
            console.log('   - req.session.isAdmin:', true);
            console.log('   - req.session.userId:', user._id);
        } else {
            console.log('❌ Login would fail - password mismatch');
        }
        
        await mongoose.disconnect();
        console.log('📡 Disconnected from MongoDB');
        
    } catch (error) {
        console.error('❌ Debug failed:', error);
        process.exit(1);
    }
}

// Run debug
debugLogin();
