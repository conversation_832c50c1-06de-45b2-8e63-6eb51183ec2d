SHEGE SRS Portable Applications
==============================

This package contains portable versions of SHEGE SRS for different operating systems.

Files:
- SHEGE-SRS-Linux.AppImage: For Linux systems (Ubuntu, CentOS, Debian, etc.)
- SHEGE-SRS-Windows.exe: For Windows systems (Windows 10, 11)

Prerequisites:
- MongoDB must be installed and running on the target system
- Port 3000 must be available

Installation:
1. Download the appropriate file for your operating system
2. Linux: chmod +x SHEGE-SRS-Linux.AppImage && ./SHEGE-SRS-Linux.AppImage
3. Windows: Double-click SHEGE-SRS-Windows.exe

First Login:
- Username: admin
- Password: admin123
- Change password immediately after first login

Support:
- Check that MongoDB is running: sudo systemctl status mongod (Linux)
- Ensure port 3000 is available: netstat -tlnp | grep :3000
- Application loads at: http://localhost:3000

Built on: Sat Jul 12 05:33:39 PM EAT 2025
Version: 1.0.0
