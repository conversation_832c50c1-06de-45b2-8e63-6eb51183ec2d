# SHEGE SRS Desktop Application Build Guide

## Prerequisites

1. **Node.js** (v16 or higher)
2. **npm** (comes with Node.js)
3. **Git** (for version control)

## Installation Steps

### 1. Install Dependencies

```bash
# Install all dependencies including Electron
npm install
```

### 2. Add Application Icons

Create or add the following icon files to the `assets/` directory:
- `icon.png` (512x512 PNG for Linux)
- `icon.ico` (256x256 ICO for Windows)

### 3. Development Mode

```bash
# Run the web app and Electron together
npm run electron-dev
```

This will:
- Start the Express server on port 3000
- Launch Electron window automatically
- Enable hot reload for development

### 4. Production Mode

```bash
# Run Electron with production server
npm run electron
```

## Building Distributables

### Build for Windows

```bash
# Build Windows installer and portable
npm run build-win
```

Creates:
- `dist/SHEGE SRS Setup 1.0.0.exe` (Installer)
- `dist/SHEGE SRS 1.0.0.exe` (Portable)

### Build for Linux

```bash
# Build Linux packages
npm run build-linux
```

Creates:
- `dist/SHEGE SRS-1.0.0.AppImage` (Universal Linux)
- `dist/shege-srs-desktop_1.0.0_amd64.deb` (Debian/Ubuntu)
- `dist/shege-srs-desktop-1.0.0.x86_64.rpm` (RedHat/CentOS)

### Build for Both Platforms

```bash
# Build for Windows and Linux
npm run build-all
```

## Distribution Files

After building, you'll find the following in the `dist/` directory:

### Windows
- **Installer**: `SHEGE SRS Setup 1.0.0.exe`
  - Full installer with uninstaller
  - Creates desktop and start menu shortcuts
  - Recommended for most users

- **Portable**: `SHEGE SRS 1.0.0.exe`
  - Single executable file
  - No installation required
  - Good for USB drives or temporary use

### Linux
- **AppImage**: `SHEGE SRS-1.0.0.AppImage`
  - Universal Linux format
  - Works on most distributions
  - Make executable and run directly

- **DEB Package**: `shege-srs-desktop_1.0.0_amd64.deb`
  - For Debian, Ubuntu, and derivatives
  - Install with: `sudo dpkg -i package.deb`

- **RPM Package**: `shege-srs-desktop-1.0.0.x86_64.rpm`
  - For RedHat, CentOS, Fedora, and derivatives
  - Install with: `sudo rpm -i package.rpm`

## Features

### Desktop Application Features
- **Native Window**: Proper desktop window with title bar
- **Menu Bar**: File, Edit, View, Window, Help menus
- **Keyboard Shortcuts**:
  - `Ctrl+N` - New Student
  - `Ctrl+D` - Dashboard
  - `Ctrl+L` - Students List
  - `Ctrl+Q` - Quit Application
  - `F5` - Refresh
  - `F11` - Toggle Fullscreen

### Security Features
- **Isolated Context**: Web content runs in isolated context
- **No Node.js Access**: Web pages cannot access Node.js APIs
- **External Link Protection**: External links open in default browser

### Platform Integration
- **Windows**: Native installer, start menu integration
- **Linux**: Desktop file integration, system tray support
- **Auto-updater Ready**: Framework for future auto-update feature

## Troubleshooting

### Common Issues

1. **Build Fails on Windows**
   - Install Windows Build Tools: `npm install --global windows-build-tools`
   - Or install Visual Studio Build Tools

2. **Build Fails on Linux**
   - Install required packages: `sudo apt-get install build-essential`

3. **Icons Not Showing**
   - Ensure `assets/icon.png` and `assets/icon.ico` exist
   - Check file permissions

4. **Server Won't Start**
   - Check if port 3000 is available
   - Verify MongoDB connection
   - Check environment variables

### Development Tips

1. **Hot Reload**: Use `npm run electron-dev` for development
2. **Debug**: Press `Ctrl+Shift+I` to open DevTools
3. **Logs**: Check console for server and client logs
4. **Database**: Ensure MongoDB is running before starting

## System Requirements

### Minimum Requirements
- **RAM**: 4GB
- **Storage**: 500MB free space
- **OS**: 
  - Windows 7 or later
  - Ubuntu 16.04 or later
  - CentOS 7 or later

### Recommended Requirements
- **RAM**: 8GB or more
- **Storage**: 1GB free space
- **OS**: Latest stable versions

## Deployment

### For End Users
1. Download appropriate package for your OS
2. Install/extract the application
3. Run SHEGE SRS from desktop shortcut or applications menu
4. Ensure MongoDB is installed and running
5. Configure database connection if needed

### For Administrators
1. Set up MongoDB server
2. Configure environment variables
3. Distribute application packages
4. Provide user training and documentation

## Support

For technical support or issues:
1. Check the troubleshooting section above
2. Review application logs
3. Contact system administrator
4. Report bugs with detailed error messages
