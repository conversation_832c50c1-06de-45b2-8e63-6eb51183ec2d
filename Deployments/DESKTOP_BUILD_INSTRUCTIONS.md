# SHEGE SRS Desktop Application Build Instructions

## Quick Fix for Current Issues

### Problem 1: "Unable to start" (Linux)
**Cause**: MongoDB not running or not accessible
**Solution**: 
```bash
# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Verify MongoDB is running
sudo systemctl status mongod
```

### Problem 2: "Application Server is Not Running" (Windows)
**Cause**: Server startup timeout or MongoDB connection issues
**Solution**:
1. Ensure MongoDB is installed and running
2. Check if port 3000 is available
3. Run dependency check: `npm run check-deps`

## Building Portable Applications

### Prerequisites
1. **Node.js 16+** installed
2. **MongoDB** installed and running
3. **Git** (for cloning repository)

### Step 1: Install Dependencies
```bash
# Install all dependencies
npm install

# Check if everything is ready
npm run check-deps
```

### Step 2: Build Portable Applications

#### Build for Windows (Portable EXE)
```bash
npm run build-portable-win
```
Creates: `dist/SHEGE SRS-1.0.0-win-portable.exe`

#### Build for Linux (AppImage)
```bash
npm run build-portable-linux
```
Creates: `dist/SHEGE SRS-1.0.0-linux-portable.AppImage`

#### Build for Both Platforms
```bash
npm run build-portable-all
```

### Step 3: Test the Build
```bash
# Check what was built
ls -la dist/

# The portable files will be:
# - SHEGE SRS-1.0.0-win-portable.exe (Windows)
# - SHEGE SRS-1.0.0-linux-portable.AppImage (Linux)
```

## Distribution

### Windows Portable
- **File**: Single executable file
- **Size**: ~150-200 MB
- **Requirements**: Windows 10+, MongoDB installed
- **Usage**: Double-click to run, no installation needed

### Linux Portable (AppImage)
- **File**: Single AppImage file
- **Size**: ~150-200 MB
- **Requirements**: Most Linux distributions, MongoDB installed
- **Usage**: 
  ```bash
  chmod +x SHEGE*.AppImage
  ./SHEGE*.AppImage
  ```

## Troubleshooting Build Issues

### Build Fails with "electron-builder not found"
```bash
npm install electron-builder --save-dev
```

### Build Fails with "Icon not found"
The build script will create a default icon automatically. For production:
1. Create `assets/icon.png` (512x512 PNG)
2. Create `assets/icon.ico` (256x256 ICO for Windows)

### MongoDB Connection Issues
```bash
# Check MongoDB status
sudo systemctl status mongod

# Start MongoDB if not running
sudo systemctl start mongod

# Test connection
mongo --eval "db.runCommand('ping')"
```

### Port 3000 Already in Use
```bash
# Find what's using port 3000
sudo netstat -tlnp | grep :3000

# Kill the process if needed
sudo kill -9 <PID>
```

## Advanced Build Options

### Custom Build Configuration
Edit `package.json` build section for:
- Custom app name
- Different target formats
- Code signing (for production)
- Auto-updater configuration

### Environment-Specific Builds
```bash
# Development build (with DevTools)
NODE_ENV=development npm run build-portable-all

# Production build (optimized)
NODE_ENV=production npm run build-portable-all
```

### Build with Custom Icons
1. Place your icons in `assets/` directory:
   - `icon.png` (512x512 for Linux)
   - `icon.ico` (256x256 for Windows)
2. Run the build command

## Testing the Application

### Before Distribution
1. **Test on Clean System**: Test on a system without development tools
2. **MongoDB Dependency**: Ensure MongoDB installation instructions are clear
3. **First Run**: Test the first-time user experience
4. **Performance**: Check startup time and responsiveness

### User Testing Checklist
- [ ] Application starts without errors
- [ ] Login works with default credentials
- [ ] Student registration form works
- [ ] Photo uploads work
- [ ] Export functionality works
- [ ] ID card generation works
- [ ] Application closes cleanly

## Distribution Package

### What to Include
1. **Portable Application**: The built executable/AppImage
2. **README.txt**: Usage instructions (auto-generated)
3. **MongoDB Installation Guide**: Platform-specific instructions
4. **Quick Start Guide**: Getting started steps

### MongoDB Installation Instructions

#### Windows
```
1. Download MongoDB Community Server from mongodb.com
2. Run the installer with default settings
3. MongoDB will start automatically as a Windows service
```

#### Linux (Ubuntu/Debian)
```bash
# Import MongoDB public key
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Install MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
```

## Production Deployment

### For IT Departments
1. **Silent Installation**: MongoDB can be installed silently
2. **Group Policy**: Windows environments can deploy via Group Policy
3. **Package Managers**: Linux environments can use package managers
4. **Network Deployment**: Applications can be deployed from network shares

### Security Considerations
1. **Code Signing**: Sign executables for production distribution
2. **Antivirus**: Some antivirus software may flag unsigned executables
3. **Firewall**: Ensure port 3000 is accessible
4. **User Permissions**: Application runs with user permissions

## Support and Maintenance

### User Support
- Provide MongoDB installation guides
- Include troubleshooting documentation
- Set up support channels for users

### Updates
- Plan for application updates
- Consider implementing auto-updater
- Maintain backward compatibility

### Monitoring
- Monitor application usage
- Collect feedback for improvements
- Track common issues

---

**Need Help?**
- Check the troubleshooting section above
- Run `npm run check-deps` to diagnose issues
- Review the generated README.txt in the dist folder
- Contact technical support with specific error messages
