# SHEGE SRS Technical Documentation

## Table of Contents
1. [System Architecture](#system-architecture)
2. [Technology Stack](#technology-stack)
3. [Database Schema](#database-schema)
4. [API Documentation](#api-documentation)
5. [File Structure](#file-structure)
6. [Configuration Management](#configuration-management)
7. [Security Implementation](#security-implementation)
8. [Performance Considerations](#performance-considerations)
9. [Development Guidelines](#development-guidelines)
10. [Deployment Architecture](#deployment-architecture)

## System Architecture

### Overview
SHEGE SRS follows a modern web application architecture with desktop integration:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Desktop App   │    │   Web Browser   │    │  Mobile Device  │
│   (Electron)    │    │                 │    │   (Future)      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │    Express.js Server    │
                    │   (Node.js Runtime)     │
                    └─────────────┬───────────┘
                                  │
                    ┌─────────────┴───────────┐
                    │    MongoDB Database     │
                    │   (Document Storage)    │
                    └─────────────────────────┘
```

### Component Architecture

#### Frontend Layer
- **Desktop Application**: Electron wrapper providing native OS integration
- **Web Interface**: EJS templates with enhanced CSS styling
- **Client-Side JavaScript**: Form validation, image preview, interactive features

#### Backend Layer
- **Express.js Server**: RESTful API and web server
- **Middleware Stack**: Authentication, file upload, session management
- **Business Logic**: Student management, data validation, report generation

#### Data Layer
- **MongoDB**: Primary data storage for student records
- **File System**: Local storage for uploaded images and documents
- **Session Store**: In-memory session management

#### Integration Layer
- **Multer**: File upload handling
- **Mongoose**: MongoDB object modeling
- **bcrypt**: Password hashing and authentication

## Technology Stack

### Core Technologies
- **Runtime**: Node.js 16+
- **Framework**: Express.js 4.x
- **Database**: MongoDB 4.4+
- **Template Engine**: EJS (Embedded JavaScript)
- **Desktop Framework**: Electron 28+

### Dependencies
```json
{
  "production": {
    "express": "^4.18.2",
    "mongoose": "^7.0.0",
    "ejs": "^3.1.9",
    "multer": "^1.4.5",
    "bcrypt": "^5.1.0",
    "express-session": "^1.17.3",
    "connect-flash": "^0.1.1",
    "method-override": "^3.0.0",
    "dotenv": "^16.0.3"
  },
  "development": {
    "electron": "^28.0.0",
    "electron-builder": "^24.9.1",
    "nodemon": "^3.1.0",
    "concurrently": "^8.2.2"
  }
}
```

### Build Tools
- **Electron Builder**: Desktop application packaging
- **npm scripts**: Build automation
- **PM2**: Production process management

## Database Schema

### Student Collection
```javascript
{
  _id: ObjectId,
  
  // Registration Information
  registrationDate: Date,
  quarter: String, // "Quarter 1", "Quarter 2", etc.
  studentId: String, // Auto-generated or manual
  
  // Personal Information
  fullName: String, // Required
  sex: String, // "Male", "Female"
  dob: Date,
  age: Number,
  bloodType: String, // "A+", "B-", etc.
  motherTongue: String,
  weight: Number,
  height: Number,
  handUse: String, // "Right", "Left", "Both"
  seriousSickness: String,
  
  // Academic Information
  formerGrade: String,
  registeredGrade: String, // Required
  section: String, // "A", "B", "C", etc.
  
  // Address Information
  address: {
    city: String,
    subCity: String,
    tabia: String,
    ketena: String,
    block: String,
    nationality: String,
    houseNumber: String
  },
  
  // Parent Information
  father: {
    fullName: String,
    phone: String,
    nationality: String,
    education: String,
    occupation: String,
    workplace: String,
    photo: String // Filename
  },
  
  mother: {
    fullName: String,
    phone: String,
    nationality: String,
    education: String,
    occupation: String,
    workplace: String,
    photo: String // Filename
  },
  
  // Guardian Information
  guardian: {
    name: String,
    relationship: String,
    phone: String
  },
  
  // Transport Information
  transport: {
    subCity: String,
    tabya: String,
    bus: String,
    startDate: Date
  },
  
  // Documents
  documents: {
    birthCertificate: String, // Filename
    transcript: String, // Filename
    parentsID: String, // Filename
    buyBook: Boolean
  },
  
  // Medical Information
  others: {
    shortSight: Boolean,
    longSight: Boolean,
    allergic: Boolean,
    autism: Boolean,
    languageProblem: Boolean
  },
  
  // File References
  studentPic: String, // Filename
  
  // Metadata
  status: String, // "active", "pending", "graduated", "transferred"
  createdAt: Date,
  updatedAt: Date
}
```

### Admin Collection
```javascript
{
  _id: ObjectId,
  username: String, // Unique
  password: String, // Hashed with bcrypt
  role: String, // "administrator", "user", "readonly"
  lastLogin: Date,
  loginAttempts: Number,
  lockUntil: Date,
  createdAt: Date,
  updatedAt: Date
}
```

### Session Collection (if using MongoDB session store)
```javascript
{
  _id: String, // Session ID
  expires: Date,
  session: Object // Serialized session data
}
```

## API Documentation

### Authentication Endpoints

#### POST /login
```javascript
// Request
{
  username: "admin",
  password: "password123"
}

// Response (Success)
{
  success: true,
  redirect: "/dashboard"
}

// Response (Error)
{
  success: false,
  message: "Invalid credentials"
}
```

#### POST /logout
```javascript
// Response
{
  success: true,
  redirect: "/login"
}
```

### Student Management Endpoints

#### GET /students
```javascript
// Query Parameters
{
  page: 1,
  limit: 20,
  search: "john",
  grade: "Grade 10",
  section: "A",
  quarter: "Quarter 1"
}

// Response
{
  students: [...],
  pagination: {
    current: 1,
    total: 5,
    hasNext: true,
    hasPrev: false
  },
  stats: {
    total: 95,
    filtered: 20
  }
}
```

#### POST /register
```javascript
// Multipart form data with files
{
  // Student data
  fullName: "John Doe",
  registeredGrade: "Grade 10",
  // ... other fields
  
  // Files
  studentPic: File,
  fatherPic: File,
  motherPic: File,
  birthCertificate: File,
  transcript: File,
  parentsID: File
}

// Response (Success)
{
  success: true,
  message: "Student registered successfully",
  studentId: "STU123456"
}
```

#### GET /students/:id
```javascript
// Response
{
  student: {
    _id: "...",
    fullName: "John Doe",
    // ... complete student data
  }
}
```

#### POST /students/:id
```javascript
// Update student (same format as registration)
// Response
{
  success: true,
  message: "Student updated successfully"
}
```

#### DELETE /students/:id
```javascript
// Response
{
  success: true,
  message: "Student deleted successfully"
}
```

### Export Endpoints

#### POST /export/csv
```javascript
// Request
{
  filters: {
    grade: "Grade 10",
    section: "A",
    quarter: "Quarter 1"
  },
  fields: ["fullName", "registeredGrade", "section"]
}

// Response
{
  success: true,
  filename: "students_export_20240101.csv",
  downloadUrl: "/downloads/students_export_20240101.csv"
}
```

### Print Endpoints

#### GET /print/student/:id
```javascript
// Response: HTML page optimized for printing
```

#### GET /print/id-card/:id
```javascript
// Response: HTML ID card (front and back)
```

#### POST /print/filtered
```javascript
// Request
{
  filters: { ... },
  format: "student-list" // or "id-cards"
}

// Response: HTML print page
```

## File Structure

```
shege-srs/
├── src/
│   ├── app.js                 # Main application entry point
│   ├── config/
│   │   ├── db.js             # Database connection
│   │   └── system.js         # System configuration
│   ├── controllers/
│   │   └── studentController.js # Business logic
│   ├── models/
│   │   ├── student.js        # Student schema
│   │   └── admin.js          # Admin schema
│   ├── routes/
│   │   ├── authRoutes.js     # Authentication routes
│   │   ├── studentRoutes.js  # Student management routes
│   │   └── exportRoutes.js   # Export/print routes
│   ├── middleware/
│   │   ├── auth.js           # Authentication middleware
│   │   ├── upload.js         # File upload configuration
│   │   └── validation.js     # Input validation
│   ├── views/
│   │   ├── auth/
│   │   │   └── login.ejs     # Login page
│   │   ├── dashboard.ejs     # Dashboard
│   │   ├── index.ejs         # Student list
│   │   ├── register.ejs      # Registration form
│   │   ├── id-card.ejs       # ID card template
│   │   └── print-*.ejs       # Print templates
│   ├── public/
│   │   ├── styles-enhanced.css # Main stylesheet
│   │   ├── images/           # Static images
│   │   └── js/               # Client-side JavaScript
│   ├── uploads/              # Uploaded files
│   ├── logs/                 # Application logs
│   └── scripts/
│       └── createAdmin.js    # Admin creation script
├── electron-main.js          # Electron main process
├── electron-preload.js       # Electron preload script
├── package.json              # Dependencies and scripts
├── .env                      # Environment variables
├── .env.example              # Environment template
└── docs/                     # Documentation
    ├── USER_MANUAL.md
    ├── ADMINISTRATOR_GUIDE.md
    ├── INSTALLATION_GUIDE.md
    └── TECHNICAL_DOCUMENTATION.md
```

## Configuration Management

### Environment Variables
```env
# Core Configuration
SYSTEM_NAME=SHEGE SRS
MONGODB_URI=mongodb://localhost:27017/student-registration
PORT=3000
NODE_ENV=production
SESSION_SECRET=secure-random-string

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_PRINT_FEATURES=true
ENABLE_EXPORT_FEATURES=true

# Security Settings
SESSION_TIMEOUT=3600000
MAX_LOGIN_ATTEMPTS=5
MAX_FILE_SIZE=5MB

# School Information
SCHOOL_NAME=School Name
SCHOOL_ADDRESS=School Address
SCHOOL_PHONE=******-567-8900
```

### System Configuration
The `src/config/system.js` file provides:
- Default values for all settings
- Environment variable integration
- Feature toggles
- Data validation rules
- Academic year settings

## Security Implementation

### Authentication
- **Password Hashing**: bcrypt with salt rounds
- **Session Management**: Express sessions with secure cookies
- **Login Attempts**: Rate limiting and account lockout
- **CSRF Protection**: Built-in Express protections

### Input Validation
- **Server-Side Validation**: All inputs validated before processing
- **File Upload Security**: Type and size restrictions
- **XSS Prevention**: Input sanitization and output encoding
- **SQL Injection Prevention**: MongoDB parameterized queries

### File Security
- **Upload Restrictions**: File type and size validation
- **Secure Storage**: Files stored outside web root
- **Access Control**: Authenticated access required
- **Virus Scanning**: Recommended for production

## Performance Considerations

### Database Optimization
- **Indexing Strategy**: Indexes on frequently queried fields
- **Query Optimization**: Efficient MongoDB queries
- **Connection Pooling**: Mongoose connection management
- **Data Pagination**: Large datasets handled efficiently

### Application Performance
- **Caching**: Static asset caching
- **Compression**: Gzip compression for responses
- **Image Optimization**: Automatic image resizing
- **Memory Management**: Efficient memory usage patterns

### Scalability
- **Horizontal Scaling**: Load balancer ready
- **Database Sharding**: MongoDB sharding support
- **CDN Integration**: Static asset delivery
- **Microservices**: Modular architecture for future expansion

## Development Guidelines

### Code Standards
- **ES6+ JavaScript**: Modern JavaScript features
- **Async/Await**: Promise-based asynchronous code
- **Error Handling**: Comprehensive error management
- **Logging**: Structured logging with levels

### Testing Strategy
- **Unit Tests**: Individual function testing
- **Integration Tests**: API endpoint testing
- **End-to-End Tests**: Complete workflow testing
- **Performance Tests**: Load and stress testing

### Version Control
- **Git Workflow**: Feature branch workflow
- **Commit Standards**: Conventional commit messages
- **Code Reviews**: Mandatory peer reviews
- **Release Management**: Semantic versioning

## Deployment Architecture

### Production Environment
```
┌─────────────────┐
│   Load Balancer │
│    (nginx)      │
└─────────┬───────┘
          │
    ┌─────┴─────┐
    │           │
┌───▼───┐   ┌───▼───┐
│App #1 │   │App #2 │
│(PM2)  │   │(PM2)  │
└───┬───┘   └───┬───┘
    │           │
    └─────┬─────┘
          │
    ┌─────▼─────┐
    │ MongoDB   │
    │ Cluster   │
    └───────────┘
```

### Monitoring Stack
- **Application Monitoring**: PM2 monitoring
- **Database Monitoring**: MongoDB Compass
- **Log Aggregation**: Centralized logging
- **Performance Metrics**: Custom dashboards
- **Alerting**: Email/SMS notifications

---

*This technical documentation should be updated as the system evolves and new features are added.*
