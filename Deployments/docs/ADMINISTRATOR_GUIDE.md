# SHEGE SRS Administrator's Guide

## Table of Contents
1. [System Overview](#system-overview)
2. [Installation and Setup](#installation-and-setup)
3. [Configuration Management](#configuration-management)
4. [User Management](#user-management)
5. [Database Administration](#database-administration)
6. [Backup and Recovery](#backup-and-recovery)
7. [Security Management](#security-management)
8. [Performance Monitoring](#performance-monitoring)
9. [Troubleshooting](#troubleshooting)
10. [Maintenance Procedures](#maintenance-procedures)

## System Overview

### Architecture
SHEGE SRS is built on a modern technology stack:
- **Frontend**: EJS templates with enhanced CSS
- **Backend**: Node.js with Express.js framework
- **Database**: MongoDB for data storage
- **Desktop**: Electron for cross-platform desktop application
- **File Storage**: Local file system for uploads

### System Components
- **Web Server**: Express.js application server
- **Database Server**: MongoDB instance
- **File Storage**: Local uploads directory
- **Desktop Shell**: Electron wrapper
- **Configuration**: Environment variables and config files

### Network Architecture
```
[Desktop App] → [Electron] → [Express Server] → [MongoDB]
                     ↓
                [File System]
```

## Installation and Setup

### Prerequisites
- **Node.js**: Version 16.0 or higher
- **MongoDB**: Version 4.4 or higher
- **Operating System**: Windows 10+ or Linux (Ubuntu 18.04+)
- **RAM**: 8GB minimum for server deployment
- **Storage**: 10GB minimum for application and data

### Server Installation

#### 1. MongoDB Setup
```bash
# Ubuntu/Debian
sudo apt-get install mongodb

# CentOS/RHEL
sudo yum install mongodb-org

# Windows
# Download and install from MongoDB official website

# Start MongoDB service
sudo systemctl start mongod
sudo systemctl enable mongod
```

#### 2. Application Installation
```bash
# Clone or extract application files
cd /opt/shege-srs

# Install dependencies
npm install --production

# Create environment configuration
cp .env.example .env
nano .env
```

#### 3. Initial Configuration
```bash
# Create admin user
npm run setup-admin

# Start application
npm start

# For production with PM2
npm install -g pm2
pm2 start src/app.js --name "shege-srs"
pm2 startup
pm2 save
```

### Desktop Distribution

#### Building Installers
```bash
# Install build dependencies
npm install

# Build for Windows
npm run build-win

# Build for Linux
npm run build-linux

# Build for both platforms
npm run build-all
```

#### Distribution Files
- **Windows**: `dist/SHEGE SRS Setup 1.0.0.exe`
- **Linux DEB**: `dist/shege-srs-desktop_1.0.0_amd64.deb`
- **Linux RPM**: `dist/shege-srs-desktop-1.0.0.x86_64.rpm`
- **Linux AppImage**: `dist/SHEGE SRS-1.0.0.AppImage`

## Configuration Management

### Environment Variables

#### Core Settings
```env
# System Information
SYSTEM_NAME=SHEGE SRS
SYSTEM_FULL_NAME=Student Registration System
SYSTEM_VERSION=1.0.0

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/student-registration
DB_NAME=student-registration

# Server Configuration
PORT=3000
NODE_ENV=production
SESSION_SECRET=your-secure-secret-key-here

# School Information
SCHOOL_NAME=Your School Name
SCHOOL_ADDRESS=School Address
SCHOOL_PHONE=******-567-8900
SCHOOL_EMAIL=<EMAIL>
```

#### Security Settings
```env
# Session Configuration
SESSION_TIMEOUT=3600000
MAX_LOGIN_ATTEMPTS=5

# File Upload Limits
MAX_FILE_SIZE=5MB

# Security Features
ENABLE_RATE_LIMITING=true
API_RATE_LIMIT=100
```

#### Feature Toggles
```env
# Application Features
ENABLE_REGISTRATION=true
ENABLE_PRINT_FEATURES=true
ENABLE_EXPORT_FEATURES=true
ENABLE_EMAIL=false
ENABLE_BACKUP=true
```

### System Configuration File

The `src/config/system.js` file contains comprehensive system settings:

#### Academic Configuration
- Grade levels and sections
- Academic year settings
- Quarter definitions
- Student status options

#### Data Validation
- Blood type options
- Medical condition lists
- Guardian relationship types
- Language options

#### File Management
- Upload directories
- File type restrictions
- Size limitations
- Storage paths

## User Management

### Admin Account Management

#### Creating Admin Users
```bash
# Using setup script
npm run setup-admin

# Manual creation (in MongoDB shell)
use student-registration
db.admins.insertOne({
  username: "admin",
  password: "$2b$10$hashedpassword",
  role: "administrator",
  createdAt: new Date()
})
```

#### Password Management
- Passwords are hashed using bcrypt
- Minimum password requirements should be enforced
- Regular password rotation recommended
- Account lockout after failed attempts

#### Role-Based Access
Current system supports:
- **Administrator**: Full system access
- **User**: Limited access (future enhancement)
- **Read-Only**: View-only access (future enhancement)

### Session Management
- Session timeout: 1 hour (configurable)
- Secure session cookies
- Session cleanup on logout
- Concurrent session handling

## Database Administration

### MongoDB Management

#### Database Structure
```
student-registration/
├── students          # Student records
├── admins           # Administrator accounts
├── sessions         # User sessions
└── uploads          # File metadata
```

#### Regular Maintenance
```bash
# Database backup
mongodump --db student-registration --out /backup/$(date +%Y%m%d)

# Database restore
mongorestore --db student-registration /backup/********/student-registration

# Database repair
mongod --repair

# Index optimization
mongo student-registration --eval "db.students.reIndex()"
```

#### Performance Optimization
```javascript
// Create indexes for better performance
db.students.createIndex({ "fullName": "text" })
db.students.createIndex({ "registeredGrade": 1 })
db.students.createIndex({ "quarter": 1 })
db.students.createIndex({ "registrationDate": -1 })
```

### Data Management

#### Student Records
- Automatic ID generation
- Data validation on insert/update
- File reference management
- Soft delete capability (future enhancement)

#### File Management
- Uploaded files stored in `src/uploads/`
- File naming convention: `timestamp-originalname`
- Orphaned file cleanup procedures
- File size and type validation

## Backup and Recovery

### Automated Backup Strategy

#### Daily Backups
```bash
#!/bin/bash
# /etc/cron.daily/shege-srs-backup

BACKUP_DIR="/backup/shege-srs"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p "$BACKUP_DIR/$DATE"

# Backup database
mongodump --db student-registration --out "$BACKUP_DIR/$DATE"

# Backup uploaded files
tar -czf "$BACKUP_DIR/$DATE/uploads.tar.gz" /opt/shege-srs/src/uploads/

# Backup configuration
cp /opt/shege-srs/.env "$BACKUP_DIR/$DATE/"

# Remove backups older than 30 days
find "$BACKUP_DIR" -type d -mtime +30 -exec rm -rf {} \;
```

#### Weekly Full Backups
```bash
#!/bin/bash
# Weekly full system backup

BACKUP_DIR="/backup/weekly"
DATE=$(date +%Y%m%d)

# Full application backup
tar -czf "$BACKUP_DIR/shege-srs-full-$DATE.tar.gz" \
  --exclude=node_modules \
  --exclude=.git \
  /opt/shege-srs/

# Database backup
mongodump --db student-registration --gzip --archive="$BACKUP_DIR/database-$DATE.gz"
```

### Recovery Procedures

#### Database Recovery
```bash
# Restore from backup
mongorestore --db student-registration --gzip --archive=/backup/database-********.gz

# Verify data integrity
mongo student-registration --eval "db.students.count()"
```

#### File Recovery
```bash
# Restore uploaded files
cd /opt/shege-srs/src/
tar -xzf /backup/uploads-********.tar.gz
```

#### Application Recovery
```bash
# Restore full application
cd /opt/
tar -xzf /backup/shege-srs-full-********.tar.gz
cd shege-srs
npm install --production
```

## Security Management

### Access Control

#### Network Security
- Firewall configuration for port 3000
- HTTPS implementation (recommended)
- VPN access for remote administration
- IP whitelisting for admin access

#### Application Security
- Input validation and sanitization
- XSS protection
- CSRF protection (future enhancement)
- SQL injection prevention (NoSQL)

#### File Security
- Upload directory permissions (755)
- File type validation
- Virus scanning (recommended)
- Access logging

### Security Monitoring

#### Log Management
```bash
# Application logs
tail -f /opt/shege-srs/logs/app.log

# MongoDB logs
tail -f /var/log/mongodb/mongod.log

# System logs
journalctl -u shege-srs -f
```

#### Security Auditing
- Regular password audits
- Failed login monitoring
- File access logging
- Database query monitoring

### Incident Response

#### Security Breach Procedures
1. **Immediate Response**
   - Isolate affected systems
   - Change all passwords
   - Review access logs
   - Document incident

2. **Investigation**
   - Analyze log files
   - Identify breach scope
   - Assess data exposure
   - Implement fixes

3. **Recovery**
   - Restore from clean backups
   - Update security measures
   - Monitor for reoccurrence
   - Update procedures

## Performance Monitoring

### System Metrics

#### Application Performance
```bash
# Monitor Node.js process
pm2 monit

# Memory usage
free -h

# Disk usage
df -h

# CPU usage
top -p $(pgrep node)
```

#### Database Performance
```javascript
// MongoDB performance stats
db.runCommand({ serverStatus: 1 })
db.stats()
db.students.stats()
```

### Performance Optimization

#### Application Tuning
- Enable gzip compression
- Implement caching strategies
- Optimize database queries
- Image compression for uploads

#### Database Tuning
- Regular index maintenance
- Query optimization
- Connection pooling
- Memory allocation tuning

#### System Tuning
- File system optimization
- Network configuration
- Memory management
- Process limits

## Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check port availability
netstat -tlnp | grep :3000

# Check MongoDB connection
mongo --eval "db.runCommand('ping')"

# Check application logs
tail -f logs/app.log

# Check environment variables
printenv | grep SHEGE
```

#### Database Connection Issues
```bash
# Check MongoDB status
systemctl status mongod

# Test connection
mongo student-registration --eval "db.students.count()"

# Check network connectivity
telnet localhost 27017
```

#### File Upload Problems
```bash
# Check upload directory permissions
ls -la src/uploads/

# Check disk space
df -h

# Check file size limits
grep MAX_FILE_SIZE .env
```

### Diagnostic Tools

#### Health Check Script
```bash
#!/bin/bash
# System health check

echo "=== SHEGE SRS Health Check ==="

# Check application process
if pgrep -f "node.*app.js" > /dev/null; then
    echo "✓ Application is running"
else
    echo "✗ Application is not running"
fi

# Check MongoDB
if systemctl is-active --quiet mongod; then
    echo "✓ MongoDB is running"
else
    echo "✗ MongoDB is not running"
fi

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -lt 90 ]; then
    echo "✓ Disk space OK ($DISK_USAGE%)"
else
    echo "✗ Disk space critical ($DISK_USAGE%)"
fi

# Check memory usage
MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ $MEM_USAGE -lt 90 ]; then
    echo "✓ Memory usage OK ($MEM_USAGE%)"
else
    echo "✗ Memory usage high ($MEM_USAGE%)"
fi
```

## Maintenance Procedures

### Regular Maintenance Tasks

#### Daily Tasks
- Monitor system health
- Check error logs
- Verify backup completion
- Review security alerts

#### Weekly Tasks
- Database maintenance
- Log rotation
- Performance review
- Security updates

#### Monthly Tasks
- Full system backup
- User account review
- Capacity planning
- Documentation updates

#### Quarterly Tasks
- Security audit
- Performance optimization
- Disaster recovery testing
- System updates

### Update Procedures

#### Application Updates
```bash
# Backup current version
cp -r /opt/shege-srs /opt/shege-srs-backup-$(date +%Y%m%d)

# Stop application
pm2 stop shege-srs

# Update application files
# (Extract new version)

# Update dependencies
npm install --production

# Run database migrations (if any)
npm run migrate

# Start application
pm2 start shege-srs

# Verify functionality
curl http://localhost:3000/health
```

#### System Updates
```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Update Node.js (if needed)
# Update MongoDB (if needed)

# Restart services
sudo systemctl restart mongod
pm2 restart shege-srs
```

### Monitoring and Alerting

#### Log Monitoring
```bash
# Set up log rotation
sudo nano /etc/logrotate.d/shege-srs

# Monitor critical errors
tail -f logs/app.log | grep ERROR

# Set up email alerts for critical issues
```

#### Performance Monitoring
- CPU usage thresholds
- Memory usage alerts
- Disk space monitoring
- Database performance metrics

### Emergency Procedures

#### System Recovery
1. **Assess the situation**
2. **Implement immediate fixes**
3. **Restore from backups if necessary**
4. **Document the incident**
5. **Update procedures to prevent recurrence**

#### Contact Information
- **Primary Administrator**: [Contact Details]
- **Database Administrator**: [Contact Details]
- **IT Support**: [Contact Details]
- **Vendor Support**: [Contact Details]

---

*This guide should be reviewed and updated regularly to reflect system changes and improvements.*
