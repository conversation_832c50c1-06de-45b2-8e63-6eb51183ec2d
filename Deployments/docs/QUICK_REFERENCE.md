# SHEGE SRS Quick Reference Guide

## Table of Contents
1. [System Access](#system-access)
2. [Common Tasks](#common-tasks)
3. [Keyboard Shortcuts](#keyboard-shortcuts)
4. [Troubleshooting Quick Fixes](#troubleshooting-quick-fixes)
5. [Emergency Procedures](#emergency-procedures)
6. [Contact Information](#contact-information)

## System Access

### Desktop Application
- **Windows**: Start Menu → SHEGE SRS or Desktop Shortcut
- **Linux**: Applications Menu → Education → SHEGE SRS
- **Direct Launch**: Double-click application icon

### Web Browser Access
- **URL**: `http://localhost:3000`
- **Default Login**: 
  - Username: `admin`
  - Password: `admin123` (change immediately)

### First Time Setup
1. Launch application
2. Login with default credentials
3. Change password immediately
4. Configure school information
5. Create additional admin users if needed

## Common Tasks

### Student Registration
1. **Navigate**: File → New Student (Ctrl+N)
2. **Fill Form**: Complete all required fields (marked with *)
3. **Upload Photos**: Click photo upload buttons
4. **Submit**: Click "Register Student" button
5. **Verify**: Check success message and student appears in list

### Search Students
1. **Navigate**: File → Students List (Ctrl+L)
2. **Search**: Use search box at top
3. **Filter**: Use dropdown filters (Grade, Section, Quarter)
4. **Sort**: Click column headers to sort

### Export Data
1. **Select Students**: Check boxes next to desired students
2. **Export Options**: 
   - Current View: Export → Current View
   - Selected: Export → Selected Students
   - Filtered: Export → Advanced Filters
3. **Download**: File downloads automatically

### Generate ID Cards
1. **Select Students**: Check boxes for students needing ID cards
2. **Print**: Click "Print ID Cards" button
3. **Preview**: Review front and back pages
4. **Print**: Use browser print function (Ctrl+P)

### Edit Student Information
1. **Find Student**: Search or browse student list
2. **Edit**: Click "Edit" button in Actions column
3. **Modify**: Update any information
4. **Save**: Click "Update Student" button

### View Student Details
1. **Find Student**: Search or browse student list
2. **View**: Click "View" button in Actions column
3. **Review**: All student information displayed
4. **Print**: Use "Print Student Info" if needed

## Keyboard Shortcuts

### Application Navigation
- `Ctrl+N`: New Student Registration
- `Ctrl+D`: Dashboard
- `Ctrl+L`: Students List
- `Ctrl+Q`: Quit Application
- `F5`: Refresh Current Page
- `F11`: Toggle Fullscreen

### Form Navigation
- `Tab`: Move to next field
- `Shift+Tab`: Move to previous field
- `Enter`: Submit form (when on submit button)
- `Esc`: Cancel current operation

### List Operations
- `Ctrl+A`: Select all students
- `Ctrl+Click`: Select multiple students
- `Shift+Click`: Select range of students
- `Delete`: Delete selected student (with confirmation)

### Print Operations
- `Ctrl+P`: Print current page
- `Ctrl+Shift+P`: Print preview

## Troubleshooting Quick Fixes

### Application Won't Start
```bash
# Check if already running
ps aux | grep node

# Kill existing process
pkill -f "node.*app.js"

# Restart application
cd /opt/shege-srs
npm start
```

### Login Issues
1. **Verify Credentials**: Check username/password
2. **Check Caps Lock**: Ensure caps lock is off
3. **Clear Browser Cache**: Ctrl+Shift+Delete
4. **Reset Password**: Contact administrator

### Photo Upload Fails
1. **Check File Size**: Must be under 5MB
2. **Check File Type**: JPEG, PNG, GIF, WebP only
3. **Try Different Photo**: Use different image file
4. **Refresh Page**: F5 and try again

### Export Not Working
1. **Check Selection**: Ensure students are selected
2. **Try Smaller Export**: Export fewer students
3. **Check Disk Space**: Ensure sufficient storage
4. **Refresh and Retry**: F5 and try again

### Print Issues
1. **Check Printer**: Ensure printer is connected and ready
2. **Print Preview**: Use Ctrl+P to preview first
3. **Try Different Browser**: Use Chrome or Firefox
4. **Check Print Settings**: Verify paper size and orientation

### Slow Performance
1. **Close Other Programs**: Free up system resources
2. **Restart Application**: Close and reopen SHEGE SRS
3. **Check Internet**: Ensure stable connection
4. **Clear Browser Cache**: Ctrl+Shift+Delete

### Database Connection Error
1. **Check MongoDB**: Ensure database service is running
2. **Restart Services**: Restart both app and database
3. **Check Network**: Verify network connectivity
4. **Contact Administrator**: If problem persists

## Emergency Procedures

### System Crash Recovery
1. **Assess Situation**: Determine extent of issue
2. **Check Logs**: Review error messages
3. **Restart Services**: 
   ```bash
   sudo systemctl restart mongod
   sudo systemctl restart shege-srs
   ```
4. **Verify Functionality**: Test basic operations
5. **Contact Support**: If issues persist

### Data Recovery
1. **Stop Application**: Prevent further data changes
2. **Identify Issue**: Determine what data is affected
3. **Restore from Backup**:
   ```bash
   # Restore database
   mongorestore --drop --gzip --archive=/backup/latest/database.gz
   
   # Restore files
   tar -xzf /backup/latest/uploads.tar.gz -C /opt/shege-srs/src/
   ```
4. **Verify Recovery**: Check that data is restored
5. **Document Incident**: Record what happened

### Security Incident Response
1. **Isolate System**: Disconnect from network if necessary
2. **Change Passwords**: Update all admin passwords
3. **Review Logs**: Check for unauthorized access
4. **Contact Security Team**: Report incident immediately
5. **Document Everything**: Keep detailed records

### Power Outage Recovery
1. **Wait for Power**: Ensure stable power before restart
2. **Check Hardware**: Verify no physical damage
3. **Start Services**:
   ```bash
   sudo systemctl start mongod
   sudo systemctl start shege-srs
   ```
4. **Verify Data Integrity**: Check recent data
5. **Run Backup**: Ensure current backup exists

## Contact Information

### Technical Support
- **Primary Administrator**: [Name] - [Phone] - [Email]
- **Database Administrator**: [Name] - [Phone] - [Email]
- **IT Help Desk**: [Phone] - [Email]
- **Emergency Contact**: [Phone] (24/7)

### Vendor Support
- **Software Support**: [Contact Information]
- **Hardware Support**: [Contact Information]
- **Network Support**: [Contact Information]

### Internal Contacts
- **School IT Department**: [Phone] - [Email]
- **System Owner**: [Name] - [Phone] - [Email]
- **Training Coordinator**: [Name] - [Phone] - [Email]

## Quick Commands Reference

### System Administration
```bash
# Check system status
sudo systemctl status shege-srs
sudo systemctl status mongod

# View logs
tail -f /opt/shege-srs/logs/app.log
tail -f /var/log/mongodb/mongod.log

# Restart services
sudo systemctl restart shege-srs
sudo systemctl restart mongod

# Check disk space
df -h

# Check memory usage
free -h

# Check running processes
ps aux | grep node
ps aux | grep mongod
```

### Database Operations
```bash
# Connect to database
mongo student-registration

# Check database status
mongo --eval "db.runCommand('ping')"

# Count students
mongo student-registration --eval "db.students.count()"

# Backup database
mongodump --db student-registration --gzip --archive=backup.gz

# Restore database
mongorestore --drop --gzip --archive=backup.gz
```

### File Operations
```bash
# Check upload directory
ls -la /opt/shege-srs/src/uploads/

# Check file permissions
ls -la /opt/shege-srs/

# Clean temporary files
find /tmp -name "*shege*" -delete

# Check log file sizes
du -sh /opt/shege-srs/logs/*
```

## Error Codes Reference

### Common Error Messages
- **"Database connection failed"**: MongoDB not running or connection issue
- **"File upload failed"**: File too large or wrong format
- **"Validation failed"**: Required fields missing or invalid data
- **"Authentication failed"**: Wrong username/password
- **"Permission denied"**: Insufficient user privileges
- **"Server error"**: General application error, check logs

### HTTP Status Codes
- **200**: Success
- **400**: Bad request (client error)
- **401**: Unauthorized (login required)
- **403**: Forbidden (insufficient permissions)
- **404**: Not found
- **500**: Internal server error

## File Locations Reference

### Important Directories
- **Application**: `/opt/shege-srs/`
- **Uploads**: `/opt/shege-srs/src/uploads/`
- **Logs**: `/opt/shege-srs/logs/`
- **Backups**: `/backup/shege-srs/`
- **Configuration**: `/opt/shege-srs/.env`

### Log Files
- **Application**: `/opt/shege-srs/logs/app.log`
- **MongoDB**: `/var/log/mongodb/mongod.log`
- **System**: `/var/log/syslog` (Ubuntu) or `/var/log/messages` (CentOS)

### Configuration Files
- **Application**: `/opt/shege-srs/.env`
- **MongoDB**: `/etc/mongod.conf`
- **System Service**: `/etc/systemd/system/shege-srs.service`

## Performance Benchmarks

### Normal Operating Parameters
- **CPU Usage**: < 50% average
- **Memory Usage**: < 70% of available RAM
- **Disk Usage**: < 80% of available space
- **Response Time**: < 2 seconds for most operations
- **Database Queries**: < 100ms for simple queries

### Warning Thresholds
- **CPU Usage**: > 80% sustained
- **Memory Usage**: > 85% of available RAM
- **Disk Usage**: > 90% of available space
- **Response Time**: > 5 seconds consistently
- **Error Rate**: > 5% of requests failing

---

*Keep this quick reference guide accessible for immediate problem resolution. Update contact information and file paths as needed for your specific installation.*
