# SHEGE SRS Installation Guide

## Table of Contents
1. [System Requirements](#system-requirements)
2. [Pre-Installation Checklist](#pre-installation-checklist)
3. [Desktop Installation](#desktop-installation)
4. [Server Installation](#server-installation)
5. [Database Setup](#database-setup)
6. [Initial Configuration](#initial-configuration)
7. [Post-Installation Testing](#post-installation-testing)
8. [Troubleshooting Installation](#troubleshooting-installation)

## System Requirements

### Minimum Requirements
- **Operating System**: 
  - Windows 10 or later
  - Ubuntu 18.04 LTS or later
  - CentOS 7 or later
  - Debian 9 or later
- **RAM**: 4GB
- **Storage**: 2GB free space
- **Network**: Internet connection for initial setup
- **Display**: 1024x768 resolution minimum

### Recommended Requirements
- **Operating System**: Latest stable versions
- **RAM**: 8GB or more
- **Storage**: 10GB free space (for data growth)
- **Network**: Stable broadband connection
- **Display**: 1920x1080 resolution or higher
- **Processor**: Multi-core processor (Intel i5 or AMD equivalent)

### Server Requirements (Multi-User Setup)
- **RAM**: 16GB minimum
- **Storage**: 50GB+ (depending on user count)
- **Network**: Gigabit Ethernet
- **Backup Storage**: Additional storage for backups
- **Operating System**: Linux server distribution preferred

## Pre-Installation Checklist

### Before You Begin
- [ ] Verify system meets minimum requirements
- [ ] Ensure administrative privileges on target system
- [ ] Download latest SHEGE SRS installer
- [ ] Backup existing data (if upgrading)
- [ ] Close all unnecessary applications
- [ ] Disable antivirus temporarily (if needed)
- [ ] Prepare installation media (if offline install)

### Required Information
- [ ] School name and contact information
- [ ] Administrator username and password
- [ ] Database configuration preferences
- [ ] Network configuration details
- [ ] Backup storage location

### Network Considerations
- [ ] Firewall configuration
- [ ] Port availability (3000, 27017)
- [ ] Network security policies
- [ ] Remote access requirements

## Desktop Installation

### Windows Installation

#### Method 1: Using Installer (Recommended)
1. **Download Installer**
   - Download `SHEGE SRS Setup 1.0.0.exe`
   - Verify file integrity (check file size and source)

2. **Run Installer**
   ```
   Right-click installer → "Run as administrator"
   ```

3. **Installation Wizard**
   - Welcome screen → Click "Next"
   - License agreement → Accept and click "Next"
   - Installation directory → Choose location or use default
   - Start menu folder → Confirm or customize
   - Additional tasks → Select desktop shortcut creation
   - Ready to install → Click "Install"

4. **Complete Installation**
   - Wait for installation to complete
   - Click "Finish" to launch application

#### Method 2: Portable Version
1. **Download Portable**
   - Download `SHEGE SRS 1.0.0.exe`
   - No installation required

2. **Setup Portable**
   - Create folder: `C:\SHEGE-SRS\`
   - Copy executable to folder
   - Create desktop shortcut (optional)
   - Run executable directly

### Linux Installation

#### Ubuntu/Debian (.deb package)
```bash
# Download package
wget https://releases.shegesrs.com/shege-srs-desktop_1.0.0_amd64.deb

# Install package
sudo dpkg -i shege-srs-desktop_1.0.0_amd64.deb

# Fix dependencies if needed
sudo apt-get install -f

# Launch application
shege-srs
```

#### CentOS/RHEL/Fedora (.rpm package)
```bash
# Download package
wget https://releases.shegesrs.com/shege-srs-desktop-1.0.0.x86_64.rpm

# Install package
sudo rpm -i shege-srs-desktop-1.0.0.x86_64.rpm

# Or using yum/dnf
sudo yum install shege-srs-desktop-1.0.0.x86_64.rpm

# Launch application
shege-srs
```

#### Universal Linux (AppImage)
```bash
# Download AppImage
wget https://releases.shegesrs.com/SHEGE\ SRS-1.0.0.AppImage

# Make executable
chmod +x SHEGE\ SRS-1.0.0.AppImage

# Run application
./SHEGE\ SRS-1.0.0.AppImage
```

## Server Installation

### Prerequisites Installation

#### Node.js Installation
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# Verify installation
node --version
npm --version
```

#### MongoDB Installation
```bash
# Ubuntu/Debian
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# CentOS/RHEL
sudo tee /etc/yum.repos.d/mongodb-org-6.0.repo <<EOF
[mongodb-org-6.0]
name=MongoDB Repository
baseurl=https://repo.mongodb.org/yum/redhat/8/mongodb-org/6.0/x86_64/
gpgcheck=1
enabled=1
gpgkey=https://www.mongodb.org/static/pgp/server-6.0.asc
EOF
sudo yum install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
```

### Application Installation

#### Download and Setup
```bash
# Create application directory
sudo mkdir -p /opt/shege-srs
cd /opt/shege-srs

# Download application (replace with actual download method)
sudo wget https://releases.shegesrs.com/shege-srs-server-1.0.0.tar.gz
sudo tar -xzf shege-srs-server-1.0.0.tar.gz

# Set permissions
sudo chown -R $USER:$USER /opt/shege-srs
```

#### Install Dependencies
```bash
# Install production dependencies
npm install --production

# Install PM2 for process management
sudo npm install -g pm2
```

#### Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

#### Service Setup
```bash
# Create systemd service
sudo tee /etc/systemd/system/shege-srs.service <<EOF
[Unit]
Description=SHEGE SRS Application
After=network.target mongod.service

[Service]
Type=simple
User=shegesrs
WorkingDirectory=/opt/shege-srs
ExecStart=/usr/bin/node src/app.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl enable shege-srs
sudo systemctl start shege-srs
```

## Database Setup

### MongoDB Configuration

#### Basic Configuration
```bash
# Edit MongoDB configuration
sudo nano /etc/mongod.conf
```

```yaml
# /etc/mongod.conf
storage:
  dbPath: /var/lib/mongodb
  journal:
    enabled: true

systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log

net:
  port: 27017
  bindIp: 127.0.0.1

processManagement:
  timeZoneInfo: /usr/share/zoneinfo
```

#### Security Configuration
```bash
# Enable authentication (recommended for production)
mongo
```

```javascript
// Create admin user
use admin
db.createUser({
  user: "admin",
  pwd: "secure_password_here",
  roles: ["userAdminAnyDatabase", "dbAdminAnyDatabase"]
})

// Create application user
use student-registration
db.createUser({
  user: "shegesrs",
  pwd: "app_password_here",
  roles: ["readWrite"]
})
```

#### Update Configuration for Authentication
```yaml
# Add to /etc/mongod.conf
security:
  authorization: enabled
```

```bash
# Restart MongoDB
sudo systemctl restart mongod
```

### Database Initialization

#### Create Initial Data
```bash
# Run setup script
npm run setup-admin

# Or manually create admin user
node -e "
const bcrypt = require('bcrypt');
const { MongoClient } = require('mongodb');

async function createAdmin() {
  const client = new MongoClient('mongodb://localhost:27017');
  await client.connect();
  const db = client.db('student-registration');
  
  const hashedPassword = await bcrypt.hash('admin123', 10);
  await db.collection('admins').insertOne({
    username: 'admin',
    password: hashedPassword,
    role: 'administrator',
    createdAt: new Date()
  });
  
  console.log('Admin user created');
  await client.close();
}

createAdmin().catch(console.error);
"
```

## Initial Configuration

### Environment Variables

#### Basic Configuration
```env
# System Information
SYSTEM_NAME=Your School SRS
SYSTEM_FULL_NAME=Your School Student Registration System
SYSTEM_VERSION=1.0.0

# Database
MONGODB_URI=mongodb://localhost:27017/student-registration
DB_NAME=student-registration

# Server
PORT=3000
NODE_ENV=production
SESSION_SECRET=generate-secure-random-string-here

# School Information
SCHOOL_NAME=Your School Name
SCHOOL_ADDRESS=123 School Street, City, State 12345
SCHOOL_PHONE=******-123-4567
SCHOOL_EMAIL=<EMAIL>
SCHOOL_WEBSITE=https://www.yourschool.edu
```

#### Security Configuration
```env
# Security Settings
SESSION_TIMEOUT=3600000
MAX_LOGIN_ATTEMPTS=5
MAX_FILE_SIZE=5MB

# Features
ENABLE_REGISTRATION=true
ENABLE_PRINT_FEATURES=true
ENABLE_EXPORT_FEATURES=true
```

### File Permissions

#### Set Proper Permissions
```bash
# Application files
sudo chown -R shegesrs:shegesrs /opt/shege-srs
sudo chmod -R 755 /opt/shege-srs

# Upload directory
sudo mkdir -p /opt/shege-srs/src/uploads
sudo chmod 755 /opt/shege-srs/src/uploads

# Log directory
sudo mkdir -p /opt/shege-srs/logs
sudo chmod 755 /opt/shege-srs/logs

# Configuration files
sudo chmod 600 /opt/shege-srs/.env
```

### Firewall Configuration

#### Open Required Ports
```bash
# Ubuntu/Debian (UFW)
sudo ufw allow 3000/tcp
sudo ufw allow 27017/tcp  # Only if remote database access needed

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=27017/tcp  # Only if needed
sudo firewall-cmd --reload

# Or using iptables
sudo iptables -A INPUT -p tcp --dport 3000 -j ACCEPT
```

## Post-Installation Testing

### Verification Steps

#### 1. Service Status Check
```bash
# Check application status
sudo systemctl status shege-srs

# Check MongoDB status
sudo systemctl status mongod

# Check process
ps aux | grep node
```

#### 2. Network Connectivity
```bash
# Test local access
curl http://localhost:3000

# Test from another machine
curl http://server-ip:3000

# Check port listening
netstat -tlnp | grep :3000
```

#### 3. Database Connectivity
```bash
# Test MongoDB connection
mongo student-registration --eval "db.runCommand('ping')"

# Check collections
mongo student-registration --eval "show collections"
```

#### 4. Application Functionality
1. **Access Web Interface**
   - Open browser to `http://localhost:3000`
   - Verify login page loads

2. **Test Login**
   - Use default admin credentials
   - Verify dashboard loads

3. **Test Basic Functions**
   - Navigate to student registration
   - Test form validation
   - Upload a test photo

#### 5. Log Review
```bash
# Check application logs
tail -f /opt/shege-srs/logs/app.log

# Check system logs
journalctl -u shege-srs -f

# Check MongoDB logs
tail -f /var/log/mongodb/mongod.log
```

## Troubleshooting Installation

### Common Issues

#### Installation Fails
**Windows:**
- Run installer as administrator
- Disable antivirus temporarily
- Check available disk space
- Verify Windows version compatibility

**Linux:**
- Check package dependencies
- Verify repository access
- Use `sudo` for system-wide installation
- Check file permissions

#### Application Won't Start
```bash
# Check error messages
journalctl -u shege-srs --no-pager

# Check configuration
node -c src/app.js

# Check dependencies
npm list --depth=0

# Check environment variables
printenv | grep SHEGE
```

#### Database Connection Issues
```bash
# Check MongoDB status
sudo systemctl status mongod

# Test connection manually
mongo --eval "db.runCommand('ping')"

# Check configuration
cat /etc/mongod.conf

# Check logs
tail -f /var/log/mongodb/mongod.log
```

#### Permission Issues
```bash
# Fix file permissions
sudo chown -R shegesrs:shegesrs /opt/shege-srs
sudo chmod -R 755 /opt/shege-srs

# Fix upload directory
sudo chmod 755 /opt/shege-srs/src/uploads

# Check SELinux (if applicable)
sudo setsebool -P httpd_can_network_connect 1
```

### Getting Help

#### Log Collection
```bash
# Collect system information
uname -a > system-info.txt
cat /etc/os-release >> system-info.txt

# Collect application logs
cp /opt/shege-srs/logs/app.log ./
cp /var/log/mongodb/mongod.log ./

# Collect configuration (remove sensitive data)
cp /opt/shege-srs/.env .env-sanitized
# Edit .env-sanitized to remove passwords
```

#### Support Contacts
- **Technical Support**: [Contact Information]
- **Documentation**: [Documentation URL]
- **Community Forum**: [Forum URL]
- **Bug Reports**: [Issue Tracker URL]

---

*For additional installation support, refer to the Administrator's Guide or contact technical support.*
