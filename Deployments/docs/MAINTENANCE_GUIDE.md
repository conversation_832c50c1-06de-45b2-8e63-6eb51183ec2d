# SHEGE SRS Maintenance Guide

## Table of Contents
1. [Maintenance Overview](#maintenance-overview)
2. [Daily Maintenance Tasks](#daily-maintenance-tasks)
3. [Weekly Maintenance Tasks](#weekly-maintenance-tasks)
4. [Monthly Maintenance Tasks](#monthly-maintenance-tasks)
5. [Quarterly Maintenance Tasks](#quarterly-maintenance-tasks)
6. [Annual Maintenance Tasks](#annual-maintenance-tasks)
7. [Backup Procedures](#backup-procedures)
8. [Performance Monitoring](#performance-monitoring)
9. [Security Maintenance](#security-maintenance)
10. [Troubleshooting Common Issues](#troubleshooting-common-issues)

## Maintenance Overview

### Maintenance Philosophy
Regular maintenance ensures:
- **System Reliability**: Consistent performance and uptime
- **Data Integrity**: Protection against data loss or corruption
- **Security**: Protection against vulnerabilities and threats
- **Performance**: Optimal system speed and responsiveness
- **Compliance**: Meeting institutional and regulatory requirements

### Maintenance Schedule
- **Daily**: Automated monitoring and basic health checks
- **Weekly**: Performance review and minor optimizations
- **Monthly**: Comprehensive system review and updates
- **Quarterly**: Major updates and security audits
- **Annually**: Complete system overhaul and planning

### Maintenance Team Roles
- **System Administrator**: Overall system health and configuration
- **Database Administrator**: Database performance and integrity
- **Security Officer**: Security monitoring and compliance
- **Application Support**: User support and application issues

## Daily Maintenance Tasks

### Automated Monitoring (5 minutes)

#### System Health Check
```bash
#!/bin/bash
# Daily health check script

echo "=== SHEGE SRS Daily Health Check - $(date) ==="

# Check application status
if pgrep -f "node.*app.js" > /dev/null; then
    echo "✓ Application is running"
else
    echo "✗ Application is DOWN - ALERT REQUIRED"
    # Send alert notification
fi

# Check MongoDB status
if systemctl is-active --quiet mongod; then
    echo "✓ MongoDB is running"
else
    echo "✗ MongoDB is DOWN - ALERT REQUIRED"
fi

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -lt 85 ]; then
    echo "✓ Disk space OK ($DISK_USAGE%)"
else
    echo "⚠ Disk space warning ($DISK_USAGE%)"
fi

# Check memory usage
MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ $MEM_USAGE -lt 85 ]; then
    echo "✓ Memory usage OK ($MEM_USAGE%)"
else
    echo "⚠ Memory usage high ($MEM_USAGE%)"
fi

# Check recent errors
ERROR_COUNT=$(tail -n 100 /opt/shege-srs/logs/app.log | grep -c ERROR)
if [ $ERROR_COUNT -eq 0 ]; then
    echo "✓ No recent errors"
else
    echo "⚠ $ERROR_COUNT errors in last 100 log entries"
fi
```

#### Log Review (10 minutes)
```bash
# Check for critical errors
tail -n 50 /opt/shege-srs/logs/app.log | grep -i "error\|critical\|fatal"

# Check MongoDB logs
tail -n 50 /var/log/mongodb/mongod.log | grep -i "error\|warning"

# Check system logs
journalctl -u shege-srs --since "1 day ago" | grep -i "error\|failed"
```

#### Backup Verification (5 minutes)
```bash
# Verify last backup completed successfully
ls -la /backup/shege-srs/ | head -5

# Check backup size (should be consistent)
du -sh /backup/shege-srs/$(date +%Y%m%d)*

# Verify database backup integrity
mongorestore --dry-run --gzip --archive=/backup/latest/database.gz
```

### Manual Tasks (10 minutes)

#### User Activity Review
- Check recent login attempts
- Review failed authentication logs
- Monitor unusual user activity patterns
- Verify system access logs

#### Performance Metrics
- Application response times
- Database query performance
- File upload success rates
- Export/print operation status

## Weekly Maintenance Tasks

### System Performance Review (30 minutes)

#### Database Maintenance
```bash
# Database statistics
mongo student-registration --eval "
  print('=== Database Statistics ===');
  printjson(db.stats());
  print('=== Collection Statistics ===');
  printjson(db.students.stats());
  print('=== Index Usage ===');
  printjson(db.students.aggregate([{\$indexStats:{}}]));
"

# Optimize database
mongo student-registration --eval "
  db.runCommand({compact: 'students'});
  db.students.reIndex();
"
```

#### Application Performance
```bash
# Check PM2 process statistics
pm2 show shege-srs

# Monitor memory leaks
pm2 monit

# Review slow queries (if logging enabled)
grep "slow" /var/log/mongodb/mongod.log
```

#### File System Maintenance
```bash
# Clean temporary files
find /tmp -name "*shege*" -mtime +7 -delete

# Check upload directory size
du -sh /opt/shege-srs/src/uploads/

# Remove orphaned files (files not referenced in database)
node /opt/shege-srs/scripts/cleanup-orphaned-files.js
```

### Security Review (20 minutes)

#### Access Log Analysis
```bash
# Review authentication attempts
grep "login" /opt/shege-srs/logs/app.log | tail -50

# Check for suspicious activity
grep -i "failed\|error\|unauthorized" /opt/shege-srs/logs/app.log | tail -20

# Review file access patterns
ls -la /opt/shege-srs/src/uploads/ | tail -20
```

#### System Updates Check
```bash
# Check for system updates
sudo apt list --upgradable  # Ubuntu/Debian
sudo yum check-update       # CentOS/RHEL

# Check Node.js security advisories
npm audit

# Check for application updates
# (Manual check of release notes)
```

## Monthly Maintenance Tasks

### Comprehensive System Review (2 hours)

#### Database Optimization
```bash
# Full database analysis
mongo student-registration --eval "
  db.runCommand({dbStats: 1});
  db.students.aggregate([
    {\$group: {_id: '\$registeredGrade', count: {\$sum: 1}}},
    {\$sort: {count: -1}}
  ]);
"

# Index optimization
mongo student-registration --eval "
  db.students.createIndex({fullName: 'text'});
  db.students.createIndex({registeredGrade: 1, section: 1});
  db.students.createIndex({registrationDate: -1});
"

# Database repair and optimization
mongod --repair --dbpath /var/lib/mongodb
```

#### Application Updates
```bash
# Backup current version
cp -r /opt/shege-srs /opt/shege-srs-backup-$(date +%Y%m%d)

# Update dependencies
cd /opt/shege-srs
npm update

# Check for security vulnerabilities
npm audit fix

# Test application after updates
npm test  # If tests are available
```

#### Performance Optimization
```bash
# Analyze log files for performance issues
awk '/slow|timeout|error/ {print}' /opt/shege-srs/logs/app.log | tail -50

# Check resource usage trends
sar -u 1 10  # CPU usage
sar -r 1 10  # Memory usage
sar -d 1 10  # Disk I/O
```

### Data Management (1 hour)

#### Data Cleanup
```bash
# Archive old student records (if applicable)
mongo student-registration --eval "
  var cutoffDate = new Date();
  cutoffDate.setFullYear(cutoffDate.getFullYear() - 5);
  
  var oldStudents = db.students.find({
    registrationDate: {\$lt: cutoffDate},
    status: 'graduated'
  });
  
  print('Students to archive: ' + oldStudents.count());
"

# Clean up old log files
find /opt/shege-srs/logs -name "*.log.*" -mtime +30 -delete

# Optimize file storage
find /opt/shege-srs/src/uploads -type f -size +10M -ls
```

#### Data Integrity Check
```bash
# Verify student data consistency
mongo student-registration --eval "
  // Check for students without required fields
  db.students.find({
    \$or: [
      {fullName: {\$exists: false}},
      {registeredGrade: {\$exists: false}},
      {fullName: ''},
      {registeredGrade: ''}
    ]
  }).count();
"

# Check file references
node /opt/shege-srs/scripts/verify-file-references.js
```

## Quarterly Maintenance Tasks

### Major System Updates (4 hours)

#### Security Audit
```bash
# System security scan
sudo lynis audit system

# Application security review
npm audit
nsp check  # If using nsp

# File permission audit
find /opt/shege-srs -type f -perm /o+w -ls
```

#### Performance Benchmarking
```bash
# Database performance test
mongo student-registration --eval "
  var start = new Date();
  db.students.find({registeredGrade: 'Grade 10'}).limit(100).toArray();
  var end = new Date();
  print('Query time: ' + (end - start) + 'ms');
"

# Application load testing
# Use tools like Apache Bench or Artillery
ab -n 100 -c 10 http://localhost:3000/students
```

#### Capacity Planning
- Review storage growth trends
- Analyze user activity patterns
- Plan for hardware upgrades
- Evaluate performance requirements

### System Optimization (2 hours)

#### Database Tuning
```bash
# MongoDB configuration optimization
sudo nano /etc/mongod.conf

# Add/modify settings:
# storage.wiredTiger.engineConfig.cacheSizeGB: 2
# operationProfiling.slowOpThresholdMs: 100
# operationProfiling.mode: slowOp

sudo systemctl restart mongod
```

#### Application Tuning
```bash
# Node.js optimization
export NODE_OPTIONS="--max-old-space-size=4096"

# PM2 configuration optimization
pm2 delete shege-srs
pm2 start src/app.js --name shege-srs --instances 2 --exec-mode cluster
```

## Annual Maintenance Tasks

### Complete System Overhaul (8 hours)

#### Major Version Updates
- Plan and execute major software updates
- Database migration if required
- Application architecture review
- Security framework updates

#### Hardware Assessment
- Server performance evaluation
- Storage capacity planning
- Network infrastructure review
- Backup system evaluation

#### Documentation Updates
- Update all system documentation
- Review and update procedures
- Update disaster recovery plans
- Training material updates

### Compliance and Audit (4 hours)

#### Data Protection Compliance
- Review data retention policies
- Audit data access controls
- Update privacy policies
- Compliance reporting

#### Security Certification
- Complete security assessments
- Update security policies
- Penetration testing
- Vulnerability assessments

## Backup Procedures

### Daily Automated Backup
```bash
#!/bin/bash
# /etc/cron.daily/shege-srs-backup

BACKUP_DIR="/backup/shege-srs"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p "$BACKUP_DIR/$DATE"

# Database backup
mongodump --db student-registration --gzip --archive="$BACKUP_DIR/$DATE/database.gz"

# File backup
tar -czf "$BACKUP_DIR/$DATE/uploads.tar.gz" /opt/shege-srs/src/uploads/

# Configuration backup
cp /opt/shege-srs/.env "$BACKUP_DIR/$DATE/"

# Cleanup old backups (keep 30 days)
find "$BACKUP_DIR" -type d -mtime +30 -exec rm -rf {} \;

# Verify backup
if [ -f "$BACKUP_DIR/$DATE/database.gz" ]; then
    echo "Backup completed successfully: $DATE"
else
    echo "Backup failed: $DATE" | mail -s "Backup Failure" <EMAIL>
fi
```

### Weekly Full Backup
```bash
#!/bin/bash
# Weekly comprehensive backup

BACKUP_DIR="/backup/weekly"
DATE=$(date +%Y%m%d)

# Full application backup
tar -czf "$BACKUP_DIR/shege-srs-full-$DATE.tar.gz" \
  --exclude=node_modules \
  --exclude=.git \
  --exclude=logs \
  /opt/shege-srs/

# Database backup with compression
mongodump --db student-registration --gzip --archive="$BACKUP_DIR/database-$DATE.gz"

# System configuration backup
tar -czf "$BACKUP_DIR/system-config-$DATE.tar.gz" \
  /etc/mongod.conf \
  /etc/systemd/system/shege-srs.service \
  /etc/nginx/sites-available/shege-srs  # If using nginx
```

### Backup Verification
```bash
#!/bin/bash
# Verify backup integrity

LATEST_BACKUP=$(ls -t /backup/shege-srs/ | head -1)

# Test database restore
mongorestore --dry-run --gzip --archive="/backup/shege-srs/$LATEST_BACKUP/database.gz"

# Test file archive
tar -tzf "/backup/shege-srs/$LATEST_BACKUP/uploads.tar.gz" > /dev/null

if [ $? -eq 0 ]; then
    echo "Backup verification successful"
else
    echo "Backup verification failed" | mail -s "Backup Verification Failure" <EMAIL>
fi
```

## Performance Monitoring

### Real-time Monitoring
```bash
# System resource monitoring
htop

# Database monitoring
mongostat --host localhost:27017

# Application monitoring
pm2 monit

# Network monitoring
iftop
```

### Performance Metrics Collection
```bash
#!/bin/bash
# Collect performance metrics

LOG_FILE="/var/log/shege-srs-performance.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# CPU usage
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)

# Memory usage
MEM_USAGE=$(free | awk 'NR==2{printf "%.2f", $3*100/$2}')

# Disk usage
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')

# Database connections
DB_CONNECTIONS=$(mongo --quiet --eval "db.serverStatus().connections.current")

# Log metrics
echo "$DATE,CPU:$CPU_USAGE,MEM:$MEM_USAGE,DISK:$DISK_USAGE,DB_CONN:$DB_CONNECTIONS" >> $LOG_FILE
```

## Security Maintenance

### Daily Security Tasks
- Review authentication logs
- Monitor failed login attempts
- Check for unusual file access patterns
- Verify backup encryption

### Weekly Security Tasks
- Update security patches
- Review user access permissions
- Scan for malware
- Check SSL certificate status

### Monthly Security Tasks
- Complete vulnerability scan
- Review and update firewall rules
- Audit user accounts
- Update security documentation

### Quarterly Security Tasks
- Penetration testing
- Security policy review
- Incident response plan testing
- Security training updates

## Troubleshooting Common Issues

### Application Performance Issues
```bash
# Check for memory leaks
ps aux | grep node
top -p $(pgrep node)

# Analyze slow queries
grep "slow" /var/log/mongodb/mongod.log

# Check for blocking operations
mongo --eval "db.currentOp()"
```

### Database Issues
```bash
# Check database integrity
mongo student-registration --eval "db.runCommand({validate: 'students'})"

# Repair database
mongod --repair --dbpath /var/lib/mongodb

# Check disk space
df -h /var/lib/mongodb
```

### File Upload Issues
```bash
# Check upload directory permissions
ls -la /opt/shege-srs/src/uploads/

# Check disk space
df -h /opt/shege-srs/

# Check file size limits
grep MAX_FILE_SIZE /opt/shege-srs/.env
```

### Network Connectivity Issues
```bash
# Check port availability
netstat -tlnp | grep :3000

# Test database connection
mongo --eval "db.runCommand('ping')"

# Check firewall status
sudo ufw status  # Ubuntu
sudo firewall-cmd --list-all  # CentOS
```

---

*This maintenance guide should be customized based on your specific environment and requirements. Regular review and updates are essential for maintaining system reliability.*
