#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 SHEGE SRS Simple Portable Builder');
console.log('====================================');

// Parse command line arguments
const args = process.argv.slice(2);
const platform = args[0] || 'all';

// Clean previous builds
const distDir = 'dist-simple';
if (fs.existsSync(distDir)) {
    console.log('🧹 Cleaning previous builds...');
    fs.rmSync(distDir, { recursive: true, force: true });
}

// Create dist directory
fs.mkdirSync(distDir, { recursive: true });

console.log('📦 Creating portable Node.js application...');

// Create the main structure
const platforms = platform === 'all' ? ['windows', 'linux', 'mac'] : [platform];

for (const targetPlatform of platforms) {
    console.log(`\n🔨 Building for ${targetPlatform}...`);
    
    const platformDir = path.join(distDir, `shege-srs-${targetPlatform}`);
    fs.mkdirSync(platformDir, { recursive: true });
    
    // Copy source files
    console.log('📁 Copying source files...');
    copyDirectory('src', path.join(platformDir, 'src'));
    copyDirectory('scripts', path.join(platformDir, 'scripts'));
    
    // Copy package files
    fs.copyFileSync('package.json', path.join(platformDir, 'package.json'));
    
    // Create .env file
    const envContent = `# SHEGE SRS Configuration
SYSTEM_NAME=SHEGE SRS
MONGODB_URI=mongodb://localhost:27017/student-registration
PORT=3000
NODE_ENV=production
SESSION_SECRET=shege-srs-secure-session-key-2024
`;
    fs.writeFileSync(path.join(platformDir, '.env'), envContent);
    
    // Create startup scripts
    createStartupScripts(platformDir, targetPlatform);
    
    // Create README
    createPlatformReadme(platformDir, targetPlatform);
    
    console.log(`✅ ${targetPlatform} build completed`);
}

// Create installer script
console.log('\n📝 Creating installer script...');
createInstallerScript();

console.log('\n🎉 Simple portable build completed!');
console.log(`📦 Output directory: ${distDir}/`);
console.log('\n📋 Built packages:');
fs.readdirSync(distDir).forEach(dir => {
    if (fs.statSync(path.join(distDir, dir)).isDirectory()) {
        console.log(`   - ${dir}/`);
    }
});

function copyDirectory(src, dest) {
    if (!fs.existsSync(src)) return;
    
    fs.mkdirSync(dest, { recursive: true });
    
    const items = fs.readdirSync(src);
    for (const item of items) {
        const srcPath = path.join(src, item);
        const destPath = path.join(dest, item);
        
        if (fs.statSync(srcPath).isDirectory()) {
            copyDirectory(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

function createStartupScripts(platformDir, targetPlatform) {
    const scriptsDir = path.join(platformDir, 'scripts');
    fs.mkdirSync(scriptsDir, { recursive: true });
    
    if (targetPlatform === 'windows') {
        // Windows batch file
        const batContent = `@echo off
title SHEGE SRS - Student Registration System
echo.
echo ========================================
echo   SHEGE SRS - Student Registration System
echo ========================================
echo.
echo Checking prerequisites...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js is installed
echo.

REM Check if MongoDB is running
echo Checking MongoDB connection...
timeout /t 2 /nobreak >nul

echo.
echo Installing dependencies...
cd /d "%~dp0.."
npm install --production --silent

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo ✅ Dependencies installed
echo.
echo Starting SHEGE SRS...
echo.
echo 🌐 Application will be available at: http://localhost:3000
echo 🔐 Login: admin / admin123
echo.
echo Press Ctrl+C to stop the server
echo.

node src/app.js
pause
`;
        fs.writeFileSync(path.join(scriptsDir, 'start.bat'), batContent);
        
        // Windows installer
        const installerContent = `@echo off
title SHEGE SRS - Setup
echo.
echo ========================================
echo   SHEGE SRS - Setup
echo ========================================
echo.
echo This will install SHEGE SRS dependencies
echo.
pause

cd /d "%~dp0.."
echo Installing dependencies...
npm install --production

if %errorlevel% equ 0 (
    echo.
    echo ✅ Setup completed successfully!
    echo.
    echo To start SHEGE SRS, run: scripts\\start.bat
    echo.
) else (
    echo.
    echo ❌ Setup failed!
    echo.
)

pause
`;
        fs.writeFileSync(path.join(scriptsDir, 'setup.bat'), installerContent);
        
    } else {
        // Unix shell script
        const shContent = `#!/bin/bash

echo ""
echo "========================================"
echo "  SHEGE SRS - Student Registration System"
echo "========================================"
echo ""
echo "Checking prerequisites..."
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed!"
    echo ""
    echo "Please install Node.js from: https://nodejs.org/"
    echo ""
    read -p "Press Enter to exit..."
    exit 1
fi

echo "✅ Node.js is installed"
echo ""

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed!"
    echo ""
    read -p "Press Enter to exit..."
    exit 1
fi

echo "✅ npm is installed"
echo ""

echo "Installing dependencies..."
cd "$(dirname "$0")/.."
npm install --production --silent

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    read -p "Press Enter to exit..."
    exit 1
fi

echo ""
echo "✅ Dependencies installed"
echo ""
echo "Starting SHEGE SRS..."
echo ""
echo "🌐 Application will be available at: http://localhost:3000"
echo "🔐 Login: admin / admin123"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

node src/app.js
`;
        fs.writeFileSync(path.join(scriptsDir, 'start.sh'), shContent);
        
        // Make executable
        try {
            execSync(`chmod +x "${path.join(scriptsDir, 'start.sh')}"`);
        } catch (e) {
            // Ignore chmod errors
        }
        
        // Unix installer
        const installerContent = `#!/bin/bash

echo ""
echo "========================================"
echo "  SHEGE SRS - Setup"
echo "========================================"
echo ""
echo "This will install SHEGE SRS dependencies"
echo ""
read -p "Press Enter to continue..."

cd "$(dirname "$0")/.."
echo "Installing dependencies..."
npm install --production

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Setup completed successfully!"
    echo ""
    echo "To start SHEGE SRS, run: ./scripts/start.sh"
    echo ""
else
    echo ""
    echo "❌ Setup failed!"
    echo ""
fi

read -p "Press Enter to exit..."
`;
        fs.writeFileSync(path.join(scriptsDir, 'setup.sh'), installerContent);
        
        try {
            execSync(`chmod +x "${path.join(scriptsDir, 'setup.sh')}"`);
        } catch (e) {
            // Ignore chmod errors
        }
    }
}

function createPlatformReadme(platformDir, targetPlatform) {
    const readmeContent = `# SHEGE SRS - Student Registration System (${targetPlatform.toUpperCase()})

## Quick Start

### Prerequisites
- Node.js 16+ installed
- MongoDB running on localhost:27017

### Installation
1. Run the setup script:
   ${targetPlatform === 'windows' ? '- Double-click `scripts/setup.bat`' : '- Run `./scripts/setup.sh`'}

### Running the Application
1. Start the application:
   ${targetPlatform === 'windows' ? '- Double-click `scripts/start.bat`' : '- Run `./scripts/start.sh`'}
2. Open your browser to: http://localhost:3000
3. Login with: admin / admin123

### Features
✅ Complete student registration system
✅ All form fields are optional
✅ CSV export functionality
✅ Print capabilities
✅ Student status management
✅ Cross-platform compatibility

### File Structure
- \`src/\` - Application source code
- \`scripts/\` - Startup and setup scripts
- \`.env\` - Configuration file
- \`package.json\` - Dependencies

### Troubleshooting
- Ensure Node.js is installed: \`node --version\`
- Ensure MongoDB is running on port 27017
- Check firewall settings for port 3000
- Run setup script if dependencies are missing

### Support
For issues or questions, check the application logs in the terminal.

Built for ${targetPlatform.toUpperCase()} - Portable Node.js Application
`;
    
    fs.writeFileSync(path.join(platformDir, 'README.md'), readmeContent);
}

function createInstallerScript() {
    const installerContent = `# SHEGE SRS - Cross-Platform Installer

## Available Builds

This package contains portable builds for multiple platforms:

- \`shege-srs-windows/\` - Windows version
- \`shege-srs-linux/\` - Linux version  
- \`shege-srs-mac/\` - macOS version

## Installation

1. Choose your platform folder
2. Run the setup script in the \`scripts/\` folder
3. Run the start script to launch the application

## Requirements

- Node.js 16+ 
- MongoDB running on localhost:27017

## Quick Start

### Windows
\`\`\`
cd shege-srs-windows
scripts\\setup.bat
scripts\\start.bat
\`\`\`

### Linux/Mac
\`\`\`
cd shege-srs-linux  # or shege-srs-mac
./scripts/setup.sh
./scripts/start.sh
\`\`\`

## Features

✅ Complete student registration system
✅ Optional form fields for flexibility
✅ CSV export and printing
✅ Student status management
✅ Cross-platform compatibility
✅ No installation required (portable)

Login: admin / admin123
URL: http://localhost:3000
`;
    
    fs.writeFileSync(path.join(distDir, 'README.md'), installerContent);
}
