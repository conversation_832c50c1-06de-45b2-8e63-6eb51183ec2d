#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class WebDistributionBuilder {
    constructor() {
        this.distDir = 'web-distribution';
        this.packageInfo = require('../package.json');
    }

    log(message) {
        console.log(`🌐 ${message}`);
    }

    success(message) {
        console.log(`✅ ${message}`);
    }

    error(message) {
        console.error(`❌ ${message}`);
    }

    async createDistribution() {
        this.log('Creating web distribution package...');
        
        // Clean and create distribution directory
        if (fs.existsSync(this.distDir)) {
            fs.rmSync(this.distDir, { recursive: true });
        }
        fs.mkdirSync(this.distDir, { recursive: true });
        
        // Copy application files (excluding sensitive files)
        this.copyApplicationFiles();
        
        // Create production package.json
        this.createProductionPackageJson();
        
        // Create environment template
        this.createEnvironmentTemplate();
        
        // Create deployment scripts
        this.createDeploymentScripts();
        
        // Create documentation
        this.createDocumentation();
        
        // Create Docker files
        this.createDockerFiles();
        
        this.success('Web distribution package created successfully!');
    }

    copyApplicationFiles() {
        this.log('Copying application files...');
        
        const filesToCopy = [
            'src/',
            'package.json',
            'package-lock.json'
        ];
        
        const filesToExclude = [
            'node_modules/',
            'dist/',
            'distribution/',
            '.git/',
            'electron-main.js',
            'electron-preload.js',
            'build-*.sh',
            '*.AppImage',
            '*.exe',
            '.env'
        ];
        
        // Copy source files
        this.copyDirectory('src', path.join(this.distDir, 'src'));
        
        // Copy package files
        fs.copyFileSync('package.json', path.join(this.distDir, 'package.json'));
        if (fs.existsSync('package-lock.json')) {
            fs.copyFileSync('package-lock.json', path.join(this.distDir, 'package-lock.json'));
        }
        
        this.success('Application files copied');
    }

    copyDirectory(src, dest) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }
        
        const items = fs.readdirSync(src);
        
        for (const item of items) {
            const srcPath = path.join(src, item);
            const destPath = path.join(dest, item);
            
            if (fs.statSync(srcPath).isDirectory()) {
                this.copyDirectory(srcPath, destPath);
            } else {
                fs.copyFileSync(srcPath, destPath);
            }
        }
    }

    createProductionPackageJson() {
        this.log('Creating production package.json...');
        
        const productionPackage = {
            name: "shege-srs-web",
            version: this.packageInfo.version,
            description: "SHEGE SRS - Student Registration System (Web Version)",
            main: "src/app.js",
            scripts: {
                start: "node src/app.js",
                setup: "node scripts/setup-production.js",
                health: "curl -f http://localhost:3000/health || exit 1"
            },
            dependencies: {
                // Copy only production dependencies
                ...Object.fromEntries(
                    Object.entries(this.packageInfo.dependencies || {})
                        .filter(([key]) => !key.includes('electron'))
                )
            },
            engines: {
                node: ">=16.0.0",
                npm: ">=8.0.0"
            },
            keywords: ["student-management", "education", "registration", "web-app"],
            author: this.packageInfo.author || "SHEGE SRS Team",
            license: this.packageInfo.license || "MIT"
        };
        
        fs.writeFileSync(
            path.join(this.distDir, 'package.json'),
            JSON.stringify(productionPackage, null, 2)
        );
        
        this.success('Production package.json created');
    }

    createEnvironmentTemplate() {
        this.log('Creating environment template...');
        
        const envTemplate = `# SHEGE SRS Web Application Configuration
# Copy this file to .env and configure for your environment

# System Configuration
SYSTEM_NAME=SHEGE SRS
SYSTEM_FULL_NAME=Student Registration System
NODE_ENV=production
PORT=3000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/student-registration
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/student-registration

# Security Configuration
SESSION_SECRET=change-this-to-a-secure-random-string
SESSION_TIMEOUT=3600000

# School Information
SCHOOL_NAME=Your School Name
SCHOOL_ADDRESS=Your School Address
SCHOOL_PHONE=******-567-8900
SCHOOL_EMAIL=<EMAIL>

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_PRINT_FEATURES=true
ENABLE_EXPORT_FEATURES=true

# File Upload Configuration
MAX_FILE_SIZE=5MB

# Admin Configuration (for initial setup)
ADMIN_PASSWORD=admin123
`;
        
        fs.writeFileSync(path.join(this.distDir, '.env.example'), envTemplate);
        this.success('Environment template created');
    }

    createDeploymentScripts() {
        this.log('Creating deployment scripts...');
        
        // Create scripts directory
        const scriptsDir = path.join(this.distDir, 'scripts');
        fs.mkdirSync(scriptsDir, { recursive: true });
        
        // Copy setup script
        if (fs.existsSync('scripts/setup-production.js')) {
            fs.copyFileSync('scripts/setup-production.js', path.join(scriptsDir, 'setup-production.js'));
        }
        
        // Create start script
        const startScript = `#!/bin/bash

echo "🚀 Starting SHEGE SRS Web Application"
echo "====================================="

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "📝 Creating .env from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install --production

# Setup database
echo "🔧 Setting up database..."
npm run setup

# Start application
echo "🌐 Starting web server..."
npm start
`;
        
        fs.writeFileSync(path.join(this.distDir, 'start.sh'), startScript);
        fs.chmodSync(path.join(this.distDir, 'start.sh'), '755');
        
        this.success('Deployment scripts created');
    }

    createDocumentation() {
        this.log('Creating documentation...');
        
        const readme = `# SHEGE SRS Web Application

## Quick Start

### Prerequisites
- Node.js 16+ 
- MongoDB 4.4+
- 2GB RAM minimum

### Installation

1. **Extract the package**
   \`\`\`bash
   tar -xzf shege-srs-web.tar.gz
   cd shege-srs-web
   \`\`\`

2. **Configure environment**
   \`\`\`bash
   cp .env.example .env
   # Edit .env with your settings
   \`\`\`

3. **Start the application**
   \`\`\`bash
   ./start.sh
   \`\`\`

4. **Access the application**
   - Open browser to http://localhost:3000
   - Login: admin / admin123
   - Change password immediately

## Docker Deployment

\`\`\`bash
# Build and run with Docker Compose
docker-compose up -d

# Access at http://localhost:3000
\`\`\`

## Cloud Deployment

### Heroku
[![Deploy](https://www.herokucdn.com/deploy/button.svg)](https://heroku.com/deploy?template=https://github.com/yourusername/shege-srs)

### Railway
[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/new/template?template=https://github.com/yourusername/shege-srs)

### DigitalOcean
[![Deploy to DO](https://www.deploytodo.com/do-btn-blue.svg)](https://cloud.digitalocean.com/apps/new?repo=https://github.com/yourusername/shege-srs)

## Configuration

### Database
- **Local MongoDB**: \`mongodb://localhost:27017/student-registration\`
- **MongoDB Atlas**: \`mongodb+srv://user:<EMAIL>/student-registration\`

### Security
- Change \`SESSION_SECRET\` to a secure random string
- Use strong admin password
- Enable HTTPS in production

## Features
- ✅ Student registration and management
- ✅ Photo uploads and document tracking
- ✅ Data export (CSV)
- ✅ ID card generation
- ✅ Print functionality
- ✅ Multi-quarter support
- ✅ Responsive web interface

## Support
- Documentation: See docs/ directory
- Issues: Contact system administrator
- Updates: Check for new releases

## License
MIT License - See LICENSE file for details
`;
        
        fs.writeFileSync(path.join(this.distDir, 'README.md'), readme);
        this.success('Documentation created');
    }

    createDockerFiles() {
        this.log('Creating Docker files...');
        
        // Copy Docker files
        if (fs.existsSync('Dockerfile')) {
            fs.copyFileSync('Dockerfile', path.join(this.distDir, 'Dockerfile'));
        }
        
        if (fs.existsSync('docker-compose.yml')) {
            fs.copyFileSync('docker-compose.yml', path.join(this.distDir, 'docker-compose.yml'));
        }
        
        if (fs.existsSync('docker-entrypoint.sh')) {
            fs.copyFileSync('docker-entrypoint.sh', path.join(this.distDir, 'docker-entrypoint.sh'));
        }
        
        if (fs.existsSync('nginx.conf')) {
            fs.copyFileSync('nginx.conf', path.join(this.distDir, 'nginx.conf'));
        }
        
        this.success('Docker files created');
    }

    async createArchive() {
        this.log('Creating distribution archive...');
        
        try {
            // Create tar.gz archive
            execSync(`tar -czf shege-srs-web-${this.packageInfo.version}.tar.gz -C ${this.distDir} .`);
            this.success(`Archive created: shege-srs-web-${this.packageInfo.version}.tar.gz`);
            
            // Show file size
            const stats = fs.statSync(`shege-srs-web-${this.packageInfo.version}.tar.gz`);
            const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
            console.log(`📦 Archive size: ${sizeInMB} MB`);
            
        } catch (error) {
            this.error(`Failed to create archive: ${error.message}`);
        }
    }

    async run() {
        console.log('🌐 SHEGE SRS Web Distribution Builder');
        console.log('====================================');
        
        try {
            await this.createDistribution();
            await this.createArchive();
            
            console.log('');
            console.log('🎉 Web distribution package created successfully!');
            console.log('');
            console.log('📦 Distribution contents:');
            console.log(`   Directory: ${this.distDir}/`);
            console.log(`   Archive: shege-srs-web-${this.packageInfo.version}.tar.gz`);
            console.log('');
            console.log('🚀 Deployment options:');
            console.log('   1. Extract archive on any server with Node.js');
            console.log('   2. Use Docker: docker-compose up -d');
            console.log('   3. Deploy to cloud platforms (Heroku, Railway, etc.)');
            console.log('   4. Use one-click deploy buttons');
            console.log('');
            
        } catch (error) {
            this.error(`Build failed: ${error.message}`);
            process.exit(1);
        }
    }
}

// Run if called directly
if (require.main === module) {
    const builder = new WebDistributionBuilder();
    builder.run();
}

module.exports = WebDistributionBuilder;
