#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 SHEGE SRS PKG Builder');
console.log('========================');

// Parse command line arguments
const args = process.argv.slice(2);
const platform = args.find(arg => ['--win', '--linux', '--mac', '--all'].includes(arg)) || '--all';

// Clean previous builds
const distDir = 'dist-pkg';
if (fs.existsSync(distDir)) {
    console.log('🧹 Cleaning previous builds...');
    fs.rmSync(distDir, { recursive: true, force: true });
}

// Create dist directory
fs.mkdirSync(distDir, { recursive: true });

// Ensure .env file exists
if (!fs.existsSync('.env')) {
    console.log('📝 Creating .env file...');
    const envContent = `# SHEGE SRS Configuration
SYSTEM_NAME=SHEGE SRS
MONGODB_URI=mongodb://localhost:27017/student-registration
PORT=3000
NODE_ENV=production
SESSION_SECRET=shege-srs-secure-session-key-2024
`;
    fs.writeFileSync('.env', envContent);
}

// Define build targets
const targets = {
    '--win': ['node18-win-x64'],
    '--linux': ['node18-linux-x64'],
    '--mac': ['node18-macos-x64'],
    '--all': ['node18-win-x64', 'node18-linux-x64', 'node18-macos-x64']
};

const selectedTargets = targets[platform];
console.log(`🎯 Building for: ${selectedTargets.join(', ')}`);

// Build for each target
for (const target of selectedTargets) {
    console.log(`\n🔨 Building ${target}...`);
    
    try {
        const outputName = getOutputName(target);
        const command = `npx pkg src/app.js --target ${target} --output ${distDir}/${outputName}`;
        
        console.log(`📦 Running: ${command}`);
        execSync(command, { stdio: 'inherit' });
        
        console.log(`✅ ${target} build completed: ${outputName}`);
        
        // Create a launch script for the executable
        createLaunchScript(target, outputName);
        
    } catch (error) {
        console.error(`❌ Failed to build ${target}:`, error.message);
    }
}

// Copy assets
console.log('\n📁 Copying assets...');
copyAssets();

// Create README
console.log('📝 Creating README...');
createReadme();

console.log('\n🎉 PKG build completed!');
console.log(`📦 Output directory: ${distDir}/`);
console.log('\n📋 Built executables:');
fs.readdirSync(distDir).forEach(file => {
    if (file.endsWith('.exe') || (!file.includes('.') && !file.endsWith('.txt') && !file.endsWith('.md'))) {
        const stats = fs.statSync(path.join(distDir, file));
        const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
        console.log(`   - ${file} (${sizeMB} MB)`);
    }
});

function getOutputName(target) {
    const platform = target.split('-')[1];
    const arch = target.split('-')[2];
    
    switch (platform) {
        case 'win':
            return `shege-srs-${arch}.exe`;
        case 'linux':
            return `shege-srs-${arch}`;
        case 'macos':
            return `shege-srs-macos-${arch}`;
        default:
            return `shege-srs-${platform}-${arch}`;
    }
}

function createLaunchScript(target, outputName) {
    const platform = target.split('-')[1];
    const scriptDir = path.join(distDir, 'scripts');
    
    if (!fs.existsSync(scriptDir)) {
        fs.mkdirSync(scriptDir, { recursive: true });
    }
    
    if (platform === 'win') {
        const batContent = `@echo off
echo Starting SHEGE SRS...
echo.
echo MongoDB must be running on localhost:27017
echo.
pause
echo.
echo Starting application...
cd /d "%~dp0.."
"${outputName}"
pause
`;
        fs.writeFileSync(path.join(scriptDir, 'start-windows.bat'), batContent);
    } else {
        const shContent = `#!/bin/bash
echo "Starting SHEGE SRS..."
echo ""
echo "MongoDB must be running on localhost:27017"
echo ""
read -p "Press Enter to continue..."
echo ""
echo "Starting application..."
cd "$(dirname "$0")/.."
./${outputName}
`;
        fs.writeFileSync(path.join(scriptDir, 'start-unix.sh'), shContent);
        
        // Make script executable
        try {
            execSync(`chmod +x "${path.join(scriptDir, 'start-unix.sh')}"`);
        } catch (e) {
            // Ignore chmod errors on non-Unix systems
        }
    }
}

function copyAssets() {
    // Copy views
    if (fs.existsSync('src/views')) {
        const viewsTarget = path.join(distDir, 'views');
        fs.mkdirSync(viewsTarget, { recursive: true });
        execSync(`cp -r src/views/* "${viewsTarget}/"`, { stdio: 'inherit' });
    }
    
    // Copy public assets
    if (fs.existsSync('src/public')) {
        const publicTarget = path.join(distDir, 'public');
        fs.mkdirSync(publicTarget, { recursive: true });
        execSync(`cp -r src/public/* "${publicTarget}/"`, { stdio: 'inherit' });
    }
    
    // Copy .env
    if (fs.existsSync('.env')) {
        fs.copyFileSync('.env', path.join(distDir, '.env'));
    }
}

function createReadme() {
    const readmeContent = `# SHEGE SRS - Portable Executables

## Quick Start

### Prerequisites
- MongoDB must be installed and running on localhost:27017
- No other dependencies required!

### Running the Application

#### Windows
1. Double-click \`scripts/start-windows.bat\`
2. Or run the executable directly: \`shege-srs-x64.exe\`

#### Linux/Mac
1. Run: \`./scripts/start-unix.sh\`
2. Or run the executable directly: \`./shege-srs-x64\`

### First Time Setup
1. Start the application
2. Open your browser to: http://localhost:3000
3. Login with: admin / admin123

### Features
✅ Completely portable - no installation required
✅ All dependencies bundled
✅ Cross-platform compatibility
✅ Source code protected

### File Structure
- \`shege-srs-*.exe\` - Windows executable
- \`shege-srs-*\` - Linux/Mac executable  
- \`scripts/\` - Launch scripts
- \`views/\` - Web templates
- \`public/\` - Static assets
- \`.env\` - Configuration file

### Troubleshooting
- Ensure MongoDB is running on port 27017
- Check firewall settings for port 3000
- Run executable from command line to see error messages

Built with PKG - Node.js executable packager
`;
    
    fs.writeFileSync(path.join(distDir, 'README.md'), readmeContent);
}
