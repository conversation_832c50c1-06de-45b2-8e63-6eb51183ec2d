#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class WebCompiledBuilder {
    constructor() {
        this.buildDir = 'build';
        this.distDir = 'web-compiled';
    }

    log(message) {
        console.log(`🔨 ${message}`);
    }

    success(message) {
        console.log(`✅ ${message}`);
    }

    error(message) {
        console.error(`❌ ${message}`);
    }

    async installBuildDependencies() {
        this.log('Installing build dependencies...');
        
        const buildDeps = [
            'webpack',
            'webpack-cli',
            'terser-webpack-plugin',
            'copy-webpack-plugin',
            'babel-loader',
            '@babel/core',
            '@babel/preset-env'
        ];

        try {
            execSync(`npm install --save-dev ${buildDeps.join(' ')}`, { stdio: 'inherit' });
            this.success('Build dependencies installed');
        } catch (error) {
            this.error('Failed to install build dependencies');
            throw error;
        }
    }

    async buildApplication() {
        this.log('Building application with webpack...');
        
        try {
            execSync('npx webpack --config webpack.config.js', { stdio: 'inherit' });
            this.success('Application built successfully');
        } catch (error) {
            this.error('Build failed');
            throw error;
        }
    }

    async createWebCompiledDistribution() {
        this.log('Creating web compiled distribution...');
        
        // Clean and create distribution directory
        if (fs.existsSync(this.distDir)) {
            fs.rmSync(this.distDir, { recursive: true });
        }
        fs.mkdirSync(this.distDir, { recursive: true });

        // Copy built files
        this.copyDirectory(this.buildDir, this.distDir);

        // Create production environment file
        this.createProductionEnv();

        // Create startup script
        this.createStartupScript();

        // Create web launcher
        this.createWebLauncher();

        // Create documentation
        this.createDocumentation();

        this.success('Web compiled distribution created');
    }

    copyDirectory(src, dest) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }
        
        const items = fs.readdirSync(src);
        
        for (const item of items) {
            const srcPath = path.join(src, item);
            const destPath = path.join(dest, item);
            
            if (fs.statSync(srcPath).isDirectory()) {
                this.copyDirectory(srcPath, destPath);
            } else {
                fs.copyFileSync(srcPath, destPath);
            }
        }
    }

    createProductionEnv() {
        this.log('Creating production environment...');
        
        const envContent = `# SHEGE SRS Production Configuration
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb://localhost:27017/student-registration
SESSION_SECRET=change-this-secure-secret-key
SYSTEM_NAME=SHEGE SRS

# School Information
SCHOOL_NAME=Your School Name
SCHOOL_ADDRESS=Your School Address
SCHOOL_PHONE=******-567-8900
SCHOOL_EMAIL=<EMAIL>

# Features
ENABLE_REGISTRATION=true
ENABLE_PRINT_FEATURES=true
ENABLE_EXPORT_FEATURES=true
MAX_FILE_SIZE=5MB
`;
        
        fs.writeFileSync(path.join(this.distDir, '.env'), envContent);
        this.success('Production environment created');
    }

    createStartupScript() {
        this.log('Creating startup scripts...');
        
        // Linux/Mac startup script
        const startScript = `#!/bin/bash

echo "🚀 Starting SHEGE SRS Web Application"
echo "====================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed"
    echo "Please install Node.js from https://nodejs.org"
    exit 1
fi

# Check if MongoDB is running
if ! pgrep mongod > /dev/null; then
    echo "⚠️  MongoDB is not running"
    echo "Please start MongoDB:"
    echo "  sudo systemctl start mongod"
    echo "  or install MongoDB from https://mongodb.com"
    echo ""
    echo "Continuing anyway (you can use external MongoDB)..."
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install --production
fi

# Create uploads directory
mkdir -p uploads

# Start the application
echo "🌐 Starting web server..."
echo "Application will be available at: http://localhost:3000"
echo "Default login: admin / admin123"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

node app.bundle.js
`;
        
        fs.writeFileSync(path.join(this.distDir, 'start.sh'), startScript);
        fs.chmodSync(path.join(this.distDir, 'start.sh'), '755');

        // Windows startup script
        const startBat = `@echo off
echo Starting SHEGE SRS Web Application
echo ====================================

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo Node.js is not installed
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
)

REM Install dependencies if needed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install --production
)

REM Create uploads directory
if not exist "uploads" mkdir uploads

REM Start the application
echo Starting web server...
echo Application will be available at: http://localhost:3000
echo Default login: admin / admin123
echo.
echo Press Ctrl+C to stop the server
echo.

node app.bundle.js
pause
`;
        
        fs.writeFileSync(path.join(this.distDir, 'start.bat'), startBat);
        this.success('Startup scripts created');
    }

    createWebLauncher() {
        this.log('Creating web launcher...');
        
        const launcherScript = `#!/usr/bin/env node

const { spawn } = require('child_process');
const { exec } = require('child_process');

console.log('🚀 SHEGE SRS Web Launcher');
console.log('========================');

// Start the server
const server = spawn('node', ['app.bundle.js'], {
    stdio: 'pipe'
});

let serverStarted = false;

server.stdout.on('data', (data) => {
    const output = data.toString();
    console.log(output);
    
    if (output.includes('Server running on') && !serverStarted) {
        serverStarted = true;
        console.log('');
        console.log('🌐 Opening web browser...');
        
        // Open browser after a short delay
        setTimeout(() => {
            const url = 'http://localhost:3000';
            
            // Cross-platform browser opening
            const platform = process.platform;
            let command;
            
            if (platform === 'darwin') {
                command = \`open \${url}\`;
            } else if (platform === 'win32') {
                command = \`start \${url}\`;
            } else {
                command = \`xdg-open \${url}\`;
            }
            
            exec(command, (error) => {
                if (error) {
                    console.log(\`Please open your browser and go to: \${url}\`);
                } else {
                    console.log(\`✅ Browser opened to: \${url}\`);
                }
            });
        }, 2000);
    }
});

server.stderr.on('data', (data) => {
    console.error(data.toString());
});

server.on('close', (code) => {
    console.log(\`Server process exited with code \${code}\`);
});

// Handle Ctrl+C
process.on('SIGINT', () => {
    console.log('\\n🛑 Stopping server...');
    server.kill();
    process.exit();
});

console.log('Starting server...');
console.log('Press Ctrl+C to stop');
`;
        
        fs.writeFileSync(path.join(this.distDir, 'launch.js'), launcherScript);
        fs.chmodSync(path.join(this.distDir, 'launch.js'), '755');

        // Create simple launcher scripts
        const quickLaunch = `#!/bin/bash
node launch.js
`;
        fs.writeFileSync(path.join(this.distDir, 'launch.sh'), quickLaunch);
        fs.chmodSync(path.join(this.distDir, 'launch.sh'), '755');

        const quickLaunchBat = `@echo off
node launch.js
pause
`;
        fs.writeFileSync(path.join(this.distDir, 'launch.bat'), quickLaunchBat);

        this.success('Web launcher created');
    }

    createDocumentation() {
        this.log('Creating documentation...');
        
        const readme = `# SHEGE SRS - Compiled Web Application

## 🚀 Quick Start

### Windows Users
1. Double-click \`launch.bat\`
2. Wait for browser to open automatically
3. Login with: admin / admin123

### Linux/Mac Users
1. Run: \`./launch.sh\`
2. Wait for browser to open automatically  
3. Login with: admin / admin123

### Manual Start
1. Run: \`./start.sh\` (Linux/Mac) or \`start.bat\` (Windows)
2. Open browser to: http://localhost:3000
3. Login with: admin / admin123

## 📋 Prerequisites

### Required
- **Node.js 16+**: Download from https://nodejs.org
- **MongoDB**: Download from https://mongodb.com

### Installation
1. Install Node.js from official website
2. Install MongoDB and start the service
3. Extract this application package
4. Run the launcher

## 🔧 Configuration

Edit the \`.env\` file to customize:
- School name and contact information
- Database connection settings
- System features and limits

## 🌐 Features

- ✅ Student registration and management
- ✅ Photo uploads and document tracking
- ✅ Data export (CSV format)
- ✅ ID card generation with guardian info
- ✅ Print functionality
- ✅ Multi-quarter support
- ✅ Responsive web interface

## 🔒 Security

- Source code is compiled and obfuscated
- Secure session management
- Input validation and sanitization
- File upload restrictions

## 📞 Support

- Default login: admin / admin123
- Change password after first login
- Ensure MongoDB is running
- Check firewall allows port 3000

## 🏗️ Technical Details

- **Compiled**: Source code is minified and obfuscated
- **Secure**: No source code exposure
- **Portable**: Self-contained application
- **Cross-platform**: Works on Windows, Mac, Linux

---

**Built with ❤️ for educational institutions**
`;
        
        fs.writeFileSync(path.join(this.distDir, 'README.md'), readme);
        this.success('Documentation created');
    }

    async createArchive() {
        this.log('Creating distribution archive...');
        
        try {
            const packageInfo = require('../package.json');
            const archiveName = `shege-srs-web-compiled-${packageInfo.version}.tar.gz`;
            
            execSync(`tar -czf ${archiveName} -C ${this.distDir} .`);
            
            const stats = fs.statSync(archiveName);
            const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
            
            this.success(`Archive created: ${archiveName} (${sizeInMB} MB)`);
            
            return archiveName;
        } catch (error) {
            this.error(`Failed to create archive: ${error.message}`);
            throw error;
        }
    }

    async run() {
        console.log('🔨 SHEGE SRS Compiled Web Builder');
        console.log('=================================');
        
        try {
            // Install build dependencies
            await this.installBuildDependencies();
            
            // Build the application
            await this.buildApplication();
            
            // Create distribution
            await this.createWebCompiledDistribution();
            
            // Create archive
            const archiveName = await this.createArchive();
            
            console.log('');
            console.log('🎉 Compiled web application created successfully!');
            console.log('');
            console.log('📦 Distribution:');
            console.log(`   Directory: ${this.distDir}/`);
            console.log(`   Archive: ${archiveName}`);
            console.log('');
            console.log('🚀 To test:');
            console.log(`   cd ${this.distDir}`);
            console.log('   ./launch.sh (Linux/Mac) or launch.bat (Windows)');
            console.log('');
            console.log('🌐 Features:');
            console.log('   ✅ Source code compiled and obfuscated');
            console.log('   ✅ Automatic browser opening');
            console.log('   ✅ Cross-platform compatibility');
            console.log('   ✅ Self-contained distribution');
            console.log('');
            
        } catch (error) {
            this.error(`Build failed: ${error.message}`);
            process.exit(1);
        }
    }
}

// Run if called directly
if (require.main === module) {
    const builder = new WebCompiledBuilder();
    builder.run();
}

module.exports = WebCompiledBuilder;
