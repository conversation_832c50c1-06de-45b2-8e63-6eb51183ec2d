<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Details - <%= student.fullName %></title>
    <link rel="stylesheet" href="/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">
                <h1><i class="fas fa-graduation-cap"></i> Student Registration System</h1>
            </div>
            <nav>
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/register"><i class="fas fa-user-plus"></i> Register Student</a>
                <a href="/students"><i class="fas fa-users"></i> View Students</a>
                <a href="/login"><i class="fas fa-sign-in-alt"></i> Login</a>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <h1><i class="fas fa-user"></i> Student Details</h1>
                <div style="display: flex; gap: 1rem;">
                    <a href="/students" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                    <a href="/students/<%= student._id %>/edit" class="btn">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="/students/<%= student._id %>/id-card" class="btn">
                        <i class="fas fa-id-card"></i> Generate ID Card
                    </a>
                </div>
            </div>

            <% if (success_msg && success_msg.length > 0) { %>
                <div class="flash-success"><i class="fas fa-check-circle"></i> <%= success_msg %></div>
            <% } %>
            <% if (error_msg && error_msg.length > 0) { %>
                <div class="flash-error"><i class="fas fa-exclamation-circle"></i> <%= error_msg %></div>
            <% } %>

            <div style="display: grid; grid-template-columns: 300px 1fr; gap: 2rem; margin-bottom: 2rem;">
                <!-- Student Photo Card -->
                <div class="card">
                    <div style="text-align: center; padding: 2rem;">
                        <% if (student.studentPic) { %>
                            <img src="/uploads/<%= student.studentPic %>" alt="<%= student.fullName %>"
                                 style="width: 200px; height: 200px; border-radius: 50%; object-fit: cover; border: 4px solid #667eea; margin-bottom: 1rem;">
                        <% } else { %>
                            <div style="width: 200px; height: 200px; border-radius: 50%; background: linear-gradient(135deg, #e1e8ed, #f8f9fa);
                                        display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; border: 4px solid #e1e8ed;">
                                <i class="fas fa-user" style="font-size: 4rem; color: #666;"></i>
                            </div>
                        <% } %>
                        <h2 style="color: #2c3e50; margin-bottom: 0.5rem;"><%= student.fullName %></h2>
                        <p style="color: #666; font-size: 1.1rem;">
                            <%= student.registeredGrade || 'N/A' %> - <%= student.section || 'N/A' %><br>
                            <strong>ID:</strong> <%= student.studentId || 'STU' + student._id.toString().slice(-6).toUpperCase() %>
                        </p>
                        <div style="display: flex; gap: 0.5rem; justify-content: center; margin-top: 1rem; flex-wrap: wrap;">
                            <span style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.9rem;">
                                <i class="fas fa-<%= student.sex === 'Male' ? 'mars' : 'venus' %>"></i> <%= student.sex || 'N/A' %>
                            </span>
                            <% if (student.age) { %>
                                <span style="background: linear-gradient(135deg, #27ae60, #229954); color: white; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.9rem;">
                                    <i class="fas fa-birthday-cake"></i> <%= student.age %> years
                                </span>
                            <% } %>
                            <% if (student.quarter) { %>
                                <span style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.9rem;">
                                    <i class="fas fa-calendar-check"></i> <%= student.quarter %>
                                </span>
                            <% } %>
                            <% if (student.registrationDate) { %>
                                <span style="background: linear-gradient(135deg, #9b59b6, #8e44ad); color: white; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.9rem;">
                                    <i class="fas fa-calendar-alt"></i> <%= new Date(student.registrationDate).toLocaleDateString() %>
                                </span>
                            <% } %>
                        </div>
                    </div>
                </div>

                <!-- Student Information Cards -->
                <div style="display: flex; flex-direction: column; gap: 1.5rem;">
                    <!-- Personal Information -->
                    <div class="card">
                        <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 1rem; border-radius: 15px 15px 0 0;">
                            <h3 style="margin: 0;"><i class="fas fa-user"></i> Personal Information</h3>
                        </div>
                        <div style="padding: 1.5rem;">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div>
                                    <p><strong><i class="fas fa-user"></i> Full Name:</strong> <%= student.fullName %></p>
                                    <p><strong><i class="fas fa-calendar"></i> Date of Birth:</strong> <%= student.dob ? new Date(student.dob).toLocaleDateString() : 'N/A' %></p>
                                    <p><strong><i class="fas fa-venus-mars"></i> Gender:</strong> <%= student.sex || 'N/A' %></p>
                                    <p><strong><i class="fas fa-tint"></i> Blood Type:</strong> <%= student.bloodType || 'N/A' %></p>
                                    <p><strong><i class="fas fa-language"></i> Mother Tongue:</strong> <%= student.motherTongue || 'N/A' %></p>
                                </div>
                                <div>
                                    <p><strong><i class="fas fa-weight"></i> Weight:</strong> <%= student.weight || 'N/A' %></p>
                                    <p><strong><i class="fas fa-ruler-vertical"></i> Height:</strong> <%= student.height || 'N/A' %></p>
                                    <p><strong><i class="fas fa-hand-paper"></i> Hand Use:</strong> <%= student.handUse || 'N/A' %></p>
                                    <p><strong><i class="fas fa-layer-group"></i> Former Grade:</strong> <%= student.formerGrade || 'N/A' %></p>
                                    <% if (student.seriousSickness) { %>
                                        <p><strong><i class="fas fa-notes-medical"></i> Medical Notes:</strong> <%= student.seriousSickness %></p>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Address Information -->
                    <% if (student.address) { %>
                        <div class="card">
                            <div style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 1rem; border-radius: 15px 15px 0 0;">
                                <h3 style="margin: 0;"><i class="fas fa-map-marker-alt"></i> Address Information</h3>
                            </div>
                            <div style="padding: 1.5rem;">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                    <div>
                                        <p><strong><i class="fas fa-city"></i> City:</strong> <%= student.address.city || 'N/A' %></p>
                                        <p><strong><i class="fas fa-building"></i> Sub City:</strong> <%= student.address.subCity || 'N/A' %></p>
                                        <p><strong><i class="fas fa-map"></i> Tabia:</strong> <%= student.address.tabia || 'N/A' %></p>
                                    </div>
                                    <div>
                                        <p><strong><i class="fas fa-home"></i> Ketena:</strong> <%= student.address.ketena || 'N/A' %></p>
                                        <p><strong><i class="fas fa-building"></i> Block:</strong> <%= student.address.block || 'N/A' %></p>
                                        <p><strong><i class="fas fa-flag"></i> Nationality:</strong> <%= student.address.nationality || 'N/A' %></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <% } %>

                    <!-- Parent Information -->
                    <div class="card">
                        <div style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 1rem; border-radius: 15px 15px 0 0;">
                            <h3 style="margin: 0;"><i class="fas fa-users"></i> Parent Information</h3>
                        </div>
                        <div style="padding: 1.5rem;">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                                <!-- Father's Information -->
                                <div>
                                    <h4 style="color: #2c3e50; margin-bottom: 1rem;"><i class="fas fa-male"></i> Father's Information</h4>
                                    <% if (student.father?.photo) { %>
                                        <img src="/uploads/<%= student.father.photo %>" alt="Father" style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover; margin-bottom: 1rem;">
                                    <% } %>
                                    <p><strong><i class="fas fa-user"></i> Name:</strong> <%= student.father?.fullName || 'N/A' %></p>
                                    <p><strong><i class="fas fa-phone"></i> Phone:</strong> <%= student.father?.phone || 'N/A' %></p>
                                    <p><strong><i class="fas fa-flag"></i> Nationality:</strong> <%= student.father?.nationality || 'N/A' %></p>
                                    <p><strong><i class="fas fa-graduation-cap"></i> Education:</strong> <%= student.father?.education || 'N/A' %></p>
                                    <p><strong><i class="fas fa-briefcase"></i> Occupation:</strong> <%= student.father?.occupation || 'N/A' %></p>
                                    <p><strong><i class="fas fa-building"></i> Workplace:</strong> <%= student.father?.workplace || 'N/A' %></p>
                                </div>

                                <!-- Mother's Information -->
                                <div>
                                    <h4 style="color: #2c3e50; margin-bottom: 1rem;"><i class="fas fa-female"></i> Mother's Information</h4>
                                    <% if (student.mother?.photo) { %>
                                        <img src="/uploads/<%= student.mother.photo %>" alt="Mother" style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover; margin-bottom: 1rem;">
                                    <% } %>
                                    <p><strong><i class="fas fa-user"></i> Name:</strong> <%= student.mother?.fullName || 'N/A' %></p>
                                    <p><strong><i class="fas fa-phone"></i> Phone:</strong> <%= student.mother?.phone || 'N/A' %></p>
                                    <p><strong><i class="fas fa-flag"></i> Nationality:</strong> <%= student.mother?.nationality || 'N/A' %></p>
                                    <p><strong><i class="fas fa-graduation-cap"></i> Education:</strong> <%= student.mother?.education || 'N/A' %></p>
                                    <p><strong><i class="fas fa-briefcase"></i> Occupation:</strong> <%= student.mother?.occupation || 'N/A' %></p>
                                    <p><strong><i class="fas fa-building"></i> Workplace:</strong> <%= student.mother?.workplace || 'N/A' %></p>
                                </div>
                            </div>

                            <!-- Guardian Information -->
                            <% if (student.guardian?.name) { %>
                                <hr style="margin: 2rem 0;">
                                <h4 style="color: #2c3e50; margin-bottom: 1rem;"><i class="fas fa-user-shield"></i> Guardian Information</h4>
                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                                    <p><strong><i class="fas fa-user"></i> Name:</strong> <%= student.guardian.name %></p>
                                    <p><strong><i class="fas fa-heart"></i> Relationship:</strong> <%= student.guardian.relationship %></p>
                                    <p><strong><i class="fas fa-phone"></i> Phone:</strong> <%= student.guardian.phone %></p>
                                </div>
                            <% } %>
                        </div>
                    </div>

                    <!-- Transportation & Documents -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
                        <!-- Transportation -->
                        <div class="card">
                            <div style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; padding: 1rem; border-radius: 15px 15px 0 0;">
                                <h3 style="margin: 0;"><i class="fas fa-bus"></i> Transportation</h3>
                            </div>
                            <div style="padding: 1.5rem;">
                                <% if (student.transport && (student.transport.subCity || student.transport.tabya || student.transport.bus)) { %>
                                    <p><strong><i class="fas fa-building"></i> Sub-City:</strong> <%= student.transport.subCity || 'N/A' %></p>
                                    <p><strong><i class="fas fa-map"></i> Tabya:</strong> <%= student.transport.tabya || 'N/A' %></p>
                                    <p><strong><i class="fas fa-bus"></i> Bus:</strong> <%= student.transport.bus || 'N/A' %></p>
                                    <p><strong><i class="fas fa-calendar"></i> Start Date:</strong> <%= student.transport.startDate ? new Date(student.transport.startDate).toLocaleDateString() : 'N/A' %></p>
                                <% } else { %>
                                    <p style="color: #666; text-align: center; padding: 2rem;">
                                        <i class="fas fa-info-circle"></i> No transportation information available.
                                    </p>
                                <% } %>
                            </div>
                        </div>

                        <!-- Documents -->
                        <div class="card">
                            <div style="background: linear-gradient(135deg, #27ae60, #229954); color: white; padding: 1rem; border-radius: 15px 15px 0 0;">
                                <h3 style="margin: 0;"><i class="fas fa-file-alt"></i> Documents</h3>
                            </div>
                            <div style="padding: 1.5rem;">
                                <% if (student.documents) { %>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                                            <i class="fas fa-<%= student.documents.birthCertificate ? 'check-circle' : 'times-circle' %>"
                                               style="color: <%= student.documents.birthCertificate ? '#27ae60' : '#e74c3c' %>;"></i>
                                            Birth Certificate
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                                            <i class="fas fa-<%= student.documents.transcript ? 'check-circle' : 'times-circle' %>"
                                               style="color: <%= student.documents.transcript ? '#27ae60' : '#e74c3c' %>;"></i>
                                            Transcript
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                                            <i class="fas fa-<%= student.documents.parentsID ? 'check-circle' : 'times-circle' %>"
                                               style="color: <%= student.documents.parentsID ? '#27ae60' : '#e74c3c' %>;"></i>
                                            Parents' ID
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                                            <i class="fas fa-<%= student.documents.studentPhoto ? 'check-circle' : 'times-circle' %>"
                                               style="color: <%= student.documents.studentPhoto ? '#27ae60' : '#e74c3c' %>;"></i>
                                            Student Photo
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                                            <i class="fas fa-<%= student.documents.buyBook ? 'check-circle' : 'times-circle' %>"
                                               style="color: <%= student.documents.buyBook ? '#27ae60' : '#e74c3c' %>;"></i>
                                            Book Purchase
                                        </div>
                                    </div>
                                <% } else { %>
                                    <p style="color: #666; text-align: center; padding: 2rem;">
                                        <i class="fas fa-info-circle"></i> No document information available.
                                    </p>
                                <% } %>
                            </div>
                        </div>
                    </div>

                    <!-- Special Notes -->
                    <% if (student.others && (student.others.shortSight || student.others.longSight || student.others.allergic || student.others.autism || student.others.languageProblem)) { %>
                        <div class="card">
                            <div style="background: linear-gradient(135deg, #9b59b6, #8e44ad); color: white; padding: 1rem; border-radius: 15px 15px 0 0;">
                                <h3 style="margin: 0;"><i class="fas fa-exclamation-triangle"></i> Special Notes</h3>
                            </div>
                            <div style="padding: 1.5rem;">
                                <div style="display: flex; flex-wrap: wrap; gap: 1rem;">
                                    <% if (student.others.shortSight) { %>
                                        <span style="background: #3498db; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                                            <i class="fas fa-eye"></i> Short Sight
                                        </span>
                                    <% } %>
                                    <% if (student.others.longSight) { %>
                                        <span style="background: #3498db; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                                            <i class="fas fa-eye"></i> Long Sight
                                        </span>
                                    <% } %>
                                    <% if (student.others.allergic) { %>
                                        <span style="background: #f39c12; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                                            <i class="fas fa-allergies"></i> Allergies
                                        </span>
                                    <% } %>
                                    <% if (student.others.autism) { %>
                                        <span style="background: #9b59b6; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                                            <i class="fas fa-brain"></i> Autism
                                        </span>
                                    <% } %>
                                    <% if (student.others.languageProblem) { %>
                                        <span style="background: #e74c3c; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                                            <i class="fas fa-comment-slash"></i> Language Problem
                                        </span>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
