<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Student Documents</title>
    <link rel="stylesheet" href="/styles-enhanced.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @media print {
            body { 
                background: white !important; 
                font-size: 11px;
                line-height: 1.3;
                margin: 0 !important;
                padding: 0 !important;
            }
            .no-print { display: none !important; }
            
            .document-page {
                page-break-after: always;
                padding: 20px;
                min-height: 100vh;
                box-sizing: border-box;
            }
            
            .document-page:last-child {
                page-break-after: auto;
            }
            
            .document-header {
                text-align: center;
                background: linear-gradient(135deg, #ff6b35, #ff8c42);
                color: white;
                padding: 20px;
                margin-bottom: 25px;
                border-radius: 12px 12px 0 0;
                position: relative;
            }

            .document-header::after {
                content: '';
                position: absolute;
                bottom: -10px;
                left: 50%;
                transform: translateX(-50%);
                width: 0;
                height: 0;
                border-left: 15px solid transparent;
                border-right: 15px solid transparent;
                border-top: 10px solid #ff8c42;
            }

            .document-header h1 {
                font-size: 24px !important;
                margin: 0 0 8px 0 !important;
                font-weight: 800;
                text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }

            .document-header p {
                margin: 3px 0 !important;
                font-size: 12px;
                opacity: 0.95;
            }
            
            .student-info {
                display: flex;
                align-items: flex-start;
                margin-bottom: 25px;
                border: 2px solid #ff6b35;
                border-radius: 12px;
                padding: 20px;
                background: linear-gradient(135deg, #fff8f5, #fff);
                box-shadow: 0 4px 12px rgba(255, 107, 53, 0.1);
            }
            
            .student-photo {
                width: 90px;
                height: 110px;
                border: 3px solid #ff6b35;
                border-radius: 8px;
                object-fit: cover;
                margin-right: 20px;
                flex-shrink: 0;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }
            
            .student-details {
                flex: 1;
            }
            
            .student-name {
                font-size: 18px;
                font-weight: 800;
                margin-bottom: 10px;
                text-transform: uppercase;
                color: #2c3e50;
                letter-spacing: 0.5px;
            }
            
            .student-basic {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 8px;
                font-size: 10px;
            }
            
            .documents-section {
                margin-top: 20px;
            }
            
            .documents-section h3 {
                background: linear-gradient(135deg, #2c3e50, #34495e);
                color: white;
                padding: 12px 16px;
                margin: 0 0 20px 0;
                font-size: 14px;
                font-weight: 700;
                text-transform: uppercase;
                border-radius: 8px;
                letter-spacing: 0.5px;
            }
            
            .documents-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
                margin-bottom: 15px;
            }
            
            .document-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                font-size: 11px;
                background: #fff;
                margin-bottom: 8px;
                transition: all 0.3s ease;
            }

            .document-item:hover {
                border-color: #ff6b35;
                box-shadow: 0 2px 8px rgba(255, 107, 53, 0.1);
            }
            
            .document-label {
                font-weight: 500;
                flex: 1;
            }
            
            .document-status {
                font-weight: bold;
                font-size: 11px;
                padding: 3px 8px;
                border-radius: 3px;
                min-width: 50px;
                text-align: center;
            }
            
            .document-status.yes {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            
            .document-status.no {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            
            .requirements-checklist {
                margin-top: 20px;
                border: 1px solid #000;
                padding: 15px;
            }
            
            .requirements-checklist h4 {
                margin: 0 0 10px 0;
                font-size: 12px;
                text-transform: uppercase;
                border-bottom: 1px solid #ccc;
                padding-bottom: 5px;
            }
            
            .checklist-item {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                font-size: 10px;
            }
            
            .checkbox {
                width: 12px;
                height: 12px;
                border: 1px solid #000;
                margin-right: 8px;
                display: inline-block;
                text-align: center;
                line-height: 10px;
                font-size: 8px;
            }
            
            .checkbox.checked {
                background: #000;
                color: white;
            }
            
            .signatures {
                margin-top: 30px;
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
            }
            
            .signature-box {
                text-align: center;
            }
            
            .signature-line {
                border-bottom: 1px solid #000;
                height: 30px;
                margin-bottom: 5px;
            }
            
            .signature-label {
                font-size: 9px;
                font-weight: bold;
                text-transform: uppercase;
            }
            
            .signature-date {
                font-size: 8px;
                margin-top: 3px;
            }
        }
    </style>
</head>
<body>
    <!-- Print Actions (No Print) -->
    <div class="print-actions no-print">
        <button onclick="window.print()" class="btn">
            <i class="fas fa-print"></i> Print Documents
        </button>
        <button onclick="window.history.back()" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back
        </button>
    </div>
    
    <% students.forEach((student, index) => { %>
        <div class="document-page">
            <div class="document-header">
                <h1>SHEGE SRS</h1>
                <p>Document Status Report</p>
                <p>Generated on: <%= new Date().toLocaleDateString() %> | Student <%= index + 1 %> of <%= students.length %></p>
            </div>
            
            <div class="student-info">
                <% if (student.studentPic) { %>
                    <img src="/uploads/<%= student.studentPic %>" alt="<%= student.fullName %>" class="student-photo">
                <% } else { %>
                    <div class="student-photo" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #999; font-size: 8px;">
                        NO PHOTO
                    </div>
                <% } %>
                
                <div class="student-details">
                    <div class="student-name"><%= student.fullName %></div>
                    <div class="student-basic">
                        <div><strong>Student ID:</strong> <%= student.studentId || 'N/A' %></div>
                        <div><strong>Grade:</strong> <%= student.registeredGrade || 'N/A' %></div>
                        <div><strong>Section:</strong> <%= student.section || 'N/A' %></div>
                        <div><strong>Quarter:</strong> <%= student.quarter || 'N/A' %></div>
                        <div><strong>Registration Date:</strong> <%= student.registrationDate ? new Date(student.registrationDate).toLocaleDateString() : 'N/A' %></div>
                        <div><strong>Status:</strong> <%= student.status || 'Active' %></div>
                    </div>
                </div>
            </div>
            
            <div class="documents-section">
                <h3>Document Submission Status</h3>
                <div class="documents-grid">
                    <div class="document-item">
                        <div class="document-label">Birth Certificate</div>
                        <div class="document-status <%= (student.documents && student.documents.birthCertificate) ? 'yes' : 'no' %>">
                            <%= (student.documents && student.documents.birthCertificate) ? '✓ YES' : '✗ NO' %>
                        </div>
                    </div>
                    <div class="document-item">
                        <div class="document-label">Academic Transcript</div>
                        <div class="document-status <%= (student.documents && student.documents.transcript) ? 'yes' : 'no' %>">
                            <%= (student.documents && student.documents.transcript) ? '✓ YES' : '✗ NO' %>
                        </div>
                    </div>
                    <div class="document-item">
                        <div class="document-label">Parents ID Documents</div>
                        <div class="document-status <%= (student.documents && student.documents.parentsID) ? 'yes' : 'no' %>">
                            <%= (student.documents && student.documents.parentsID) ? '✓ YES' : '✗ NO' %>
                        </div>
                    </div>
                    <div class="document-item">
                        <div class="document-label">Student Photo</div>
                        <div class="document-status <%= student.studentPic ? 'yes' : 'no' %>">
                            <%= student.studentPic ? '✓ YES' : '✗ NO' %>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="requirements-checklist">
                <h4>Registration Requirements Checklist</h4>
                <div class="checklist-item">
                    <span class="checkbox <%= (student.documents && student.documents.birthCertificate) ? 'checked' : '' %>">
                        <%= (student.documents && student.documents.birthCertificate) ? '✓' : '' %>
                    </span>
                    Birth Certificate (Original + Copy)
                </div>
                <div class="checklist-item">
                    <span class="checkbox <%= (student.documents && student.documents.transcript) ? 'checked' : '' %>">
                        <%= (student.documents && student.documents.transcript) ? '✓' : '' %>
                    </span>
                    Previous School Transcript (if applicable)
                </div>
                <div class="checklist-item">
                    <span class="checkbox <%= (student.documents && student.documents.parentsID) ? 'checked' : '' %>">
                        <%= (student.documents && student.documents.parentsID) ? '✓' : '' %>
                    </span>
                    Parent/Guardian ID Documents
                </div>
                <div class="checklist-item">
                    <span class="checkbox <%= student.studentPic ? 'checked' : '' %>">
                        <%= student.studentPic ? '✓' : '' %>
                    </span>
                    Student Passport Photo
                </div>
                <div class="checklist-item">
                    <span class="checkbox <%= (student.father && student.father.photo) ? 'checked' : '' %>">
                        <%= (student.father && student.father.photo) ? '✓' : '' %>
                    </span>
                    Father's Photo
                </div>
                <div class="checklist-item">
                    <span class="checkbox <%= (student.mother && student.mother.photo) ? 'checked' : '' %>">
                        <%= (student.mother && student.mother.photo) ? '✓' : '' %>
                    </span>
                    Mother's Photo
                </div>
                <div class="checklist-item">
                    <span class="checkbox <%= (student.documents && student.documents.buyBook) ? 'checked' : '' %>">
                        <%= (student.documents && student.documents.buyBook) ? '✓' : '' %>
                    </span>
                    School Books Purchase
                </div>
            </div>
            
            <div class="signatures">
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div class="signature-label">Registrar Signature</div>
                    <div class="signature-date">Date: ___________</div>
                </div>
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div class="signature-label">Parent/Guardian Signature</div>
                    <div class="signature-date">Date: ___________</div>
                </div>
            </div>
        </div>
    <% }) %>
</body>
</html>
