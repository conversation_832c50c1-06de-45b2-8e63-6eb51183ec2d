(()=>{var e={37:module=>{"use strict";module.exports=require("mongoose")},47:(module,e,t)=>{const r=t(37);module.exports=async()=>{try{await r.connect(process.env.MONGODB_URI||"mongodb://localhost:27017/student_registration")}catch(e){process.exit(1)}}},82:(e,exports,t)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var r=t(494),n=t(417),i=t(204),s=function(){function e(){}return e.prototype.createArrayCsvStringifier=function(e){var t=n.createFieldStringifier(e.fieldDelimiter,e.alwaysQuote);return new r.ArrayCsvStringifier(t,e.recordDelimiter,e.header)},e.prototype.createObjectCsvStringifier=function(e){var t=n.createFieldStringifier(e.fieldDelimiter,e.alwaysQuote);return new i.ObjectCsvStringifier(t,e.header,e.recordDelimiter,e.headerIdDelimiter)},e}();exports.CsvStringifierFactory=s},139:(e,exports,t)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var r=t(944),n=function(){function e(e){this.csvStringifierFactory=e}return e.prototype.createArrayCsvWriter=function(e){var t=this.csvStringifierFactory.createArrayCsvStringifier({header:e.header,fieldDelimiter:e.fieldDelimiter,recordDelimiter:e.recordDelimiter,alwaysQuote:e.alwaysQuote});return new r.CsvWriter(t,e.path,e.encoding,e.append)},e.prototype.createObjectCsvWriter=function(e){var t=this.csvStringifierFactory.createObjectCsvStringifier({header:e.header,fieldDelimiter:e.fieldDelimiter,recordDelimiter:e.recordDelimiter,headerIdDelimiter:e.headerIdDelimiter,alwaysQuote:e.alwaysQuote});return new r.CsvWriter(t,e.path,e.encoding,e.append)},e}();exports.CsvWriterFactory=n},204:function(e,exports,t){"use strict";var r,n=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(exports,"__esModule",{value:!0});var i=t(897),s=t(852),a=function(e){function t(t,r,n,i){var s=e.call(this,t,n)||this;return s.header=r,s.headerIdDelimiter=i,s}return n(t,e),t.prototype.getHeaderRecord=function(){return this.isObjectHeader?this.header.map(function(e){return e.title}):null},t.prototype.getRecordAsArray=function(e){var t=this;return this.fieldIds.map(function(r){return t.getNestedValue(e,r)})},t.prototype.getNestedValue=function(e,t){return this.headerIdDelimiter?t.split(this.headerIdDelimiter).reduce(function(e,t){return(e||{})[t]},e):e[t]},Object.defineProperty(t.prototype,"fieldIds",{get:function(){return this.isObjectHeader?this.header.map(function(e){return e.id}):this.header},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isObjectHeader",{get:function(){return s.isObject(this.header&&this.header[0])},enumerable:!0,configurable:!0}),t}(i.CsvStringifier);exports.ObjectCsvStringifier=a},242:(e,exports,t)=>{"use strict";var r=t(82),n=t(139),i=new r.CsvStringifierFactory,s=new n.CsvWriterFactory(i);exports.$d=function(e){return s.createObjectCsvWriter(e)}},252:module=>{"use strict";module.exports=require("express")},256:(module,e,t)=>{const r=t(252).Router(),n=t(578),i=t(461),s=t(928),a=t(571),o=t(242).$d,u=t(896),d=i.diskStorage({destination:function(e,t,r){r(null,s.join(__dirname,"../public/uploads"))},filename:function(e,t,r){r(null,Date.now()+"-"+t.originalname)}}),l=i({storage:d});function c(e){const t={};if(e.name&&(t.fullName={$regex:e.name,$options:"i"}),e.grade&&(t.registeredGrade={$regex:e.grade,$options:"i"}),e.section&&(t.section={$regex:e.section,$options:"i"}),e.quarter&&(t.quarter=e.quarter),e.sex&&(t.sex=e.sex),e.status?"all"===e.status||(t.status=e.status):t.$or=[{status:"active"},{status:{$exists:!1}},{status:null}],e.nameRange){const r=e.nameRange.split("-");if(2===r.length){const e=r[0].trim(),n=r[1].trim();1===e.length&&1===n.length?t.fullName={$regex:`^[${e.toUpperCase()}-${n.toUpperCase()}]`,$options:"i"}:t.fullName={$gte:e,$lte:n+"z"}}}return t}r.get("/login",a.loginForm),r.post("/login",a.login),r.get("/logout",a.logout),r.get("/register",a.ensureAdmin,(e,t)=>{t.render("register")}),r.post("/register",a.ensureAdmin,l.fields([{name:"studentPic",maxCount:1},{name:"fatherPic",maxCount:1},{name:"motherPic",maxCount:1},{name:"birthCertificate",maxCount:1},{name:"transcript",maxCount:1},{name:"parentsID",maxCount:1}]),async(e,t)=>{try{const{body:r,files:i}=e,s=e=>e&&"string"==typeof e?e.trim().replace(/[<>]/g,""):"",a=e=>{if(!e)return;const t=parseFloat(e);return isNaN(t)?void 0:t},o=e=>{if(!e)return;const t=new Date(e);return isNaN(t.getTime())?void 0:t},u=e=>"boolean"==typeof e?e:"string"==typeof e&&("true"===e.toLowerCase()||"on"===e.toLowerCase()||"1"===e);if(!r.fullName||!r.registeredGrade)return e.flash("error_msg","Full name and registered grade are required fields."),t.redirect("/register");const d={city:s(r["address.city"]||r.addressCity),subCity:s(r["address.subCity"]||r.addressSubCity),tabia:s(r["address.tabia"]||r.addressTabia),ketena:s(r["address.ketena"]||r.addressKetena),block:s(r["address.block"]||r.addressBlock),nationality:s(r["address.nationality"]||r.addressNationality)},l=new n({registrationDate:o(r.registrationDate)||new Date,quarter:s(r.quarter),fullName:s(r.fullName),sex:s(r.sex),dob:o(r.dob),age:a(r.age),formerGrade:s(r.formerGrade),registeredGrade:s(r.registeredGrade),section:s(r.section),motherTongue:s(r.motherTongue),bloodType:s(r.bloodType),weight:a(r.weight),height:a(r.height),handUse:s(r.handUse),address:d,seriousSickness:s(r.seriousSickness),studentPic:i.studentPic?i.studentPic[0].filename:"",father:{fullName:s(r.fatherFullName),phone:s(r.fatherPhone),nationality:s(r.fatherNationality),education:s(r.fatherEducation),occupation:s(r.fatherOccupation),workplace:s(r.fatherWorkplace),photo:i.fatherPic?i.fatherPic[0].filename:""},mother:{fullName:s(r.motherFullName),phone:s(r.motherPhone),nationality:s(r.motherNationality),education:s(r.motherEducation),occupation:s(r.motherOccupation),workplace:s(r.motherWorkplace),photo:i.motherPic?i.motherPic[0].filename:""},guardian:{name:s(r.guardianName),relationship:s(r.guardianRelationship),phone:s(r.guardianPhone)},transport:{subCity:s(r.transportSubCity),tabya:s(r.transportTabya),bus:s(r.transportBus),startDate:o(r.transportStartDate)},documents:{birthCertificate:i.birthCertificate?i.birthCertificate[0].filename:"",transcript:i.transcript?i.transcript[0].filename:"",parentsID:i.parentsID?i.parentsID[0].filename:"",buyBook:u(r.buyBook)},others:{shortSight:u(r.shortSight),longSight:u(r.longSight),allergic:u(r.allergic),autism:u(r.autism),languageProblem:u(r.languageProblem)}}),c=l.validateSync();if(c){const r=Object.values(c.errors).map(e=>e.message);return e.flash("error_msg","Validation failed: "+r.join(", ")),t.redirect("/register")}await l.save(),e.flash("success_msg","Student registered successfully!"),t.redirect("/students")}catch(r){let n="Registration failed. Please try again.";if("ValidationError"===r.name){n="Validation failed: "+Object.values(r.errors).map(e=>"required"===e.kind?`${e.path} is required`:"enum"===e.kind?`${e.path} must be one of: ${e.enumValues.join(", ")}`:"Number"===e.kind?`${e.path} must be a valid number`:"Date"===e.kind?`${e.path} must be a valid date`:"Boolean"===e.kind?`${e.path} must be true or false`:e.message).join(", ")}else 11e3===r.code?n="A student with this information already exists.":"CastError"===r.name&&(n=`Invalid data format for ${r.path}: ${r.value}`);e.flash("error_msg",n),t.redirect("/register")}}),r.get("/students",a.ensureAdmin,async(e,t)=>{try{const r={};e.query.name&&(r.fullName={$regex:e.query.name,$options:"i"}),e.query.grade&&(r.registeredGrade={$regex:e.query.grade,$options:"i"}),e.query.section&&(r.section={$regex:e.query.section,$options:"i"}),e.query.studentId&&(r.studentId={$regex:e.query.studentId,$options:"i"}),e.query.quarter&&(r.quarter=e.query.quarter),e.query.sex&&(r.sex=e.query.sex),e.query.status?"all"===e.query.status||(r.status=e.query.status):r.$or=[{status:"active"},{status:{$exists:!1}},{status:null}];const i=await n.find(r).sort({registrationDate:-1});t.render("index",{students:i,name:e.query.name,grade:e.query.grade,section:e.query.section,studentId:e.query.studentId,quarter:e.query.quarter,sex:e.query.sex,status:e.query.status})}catch(r){e.flash("error_msg","Error loading students"),t.render("index",{students:[]})}}),r.get("/students/:id/edit",a.ensureAdmin,async(e,t)=>{const r=await n.findById(e.params.id);t.render("edit",{student:r})}),r.post("/students/:id",a.ensureAdmin,l.fields([{name:"studentPic",maxCount:1},{name:"fatherPic",maxCount:1},{name:"motherPic",maxCount:1},{name:"birthCertificate",maxCount:1},{name:"transcript",maxCount:1},{name:"parentsID",maxCount:1}]),async(e,t)=>{try{const{body:r,files:i}=e,s={registrationDate:r.registrationDate,quarter:r.quarter,fullName:r.fullName,sex:r.sex,dob:r.dob,age:r.age,formerGrade:r.formerGrade,registeredGrade:r.registeredGrade,section:r.section,motherTongue:r.motherTongue,bloodType:r.bloodType,weight:r.weight,height:r.height,handUse:r.handUse,seriousSickness:r.seriousSickness,studentPic:i.studentPic?i.studentPic[0].filename:void 0,parentPics:i.parentPics?i.parentPics.map(e=>e.filename):void 0,father:{fullName:r.fatherFullName,phone:r.fatherPhone,nationality:r.fatherNationality,education:r.fatherEducation,occupation:r.fatherOccupation,workplace:r.fatherWorkplace,photo:i.parentPics&&i.parentPics[0]?i.parentPics[0].filename:void 0},mother:{fullName:r.motherFullName,phone:r.motherPhone,nationality:r.motherNationality,education:r.motherEducation,occupation:r.motherOccupation,workplace:r.motherWorkplace,photo:i.parentPics&&i.parentPics[1]?i.parentPics[1].filename:void 0},guardian:{name:r.guardianName,relationship:r.guardianRelationship,phone:r.guardianPhone},transport:{subCity:r.transportSubCity,tabya:r.transportTabya,bus:r.transportBus,startDate:r.transportStartDate},documents:{birthCertificate:!!r.birthCertificate,transcript:!!r.transcript,parentsID:!!r.parentsID,studentPhoto:!!r.studentPhoto,fatherPhoto:!!r.fatherPhoto,motherPhoto:!!r.motherPhoto,buyBook:!!r.buyBook},others:{shortSight:!!r.shortSight,longSight:!!r.longSight,allergic:!!r.allergic,autism:!!r.autism,languageProblem:!!r.languageProblem}};Object.keys(s).forEach(e=>{void 0===s[e]&&delete s[e]}),await n.findByIdAndUpdate(e.params.id,s),e.flash("success_msg","Student updated successfully!"),t.redirect("/students")}catch(r){e.flash("error_msg","Update failed."),t.redirect(`/students/${e.params.id}/edit`)}}),r.get("/students/:id/id-card",a.ensureAdmin,async(e,t)=>{try{const r=await n.findById(e.params.id);if(!r)return e.flash("error_msg","Student not found"),t.redirect("/students");t.render("id-card",{student:r})}catch(r){e.flash("error_msg","Failed to generate ID card"),t.redirect("/students")}}),r.get("/students/:id",a.ensureAdmin,async(e,t)=>{try{const r=await n.findById(e.params.id);if(!r)return e.flash("error_msg","Student not found"),t.redirect("/students");t.render("students/view",{student:r})}catch(r){e.flash("error_msg","Failed to load student details"),t.redirect("/students")}}),r.get("/dashboard",a.ensureAdmin,async(e,t)=>{try{const e=await n.find({}).sort({registrationDate:-1}),r={total:e.length,active:e.filter(e=>"active"===(e.status||"active")).length,pending:e.filter(e=>"pending"===e.status).length,graduated:e.filter(e=>"graduated"===e.status).length,transferred:e.filter(e=>"transferred"===e.status).length,male:e.filter(e=>"Male"===e.sex).length,female:e.filter(e=>"Female"===e.sex).length,quarter1:e.filter(e=>"Quarter 1"===e.quarter).length,quarter2:e.filter(e=>"Quarter 2"===e.quarter).length,quarter3:e.filter(e=>"Quarter 3"===e.quarter).length,quarter4:e.filter(e=>"Quarter 4"===e.quarter).length,thisMonth:e.filter(e=>{const t=new Date(e.registrationDate),r=new Date;return t.getMonth()===r.getMonth()&&t.getFullYear()===r.getFullYear()}).length,thisWeek:e.filter(e=>{const t=new Date(e.registrationDate),r=new Date;return t>=new Date(r.getTime()-6048e5)}).length},i=e.filter(e=>"active"===e.status||!e.status).slice(0,10),s=e.filter(e=>"pending"===e.status).slice(0,10),a={};e.filter(e=>"active"===e.status||!e.status).forEach(e=>{const t=e.registeredGrade||"Unspecified";a[t]=(a[t]||0)+1}),t.render("dashboard",{stats:r,recentStudents:i,pendingStudents:s,gradeStats:a,totalStudents:e.length})}catch(r){e.flash("error_msg","Error loading dashboard"),t.render("dashboard",{stats:{},recentStudents:[],gradeStats:{},totalStudents:0})}}),r.get("/students/export/csv",a.ensureAdmin,async(e,t)=>{try{const r={};e.query.name&&(r.fullName={$regex:e.query.name,$options:"i"}),e.query.grade&&(r.registeredGrade={$regex:e.query.grade,$options:"i"}),e.query.section&&(r.section={$regex:e.query.section,$options:"i"}),e.query.studentId&&(r.studentId={$regex:e.query.studentId,$options:"i"}),e.query.quarter&&(r.quarter=e.query.quarter),e.query.sex&&(r.sex=e.query.sex);const i=await n.find(r).sort({registrationDate:-1}),a=s.join(__dirname,"../public/exports/students.csv"),d=s.dirname(a);u.existsSync(d)||u.mkdirSync(d,{recursive:!0});const l=o({path:a,header:[{id:"studentId",title:"Student ID"},{id:"fullName",title:"Full Name"},{id:"sex",title:"Sex"},{id:"dob",title:"Date of Birth"},{id:"age",title:"Age"},{id:"registeredGrade",title:"Grade"},{id:"section",title:"Section"},{id:"quarter",title:"Quarter"},{id:"registrationDate",title:"Registration Date"},{id:"motherTongue",title:"Mother Tongue"},{id:"bloodType",title:"Blood Type"},{id:"weight",title:"Weight"},{id:"height",title:"Height"},{id:"handUse",title:"Hand Use"},{id:"addressCity",title:"City"},{id:"addressSubCity",title:"Sub City"},{id:"addressTabia",title:"Tabia"},{id:"addressKetena",title:"Ketena"},{id:"addressBlock",title:"Block"},{id:"addressNationality",title:"Nationality"},{id:"seriousSickness",title:"Serious Sickness"},{id:"fatherName",title:"Father Name"},{id:"fatherPhone",title:"Father Phone"},{id:"fatherOccupation",title:"Father Occupation"},{id:"motherName",title:"Mother Name"},{id:"motherPhone",title:"Mother Phone"},{id:"motherOccupation",title:"Mother Occupation"},{id:"guardianName",title:"Guardian Name"},{id:"guardianRelationship",title:"Guardian Relationship"},{id:"guardianPhone",title:"Guardian Phone"},{id:"transportSubCity",title:"Transport Sub City"},{id:"transportBus",title:"Transport Bus"},{id:"shortSight",title:"Short Sight"},{id:"longSight",title:"Long Sight"},{id:"allergic",title:"Allergic"},{id:"autism",title:"Autism"},{id:"languageProblem",title:"Language Problem"}]}),c=i.map(e=>{var t,r,n,i,s,a,o,u,d,l,c,f,h,p,g,m,y,v,S,b,_,w;return{studentId:e.studentId||"",fullName:e.fullName||"",sex:e.sex||"",dob:e.dob?new Date(e.dob).toLocaleDateString():"",age:e.age||"",registeredGrade:e.registeredGrade||"",section:e.section||"",quarter:e.quarter||"",registrationDate:e.registrationDate?new Date(e.registrationDate).toLocaleDateString():"",motherTongue:e.motherTongue||"",bloodType:e.bloodType||"",weight:e.weight||"",height:e.height||"",handUse:e.handUse||"",addressCity:(null===(t=e.address)||void 0===t?void 0:t.city)||"",addressSubCity:(null===(r=e.address)||void 0===r?void 0:r.subCity)||"",addressTabia:(null===(n=e.address)||void 0===n?void 0:n.tabia)||"",addressKetena:(null===(i=e.address)||void 0===i?void 0:i.ketena)||"",addressBlock:(null===(s=e.address)||void 0===s?void 0:s.block)||"",addressNationality:(null===(a=e.address)||void 0===a?void 0:a.nationality)||"",seriousSickness:e.seriousSickness||"",fatherName:(null===(o=e.father)||void 0===o?void 0:o.fullName)||"",fatherPhone:(null===(u=e.father)||void 0===u?void 0:u.phone)||"",fatherOccupation:(null===(d=e.father)||void 0===d?void 0:d.occupation)||"",motherName:(null===(l=e.mother)||void 0===l?void 0:l.fullName)||"",motherPhone:(null===(c=e.mother)||void 0===c?void 0:c.phone)||"",motherOccupation:(null===(f=e.mother)||void 0===f?void 0:f.occupation)||"",guardianName:(null===(h=e.guardian)||void 0===h?void 0:h.name)||"",guardianRelationship:(null===(p=e.guardian)||void 0===p?void 0:p.relationship)||"",guardianPhone:(null===(g=e.guardian)||void 0===g?void 0:g.phone)||"",transportSubCity:(null===(m=e.transport)||void 0===m?void 0:m.subCity)||"",transportBus:(null===(y=e.transport)||void 0===y?void 0:y.bus)||"",shortSight:null!==(v=e.others)&&void 0!==v&&v.shortSight?"Yes":"No",longSight:null!==(S=e.others)&&void 0!==S&&S.longSight?"Yes":"No",allergic:null!==(b=e.others)&&void 0!==b&&b.allergic?"Yes":"No",autism:null!==(_=e.others)&&void 0!==_&&_.autism?"Yes":"No",languageProblem:null!==(w=e.others)&&void 0!==w&&w.languageProblem?"Yes":"No"}});await l.writeRecords(c);const f=`students_export_${(new Date).toISOString().split("T")[0]}.csv`;t.setHeader("Content-Type","text/csv"),t.setHeader("Content-Disposition",`attachment; filename="${f}"`),t.download(a,f,r=>{r?(e.flash("error_msg","Failed to download CSV file"),t.redirect("/students")):setTimeout(()=>{u.existsSync(a)&&u.unlinkSync(a)},5e3)})}catch(r){e.flash("error_msg","Failed to export students to CSV"),t.redirect("/students")}}),r.get("/students/:id/print",a.ensureAdmin,async(e,t)=>{try{const r=await n.findById(e.params.id);if(!r)return e.flash("error_msg","Student not found"),t.redirect("/students");t.render("print-student",{student:r})}catch(r){e.flash("error_msg","Failed to load student for printing"),t.redirect("/students")}}),r.post("/students/print/id-cards",a.ensureAdmin,async(e,t)=>{try{const{studentIds:r}=e.body;let i;if(r&&r.length>0)i=await n.find({_id:{$in:r}});else{const t={};e.body.name&&(t.fullName={$regex:e.body.name,$options:"i"}),e.body.grade&&(t.registeredGrade={$regex:e.body.grade,$options:"i"}),e.body.section&&(t.section={$regex:e.body.section,$options:"i"}),e.body.quarter&&(t.quarter=e.body.quarter),e.body.sex&&(t.sex=e.body.sex),i=await n.find(t).sort({registrationDate:-1})}t.render("print-id-cards",{students:i})}catch(r){e.flash("error_msg","Failed to load students for printing"),t.redirect("/students")}}),r.post("/students/export/selected",a.ensureAdmin,async(e,t)=>{try{const{studentIds:r}=e.body,i=await n.find({_id:{$in:r}}).sort({registrationDate:-1}),s=generateCSV(i),a=`selected_students_${(new Date).toISOString().split("T")[0]}.csv`;t.setHeader("Content-Type","text/csv"),t.setHeader("Content-Disposition",`attachment; filename="${a}"`),t.send(s)}catch(r){e.flash("error_msg","Failed to export selected students"),t.redirect("/students")}}),r.post("/students/print/documents",a.ensureAdmin,async(e,t)=>{try{const{studentIds:r}=e.body,i=await n.find({_id:{$in:r}}).sort({registrationDate:-1});t.render("print-documents",{students:i})}catch(r){e.flash("error_msg","Failed to load students for document printing"),t.redirect("/students")}}),r.get("/students/export/pdf",a.ensureAdmin,async(e,t)=>{try{const r=c(e.query),i=await n.find(r).sort({registrationDate:-1});t.render("print-filtered-students",{students:i,filters:e.query})}catch(r){e.flash("error_msg","Failed to generate PDF export"),t.redirect("/students")}}),r.get("/students/print/filtered",a.ensureAdmin,async(e,t)=>{try{const r=c(e.query),i=await n.find(r).sort({registrationDate:-1});t.render("print-filtered-students",{students:i,filters:e.query})}catch(r){e.flash("error_msg","Failed to load filtered students for printing"),t.redirect("/students")}}),r.post("/students/:id/status",a.ensureAdmin,async(e,t)=>{try{const{status:r,reason:i,notes:s}=e.body,a=await n.findById(e.params.id);if(!a)return e.flash("error_msg","Student not found"),t.redirect("/students");await n.findByIdAndUpdate(e.params.id,{status:r,statusDate:new Date,statusReason:i||"",statusNotes:s||""},{new:!0});const o="pending"===r?"marked as pending":"graduated"===r?"marked as graduated":"transferred"===r?"marked as transferred":"reactivated";e.flash("success_msg",`Student ${a.fullName} has been ${o}`),t.redirect("/students")}catch(r){e.flash("error_msg","Failed to update student status"),t.redirect("/students")}}),r.get("/students/:id/status",a.ensureAdmin,async(e,t)=>{try{const r=await n.findById(e.params.id);if(!r)return e.flash("error_msg","Student not found"),t.redirect("/students");t.render("change-status",{student:r})}catch(r){e.flash("error_msg","Failed to load status change form"),t.redirect("/students")}}),r.get("/",(e,t)=>{t.redirect("/dashboard")}),module.exports=r},320:(module,e,t)=>{const r=t(252).Router(),{isAuthenticated:n,isAdmin:i}=t(997),s=t(623);r.get("/dashboard",n,i,s.getDashboard),r.get("/users",n,i,s.getUsers),r.post("/users",n,i,s.createUser),r.delete("/users/:id",n,i,s.deleteUser),module.exports=r},408:function(e,exports,t){"use strict";var r=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function a(e){try{u(n.next(e))}catch(e){s(e)}}function o(e){try{u(n.throw(e))}catch(e){s(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(a,o)}u((n=n.apply(e,t||[])).next())})},n=this&&this.__generator||function(e,t){var r,n,i,s,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return s={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function o(s){return function(o){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){a.label=s[1];break}if(6===s[0]&&a.label<i[1]){a.label=i[1],i=s;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(s);break}i[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,o])}}};Object.defineProperty(exports,"__esModule",{value:!0});var i=t(930),s=t(896),a=i.promisify(s.writeFile),o=function(){function e(e,t,r){void 0===r&&(r="utf8"),this.path=e,this.append=t,this.encoding=r}return e.prototype.write=function(e){return r(this,void 0,void 0,function(){return n(this,function(t){switch(t.label){case 0:return[4,a(this.path,e,this.getWriteOption())];case 1:return t.sent(),this.append=!0,[2]}})})},e.prototype.getWriteOption=function(){return{encoding:this.encoding,flag:this.append?"a":"w"}},e}();exports.FileWriter=o},417:function(e,exports){"use strict";var t,r=this&&this.__extends||(t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},t(e,r)},function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)});Object.defineProperty(exports,"__esModule",{value:!0});var n=[",",";"],i=function(){function e(e){this.fieldDelimiter=e}return e.prototype.isEmpty=function(e){return null==e||""===e},e.prototype.quoteField=function(e){return'"'+e.replace(/"/g,'""')+'"'},e}();exports.FieldStringifier=i;var s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.stringify=function(e){if(this.isEmpty(e))return"";var t=String(e);return this.needsQuote(t)?this.quoteField(t):t},t.prototype.needsQuote=function(e){return e.includes(this.fieldDelimiter)||e.includes("\n")||e.includes('"')},t}(i),a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.stringify=function(e){return this.isEmpty(e)?"":this.quoteField(String(e))},t}(i);exports.createFieldStringifier=function(e,t){return void 0===e&&(e=","),void 0===t&&(t=!1),function(e){if(-1===n.indexOf(e))throw new Error("Invalid field delimiter `"+e+"` is specified")}(e),t?new a(e):new s(e)}},438:(module,e,t)=>{const r=t(37),n=new r.Schema({username:{type:String,required:!0,unique:!0},password:{type:String,required:!0}}),i=r.model("User",n);module.exports=i},461:module=>{"use strict";module.exports=require("multer")},494:function(e,exports,t){"use strict";var r,n=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},r(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(exports,"__esModule",{value:!0});var i=function(e){function t(t,r,n){var i=e.call(this,t,r)||this;return i.header=n,i}return n(t,e),t.prototype.getHeaderRecord=function(){return this.header},t.prototype.getRecordAsArray=function(e){return e},t}(t(897).CsvStringifier);exports.ArrayCsvStringifier=i},571:(e,exports,t)=>{t(578);const r=t(438),n=t(988);exports.register=async(e,t)=>{},exports.list=async(e,t)=>{},exports.edit=async(e,t)=>{},exports.update=async(e,t)=>{},exports.ensureAdmin=(e,t,r)=>{if(e.session&&e.session.user&&e.session.username)return r();e.flash("error_msg","Please log in to view this page."),t.redirect("/login")},exports.loginForm=(e,t)=>{t.render("auth/login")},exports.login=async(e,t)=>{try{const{username:i,password:s}=e.body,a=await r.findOne({username:i});if(a&&await n.compare(s,a.password))return e.session.user=a,e.session.username=a.username,e.session.isAdmin=!0,e.session.userId=a._id,e.flash("success_msg","Login successful!"),t.redirect("/dashboard");e.flash("error_msg","Invalid username or password"),t.redirect("/login")}catch(r){e.flash("error_msg","Login failed. Please try again."),t.redirect("/login")}},exports.logout=(e,t)=>{e.session.destroy(()=>{t.redirect("/login")})}},578:(module,e,t)=>{const r=t(37),n=new r.Schema({fullName:String,phone:String,nationality:String,education:String,occupation:String,workplace:String,photo:String}),i=new r.Schema({name:String,relationship:String,phone:String}),s=new r.Schema({subCity:String,tabya:String,bus:String,startDate:Date}),a=new r.Schema({birthCertificate:String,transcript:String,parentsID:String,studentPhoto:String,fatherPhoto:String,motherPhoto:String,buyBook:Boolean}),o=new r.Schema({studentPic:String,parentPics:[String],fullName:{type:String,required:!0},sex:String,dob:Date,age:Number,formerGrade:String,registeredGrade:String,section:String,motherTongue:String,bloodType:String,weight:String,height:String,handUse:String,registrationDate:{type:Date,default:Date.now},quarter:{type:String,enum:["Quarter 1","Quarter 2","Quarter 3","Quarter 4"],required:!0},studentId:{type:String,unique:!0},status:{type:String,enum:["active","pending","graduated","transferred"],default:"active",required:!0},statusDate:{type:Date},statusReason:{type:String},statusNotes:{type:String},address:{city:String,subCity:String,tabia:String,ketena:String,block:String,nationality:String},seriousSickness:String,others:{shortSight:Boolean,longSight:Boolean,allergic:Boolean,autism:Boolean,languageProblem:Boolean},father:n,mother:n,guardian:i,transport:s,documents:a},{timestamps:!0});o.pre("save",async function(e){if(!this.studentId){const e=(new Date).getFullYear(),t=this.quarter?this.quarter.replace("Quarter ","Q"):"Q1",r=await this.constructor.findOne({studentId:new RegExp(`^STU${e}${t}`)}).sort({studentId:-1});let n=1;if(r&&r.studentId){n=parseInt(r.studentId.slice(-4))+1}this.studentId=`STU${e}${t}${n.toString().padStart(3,"0")}`}e()});const u=r.model("Student",o);module.exports=u},623:(e,exports,t)=>{const r=t(578),n=t(438);exports.getDashboard=async(e,t)=>{try{const e=await r.countDocuments(),i=await n.countDocuments();t.render("admin/dashboard",{title:"Admin Dashboard",studentCount:e,userCount:i})}catch(r){e.flash("error_msg","Error loading dashboard"),t.redirect("/")}},exports.getUsers=async(e,t)=>{try{const r=await n.find().sort({createdAt:-1});t.render("admin/users",{title:"Manage Users",users:r,userId:e.session.userId})}catch(r){e.flash("error_msg","Error fetching users"),t.redirect("/admin/dashboard")}},exports.createUser=async(e,t)=>{const{username:r,password:i,isAdmin:s}=e.body;try{if(await n.findOne({username:r}))return e.flash("error_msg","Username already exists"),t.redirect("/admin/users");const a=new n({username:r,password:i,isAdmin:"on"===s});await a.save(),e.flash("success_msg","User created successfully"),t.redirect("/admin/users")}catch(r){e.flash("error_msg","Error creating user"),t.redirect("/admin/users")}},exports.deleteUser=async(e,t)=>{try{if((await n.findById(e.params.id))._id.toString()===e.session.userId)return e.flash("error_msg","You cannot delete your own account"),t.redirect("/admin/users");await n.findByIdAndDelete(e.params.id),e.flash("success_msg","User deleted successfully"),t.redirect("/admin/users")}catch(r){e.flash("error_msg","Error deleting user"),t.redirect("/admin/users")}}},692:module=>{"use strict";module.exports=require("method-override")},818:module=>{"use strict";module.exports=require("dotenv")},852:(e,exports)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.isObject=function(e){return"[object Object]"===Object.prototype.toString.call(e)}},871:function(module,exports,e){var t,r,n;module=e.nmd(module),r=[],void 0===(n="function"==typeof(t=function(){"use strict";var t={},r=null;function n(t){if(module&&module.exports)try{return e(982).randomBytes(t)}catch(e){}try{var n;return(self.crypto||self.msCrypto).getRandomValues(n=new Uint32Array(t)),Array.prototype.slice.call(n)}catch(e){}if(!r)throw Error("Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative");return r(t)}try{n(1)}catch(e){}function i(e,t){for(var r=0,n=0,i=0,s=e.length;i<s;++i)e.charCodeAt(i)===t.charCodeAt(i)?++r:++n;return!(r<0)&&0===n}r=null,t.setRandomFallback=function(e){r=e},t.genSaltSync=function(e,t){if("number"!=typeof(e=e||p))throw Error("Illegal arguments: "+typeof e+", "+typeof t);e<4?e=4:e>31&&(e=31);var r=[];return r.push("$2a$"),e<10&&r.push("0"),r.push(e.toString()),r.push("$"),r.push(l(n(h),h)),r.join("")},t.genSalt=function(e,r,n){if("function"==typeof r&&(n=r,r=void 0),"function"==typeof e&&(n=e,e=void 0),void 0===e)e=p;else if("number"!=typeof e)throw Error("illegal arguments: "+typeof e);function i(r){s(function(){try{r(null,t.genSaltSync(e))}catch(e){r(e)}})}if(!n)return new Promise(function(e,t){i(function(r,n){r?t(r):e(n)})});if("function"!=typeof n)throw Error("Illegal callback: "+typeof n);i(n)},t.hashSync=function(e,r){if(void 0===r&&(r=p),"number"==typeof r&&(r=t.genSaltSync(r)),"string"!=typeof e||"string"!=typeof r)throw Error("Illegal arguments: "+typeof e+", "+typeof r);return I(e,r)},t.hash=function(e,r,n,i){function a(n){"string"==typeof e&&"number"==typeof r?t.genSalt(r,function(t,r){I(e,r,n,i)}):"string"==typeof e&&"string"==typeof r?I(e,r,n,i):s(n.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof r)))}if(!n)return new Promise(function(e,t){a(function(r,n){r?t(r):e(n)})});if("function"!=typeof n)throw Error("Illegal callback: "+typeof n);a(n)},t.compareSync=function(e,r){if("string"!=typeof e||"string"!=typeof r)throw Error("Illegal arguments: "+typeof e+", "+typeof r);return 60===r.length&&i(t.hashSync(e,r.substr(0,r.length-31)),r)},t.compare=function(e,r,n,a){function o(n){"string"==typeof e&&"string"==typeof r?60===r.length?t.hash(e,r.substr(0,29),function(e,t){e?n(e):n(null,i(t,r))},a):s(n.bind(this,null,!1)):s(n.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof r)))}if(!n)return new Promise(function(e,t){o(function(r,n){r?t(r):e(n)})});if("function"!=typeof n)throw Error("Illegal callback: "+typeof n);o(n)},t.getRounds=function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return parseInt(e.split("$")[2],10)},t.getSalt=function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);if(60!==e.length)throw Error("Illegal hash length: "+e.length+" != 60");return e.substring(0,29)};var s="undefined"!=typeof process&&process&&"function"==typeof process.nextTick?"function"==typeof setImmediate?setImmediate:process.nextTick:setTimeout;function a(e){var t=[],r=0;return f.encodeUTF16toUTF8(function(){return r>=e.length?null:e.charCodeAt(r++)},function(e){t.push(e)}),t}var o="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),u=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,54,55,56,57,58,59,60,61,62,63,-1,-1,-1,-1,-1,-1,-1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,-1,-1,-1,-1,-1,-1,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,-1,-1,-1,-1,-1],d=String.fromCharCode;function l(e,t){var r,n,i=0,s=[];if(t<=0||t>e.length)throw Error("Illegal len: "+t);for(;i<t;){if(r=255&e[i++],s.push(o[r>>2&63]),r=(3&r)<<4,i>=t){s.push(o[63&r]);break}if(r|=(n=255&e[i++])>>4&15,s.push(o[63&r]),r=(15&n)<<2,i>=t){s.push(o[63&r]);break}r|=(n=255&e[i++])>>6&3,s.push(o[63&r]),s.push(o[63&n])}return s.join("")}function c(e,t){var r,n,i,s,a,o=0,l=e.length,c=0,f=[];if(t<=0)throw Error("Illegal len: "+t);for(;o<l-1&&c<t&&(r=(a=e.charCodeAt(o++))<u.length?u[a]:-1,n=(a=e.charCodeAt(o++))<u.length?u[a]:-1,-1!=r&&-1!=n)&&(s=r<<2>>>0,s|=(48&n)>>4,f.push(d(s)),!(++c>=t||o>=l))&&-1!=(i=(a=e.charCodeAt(o++))<u.length?u[a]:-1)&&(s=(15&n)<<4>>>0,s|=(60&i)>>2,f.push(d(s)),!(++c>=t||o>=l));)s=(3&i)<<6>>>0,s|=(a=e.charCodeAt(o++))<u.length?u[a]:-1,f.push(d(s)),++c;var h=[];for(o=0;o<c;o++)h.push(f[o].charCodeAt(0));return h}var f=function(){var e={MAX_CODEPOINT:1114111,encodeUTF8:function(e,t){var r=null;for("number"==typeof e&&(r=e,e=function(){return null});null!==r||null!==(r=e());)r<128?t(127&r):r<2048?(t(r>>6&31|192),t(63&r|128)):r<65536?(t(r>>12&15|224),t(r>>6&63|128),t(63&r|128)):(t(r>>18&7|240),t(r>>12&63|128),t(r>>6&63|128),t(63&r|128)),r=null},decodeUTF8:function(e,t){for(var r,n,i,s,a=function(e){e=e.slice(0,e.indexOf(null));var t=Error(e.toString());throw t.name="TruncatedError",t.bytes=e,t};null!==(r=e());)if(128&r)if(192==(224&r))null===(n=e())&&a([r,n]),t((31&r)<<6|63&n);else if(224==(240&r))(null===(n=e())||null===(i=e()))&&a([r,n,i]),t((15&r)<<12|(63&n)<<6|63&i);else{if(240!=(248&r))throw RangeError("Illegal starting byte: "+r);(null===(n=e())||null===(i=e())||null===(s=e()))&&a([r,n,i,s]),t((7&r)<<18|(63&n)<<12|(63&i)<<6|63&s)}else t(r)},UTF16toUTF8:function(e,t){for(var r,n=null;null!==(r=null!==n?n:e());)r>=55296&&r<=57343&&null!==(n=e())&&n>=56320&&n<=57343?(t(1024*(r-55296)+n-56320+65536),n=null):t(r);null!==n&&t(n)},UTF8toUTF16:function(e,t){var r=null;for("number"==typeof e&&(r=e,e=function(){return null});null!==r||null!==(r=e());)r<=65535?t(r):(t(55296+((r-=65536)>>10)),t(r%1024+56320)),r=null},encodeUTF16toUTF8:function(t,r){e.UTF16toUTF8(t,function(t){e.encodeUTF8(t,r)})},decodeUTF8toUTF16:function(t,r){e.decodeUTF8(t,function(t){e.UTF8toUTF16(t,r)})},calculateCodePoint:function(e){return e<128?1:e<2048?2:e<65536?3:4},calculateUTF8:function(t){for(var r,n=0;null!==(r=t());)n+=e.calculateCodePoint(r);return n},calculateUTF16asUTF8:function(t){var r=0,n=0;return e.UTF16toUTF8(t,function(t){++r,n+=e.calculateCodePoint(t)}),[r,n]}};return e}();Date.now=Date.now||function(){return+new Date};var h=16,p=10,g=16,m=100,y=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],v=[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946,1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055,3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504,976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462],S=[1332899944,1700884034,1701343084,1684370003,1668446532,1869963892];function b(e,t,r,n){var i,s=e[t],a=e[t+1];return i=n[(s^=r[0])>>>24],i+=n[256|s>>16&255],i^=n[512|s>>8&255],i=n[(a^=(i+=n[768|255&s])^r[1])>>>24],i+=n[256|a>>16&255],i^=n[512|a>>8&255],i=n[(s^=(i+=n[768|255&a])^r[2])>>>24],i+=n[256|s>>16&255],i^=n[512|s>>8&255],i=n[(a^=(i+=n[768|255&s])^r[3])>>>24],i+=n[256|a>>16&255],i^=n[512|a>>8&255],i=n[(s^=(i+=n[768|255&a])^r[4])>>>24],i+=n[256|s>>16&255],i^=n[512|s>>8&255],i=n[(a^=(i+=n[768|255&s])^r[5])>>>24],i+=n[256|a>>16&255],i^=n[512|a>>8&255],i=n[(s^=(i+=n[768|255&a])^r[6])>>>24],i+=n[256|s>>16&255],i^=n[512|s>>8&255],i=n[(a^=(i+=n[768|255&s])^r[7])>>>24],i+=n[256|a>>16&255],i^=n[512|a>>8&255],i=n[(s^=(i+=n[768|255&a])^r[8])>>>24],i+=n[256|s>>16&255],i^=n[512|s>>8&255],i=n[(a^=(i+=n[768|255&s])^r[9])>>>24],i+=n[256|a>>16&255],i^=n[512|a>>8&255],i=n[(s^=(i+=n[768|255&a])^r[10])>>>24],i+=n[256|s>>16&255],i^=n[512|s>>8&255],i=n[(a^=(i+=n[768|255&s])^r[11])>>>24],i+=n[256|a>>16&255],i^=n[512|a>>8&255],i=n[(s^=(i+=n[768|255&a])^r[12])>>>24],i+=n[256|s>>16&255],i^=n[512|s>>8&255],i=n[(a^=(i+=n[768|255&s])^r[13])>>>24],i+=n[256|a>>16&255],i^=n[512|a>>8&255],i=n[(s^=(i+=n[768|255&a])^r[14])>>>24],i+=n[256|s>>16&255],i^=n[512|s>>8&255],i=n[(a^=(i+=n[768|255&s])^r[15])>>>24],i+=n[256|a>>16&255],i^=n[512|a>>8&255],s^=(i+=n[768|255&a])^r[16],e[t]=a^r[g+1],e[t+1]=s,e}function _(e,t){for(var r=0,n=0;r<4;++r)n=n<<8|255&e[t],t=(t+1)%e.length;return{key:n,offp:t}}function w(e,t,r){for(var n,i=0,s=[0,0],a=t.length,o=r.length,u=0;u<a;u++)i=(n=_(e,i)).offp,t[u]=t[u]^n.key;for(u=0;u<a;u+=2)s=b(s,0,t,r),t[u]=s[0],t[u+1]=s[1];for(u=0;u<o;u+=2)s=b(s,0,t,r),r[u]=s[0],r[u+1]=s[1]}function A(e,t,r,n){for(var i,s=0,a=[0,0],o=r.length,u=n.length,d=0;d<o;d++)s=(i=_(t,s)).offp,r[d]=r[d]^i.key;for(s=0,d=0;d<o;d+=2)s=(i=_(e,s)).offp,a[0]^=i.key,s=(i=_(e,s)).offp,a[1]^=i.key,a=b(a,0,r,n),r[d]=a[0],r[d+1]=a[1];for(d=0;d<u;d+=2)s=(i=_(e,s)).offp,a[0]^=i.key,s=(i=_(e,s)).offp,a[1]^=i.key,a=b(a,0,r,n),n[d]=a[0],n[d+1]=a[1]}function E(e,t,r,n,i){var a,o=S.slice(),u=o.length;if(r<4||r>31){if(a=Error("Illegal number of rounds (4-31): "+r),n)return void s(n.bind(this,a));throw a}if(t.length!==h){if(a=Error("Illegal salt length: "+t.length+" != "+h),n)return void s(n.bind(this,a));throw a}r=1<<r>>>0;var d,l,c,f=0;function p(){if(i&&i(f/r),!(f<r)){for(f=0;f<64;f++)for(c=0;c<u>>1;c++)b(o,c<<1,d,l);var a=[];for(f=0;f<u;f++)a.push((o[f]>>24&255)>>>0),a.push((o[f]>>16&255)>>>0),a.push((o[f]>>8&255)>>>0),a.push((255&o[f])>>>0);return n?void n(null,a):a}for(var h=Date.now();f<r&&(f+=1,w(e,d,l),w(t,d,l),!(Date.now()-h>m)););n&&s(p)}if(Int32Array?(d=new Int32Array(y),l=new Int32Array(v)):(d=y.slice(),l=v.slice()),A(t,e,d,l),void 0!==n)p();else for(var g;;)if(void 0!==(g=p()))return g||[]}function I(e,t,r,n){var i,o,u;if("string"!=typeof e||"string"!=typeof t){if(i=Error("Invalid string / salt: Not a string"),r)return void s(r.bind(this,i));throw i}if("$"!==t.charAt(0)||"2"!==t.charAt(1)){if(i=Error("Invalid salt version: "+t.substring(0,2)),r)return void s(r.bind(this,i));throw i}if("$"===t.charAt(2))o=String.fromCharCode(0),u=3;else{if("a"!==(o=t.charAt(2))&&"b"!==o&&"y"!==o||"$"!==t.charAt(3)){if(i=Error("Invalid salt revision: "+t.substring(2,4)),r)return void s(r.bind(this,i));throw i}u=4}if(t.charAt(u+2)>"$"){if(i=Error("Missing salt rounds"),r)return void s(r.bind(this,i));throw i}var d=10*parseInt(t.substring(u,u+1),10)+parseInt(t.substring(u+1,u+2),10),f=t.substring(u+3,u+25),p=a(e+=o>="a"?"\0":""),g=c(f,h);function m(e){var t=[];return t.push("$2"),o>="a"&&t.push(o),t.push("$"),d<10&&t.push("0"),t.push(d.toString()),t.push("$"),t.push(l(g,g.length)),t.push(l(e,4*S.length-1)),t.join("")}if(void 0===r)return m(E(p,g,d));E(p,g,d,function(e,t){e?r(e,null):r(null,m(t))},n)}return t.encodeBase64=l,t.decodeBase64=c,t})?t.apply(exports,r):t)||(module.exports=n)},896:module=>{"use strict";module.exports=require("fs")},897:(e,exports)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t=["\n","\r\n"],r=function(){function e(e,r){void 0===r&&(r="\n"),this.fieldStringifier=e,this.recordDelimiter=r,function(e){if(-1===t.indexOf(e))throw new Error("Invalid record delimiter `"+e+"` is specified")}(r)}return e.prototype.getHeaderString=function(){var e=this.getHeaderRecord();return e?this.joinRecords([this.getCsvLine(e)]):null},e.prototype.stringifyRecords=function(e){var t=this,r=Array.from(e,function(e){return t.getCsvLine(t.getRecordAsArray(e))});return this.joinRecords(r)},e.prototype.getCsvLine=function(e){var t=this;return e.map(function(e){return t.fieldStringifier.stringify(e)}).join(this.fieldStringifier.fieldDelimiter)},e.prototype.joinRecords=function(e){return e.join(this.recordDelimiter)+this.recordDelimiter},e}();exports.CsvStringifier=r},904:module=>{module.exports={systemName:process.env.SYSTEM_NAME||"SHEGE SRS",systemFullName:process.env.SYSTEM_FULL_NAME||"Student Registration System",systemVersion:process.env.SYSTEM_VERSION||"1.0.0",schoolName:process.env.SCHOOL_NAME||"SHEGE SRS",schoolAddress:process.env.SCHOOL_ADDRESS||"",schoolPhone:process.env.SCHOOL_PHONE||"",schoolEmail:process.env.SCHOOL_EMAIL||"",schoolWebsite:process.env.SCHOOL_WEBSITE||"",defaultLanguage:process.env.DEFAULT_LANGUAGE||"en",timezone:process.env.TIMEZONE||"UTC",dateFormat:process.env.DATE_FORMAT||"MM/DD/YYYY",primaryColor:process.env.PRIMARY_COLOR||"#ff6b35",secondaryColor:process.env.SECONDARY_COLOR||"#2c3e50",logoPath:process.env.LOGO_PATH||"/images/logo.png",enableRegistration:"false"!==process.env.ENABLE_REGISTRATION,enablePrintFeatures:"false"!==process.env.ENABLE_PRINT_FEATURES,enableExportFeatures:"false"!==process.env.ENABLE_EXPORT_FEATURES,maxFileSize:process.env.MAX_FILE_SIZE||"5MB",allowedImageTypes:["jpg","jpeg","png","gif"],allowedDocumentTypes:["pdf","doc","docx"],defaultPageSize:parseInt(process.env.DEFAULT_PAGE_SIZE)||20,maxPageSize:parseInt(process.env.MAX_PAGE_SIZE)||100,sessionTimeout:parseInt(process.env.SESSION_TIMEOUT)||36e5,maxLoginAttempts:parseInt(process.env.MAX_LOGIN_ATTEMPTS)||5,isDevelopment:!1,isProduction:!0,debugMode:"true"===process.env.DEBUG_MODE,dbName:process.env.DB_NAME||"student-registration",emailEnabled:"true"===process.env.EMAIL_ENABLED,smtpHost:process.env.SMTP_HOST||"",smtpPort:parseInt(process.env.SMTP_PORT)||587,smtpUser:process.env.SMTP_USER||"",smtpPass:process.env.SMTP_PASS||"",backupEnabled:"true"===process.env.BACKUP_ENABLED,backupInterval:process.env.BACKUP_INTERVAL||"24h",backupPath:process.env.BACKUP_PATH||"./backups",apiVersion:process.env.API_VERSION||"v1",apiRateLimit:parseInt(process.env.API_RATE_LIMIT)||100,logLevel:process.env.LOG_LEVEL||"info",logFile:process.env.LOG_FILE||"./logs/app.log",cacheEnabled:"false"!==process.env.CACHE_ENABLED,cacheTTL:parseInt(process.env.CACHE_TTL)||300,defaultReportFormat:process.env.DEFAULT_REPORT_FORMAT||"pdf",reportsPath:process.env.REPORTS_PATH||"./reports",studentIdPrefix:process.env.STUDENT_ID_PREFIX||"STU",studentIdLength:parseInt(process.env.STUDENT_ID_LENGTH)||8,currentAcademicYear:process.env.CURRENT_ACADEMIC_YEAR||(new Date).getFullYear().toString(),academicYearStart:process.env.ACADEMIC_YEAR_START||"09-01",academicYearEnd:process.env.ACADEMIC_YEAR_END||"08-31",quarters:["Quarter 1","Quarter 2","Quarter 3","Quarter 4"],gradeLevels:["Pre-K","Kindergarten","Grade 1","Grade 2","Grade 3","Grade 4","Grade 5","Grade 6","Grade 7","Grade 8","Grade 9","Grade 10","Grade 11","Grade 12"],defaultSections:["A","B","C","D","E"],studentStatuses:["active","pending","graduated","transferred"],medicalConditions:["shortSight","longSight","allergic","autism","languageProblem"],bloodTypes:["A+","A-","B+","B-","AB+","AB-","O+","O-"],guardianRelationships:["Father","Mother","Grandfather","Grandmother","Uncle","Aunt","Brother","Sister","Legal Guardian","Other"]}},925:module=>{"use strict";module.exports=require("connect-flash")},928:module=>{"use strict";module.exports=require("path")},930:function(e,exports){"use strict";var t=this&&this.__spreadArrays||function(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;var n=Array(e),i=0;for(t=0;t<r;t++)for(var s=arguments[t],a=0,o=s.length;a<o;a++,i++)n[i]=s[a];return n};Object.defineProperty(exports,"__esModule",{value:!0}),exports.promisify=function(e){return function(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return new Promise(function(n,i){e.apply(null,t(r,[function(e,t){e?i(e):n(t)}]))})}}},944:function(e,exports,t){"use strict";var r=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function a(e){try{u(n.next(e))}catch(e){s(e)}}function o(e){try{u(n.throw(e))}catch(e){s(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(a,o)}u((n=n.apply(e,t||[])).next())})},n=this&&this.__generator||function(e,t){var r,n,i,s,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return s={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function o(s){return function(o){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){a.label=s[1];break}if(6===s[0]&&a.label<i[1]){a.label=i[1],i=s;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(s);break}i[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,o])}}};Object.defineProperty(exports,"__esModule",{value:!0});var i=t(408),s=function(){function e(e,t,r,n){void 0===n&&(n=false),this.csvStringifier=e,this.append=n,this.fileWriter=new i.FileWriter(t,this.append,r)}return e.prototype.writeRecords=function(e){return r(this,void 0,void 0,function(){var t,r;return n(this,function(n){switch(n.label){case 0:return t=this.csvStringifier.stringifyRecords(e),r=this.headerString+t,[4,this.fileWriter.write(r)];case 1:return n.sent(),this.append=!0,[2]}})})},Object.defineProperty(e.prototype,"headerString",{get:function(){return!this.append&&this.csvStringifier.getHeaderString()||""},enumerable:!0,configurable:!0}),e}();exports.CsvWriter=s},977:module=>{"use strict";module.exports=require("express-session")},982:module=>{"use strict";module.exports=require("crypto")},988:(module,e,t)=>{module.exports=t(871)},997:(module,e,t)=>{const r=t(438);module.exports={isAuthenticated:(e,t,r)=>{if(e.session&&e.session.userId)return r();e.flash("error_msg","Please log in to access this page"),t.redirect("/login")},isAdmin:async(e,t,n)=>{try{const i=await r.findById(e.session.userId);if(i&&i.isAdmin)return n();e.flash("error_msg","Access denied. Admin privileges required."),t.redirect("/login")}catch(r){e.flash("error_msg","An error occurred"),t.redirect("/login")}}}}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var module=t[n]={id:n,loaded:!1,exports:{}};return e[n].call(module.exports,module,module.exports,r),module.loaded=!0,module.exports}r.nmd=module=>(module.paths=[],module.children||(module.children=[]),module),r(818).config();const n=r(252),i=r(928),s=r(977),a=r(925),o=r(692),u=r(47),d=r(904),l=n();u(),l.use(n.urlencoded({extended:!0})),l.use(n.json()),l.use(n.static(i.join(__dirname,"public"))),l.use(o("_method")),l.use(s({secret:process.env.SESSION_SECRET||"your_strong_secret_key_here",resave:!1,saveUninitialized:!1,cookie:{httpOnly:!0,secure:!0,maxAge:864e5}})),l.use(a()),l.use((e,t,r)=>{t.locals.username=e.session.username,t.locals.isAdmin=e.session.isAdmin,t.locals.success_msg=e.flash("success_msg"),t.locals.error_msg=e.flash("error_msg"),t.locals.systemConfig=d,t.locals.systemName=d.systemName,t.locals.systemFullName=d.systemFullName,t.locals.schoolName=d.schoolName,r()}),l.set("view engine","ejs"),l.set("views",i.join(__dirname,"views")),l.use((e,t,r)=>{t.locals.success_msg=e.flash("success_msg"),t.locals.error_msg=e.flash("error_msg"),r()});const c=r(256);l.use("/",c);try{const e=r(320);l.use("/admin",e)}catch(e){}l.use((e,t)=>{t.status(404).render("error",{title:"Page Not Found",message:"The page you are looking for does not exist."})}),l.use((e,t,r,n)=>{r.status(500).render("error",{title:"Server Error",message:"Something went wrong on our end. Please try again later."})});const f=process.env.PORT||3e3;l.listen(f,()=>{})})();