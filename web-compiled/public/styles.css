* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    line-height: 1.6;
}

/* Photo Upload Component Styles */
.photo-upload-container {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 1rem auto;
    border: 3px dashed #e1e8ed;
    border-radius: 20px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9ff, #ffffff);
    transition: all 0.3s ease;
    cursor: pointer;
}

.photo-upload-container:hover {
    border-color: #667eea;
    background: linear-gradient(135deg, #f0f2ff, #f8f9ff);
    transform: scale(1.02);
}

.photo-upload-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 10;
}

.photo-upload-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #667eea;
    z-index: 1;
    font-weight: 600;
}

.photo-upload-placeholder i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.photo-upload-preview {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 5;
}

/* Checkbox Styles */
fieldset {
    border: 2px solid #e1e8ed;
    border-radius: 15px;
    padding: 1.5rem;
    margin: 1rem 0;
    background: rgba(255, 255, 255, 0.5);
}

fieldset legend {
    font-weight: 700;
    color: #2c3e50;
    padding: 0 1rem;
    background: white;
    border-radius: 8px;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.checkbox-group label:hover {
    background: #f8f9ff;
    border-color: #667eea;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #e1e8ed;
}

.nav-buttons {
    display: flex;
    gap: 1rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #2c3e50;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 1rem;
        padding: 1.5rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    nav {
        flex-wrap: wrap;
        justify-content: center;
    }

    .tabs {
        flex-direction: column;
    }

    .filter-form {
        flex-direction: column;
        align-items: stretch;
    }

    .actions {
        flex-direction: column;
    }

    table {
        font-size: 0.9rem;
    }

    th, td {
        padding: 0.5rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Dashboard Specific Styles */
.stat-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(102, 126, 234, 0.2);
    position: relative;
    overflow: hidden;
}

.stat-card .stat-icon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 2rem;
    opacity: 0.3;
    color: #667eea;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #2c3e50;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

/* Quarter Statistics */
.quarter-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.quarter-card {
    text-align: center;
    padding: 1rem;
}

.quarter-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.quarter-label {
    font-weight: 600;
    color: #666;
    margin-bottom: 1rem;
}

.quarter-bar {
    height: 8px;
    background: #e1e8ed;
    border-radius: 4px;
    overflow: hidden;
}

.quarter-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Recent Registrations */
.recent-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.recent-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    border: 1px solid #e1e8ed;
}

.recent-info {
    flex: 1;
}

.recent-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.recent-details {
    font-size: 0.9rem;
    color: #666;
}

.recent-quarter {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 10px;
    font-size: 0.8rem;
    margin-left: 0.5rem;
}

.recent-date {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

/* Grade Distribution */
.grade-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.grade-item {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    border: 1px solid #e1e8ed;
}

.grade-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.grade-name {
    font-weight: 600;
    color: #2c3e50;
}

.grade-count {
    font-size: 0.9rem;
    color: #666;
}

.grade-bar {
    height: 6px;
    background: #e1e8ed;
    border-radius: 3px;
    overflow: hidden;
}

.grade-fill {
    height: 100%;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.action-card {
    display: block;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 15px;
    text-decoration: none;
    color: inherit;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    text-align: center;
}

.action-card:hover {
    transform: translateY(-5px);
    border-color: #667eea;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
    background: white;
}

.action-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.action-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.action-desc {
    color: #666;
    font-size: 0.9rem;
}

.hidden {
    display: none !important;
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

nav {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

nav a {
    text-decoration: none;
    color: #2c3e50;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

nav a:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
}

.user-info {
    color: #2c3e50;
    font-weight: 500;
}

h1 {
    margin: 0;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    font-weight: 700;
}

/* Container Styles */
.container {
    max-width: 1200px;
    margin: 2rem auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

main {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

/* Form Styles */
form {
    background: transparent;
    padding: 0;
    border-radius: 0;
    box-shadow: none;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

form input[type="text"],
form input[type="email"],
form input[type="number"],
form input[type="date"],
form input[type="password"],
form select,
form textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    font-size: 1rem;
    background: #fff;
    transition: all 0.3s ease;
    font-family: inherit;
}

form input:focus,
form select:focus,
form textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

form input[type="file"] {
    background: #fff;
    border: 2px dashed #e1e8ed;
    padding: 1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

form input[type="file"]:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

/* Button Styles */
button,
form input[type="submit"],
.btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

button:hover,
form input[type="submit"]:hover,
.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

/* Tab Styles */
.tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    padding: 0.5rem;
    margin-bottom: 2rem;
    gap: 0.5rem;
}

.tab {
    flex: 1;
    padding: 1rem 1.5rem;
    cursor: pointer;
    border: none;
    background: transparent;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    border-radius: 12px;
    transition: all 0.3s ease;
    text-align: center;
}

.tab:hover {
    background: rgba(102, 126, 234, 0.1);
}

.tab.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Table Styles */
.student-list {
    margin-top: 2rem;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1.5rem;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e1e8ed;
}

th {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

td img {
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e1e8ed;
}

/* Action Links */
.actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.actions a {
    padding: 0.4rem 0.8rem;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.actions a:nth-child(1) { /* View */
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.actions a:nth-child(2) { /* Edit */
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.actions a:nth-child(3) { /* ID Card */
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.actions a:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Form Section Styles */
.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-section h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 700;
}

/* Flash Messages */
.flash-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.flash-error {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

/* Filter Form */
.filter-form {
    background: rgba(255, 255, 255, 0.7);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-form label {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.filter-form input,
.filter-form select {
    min-width: 150px;
}

.filter-form button,
.filter-form .btn {
    height: fit-content;
    margin-top: auto;
}

/* Photo Upload Component Styles */
.photo-upload-container {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 1rem auto;
    border: 3px dashed #e1e8ed;
    border-radius: 20px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9ff, #ffffff);
    transition: all 0.3s ease;
    cursor: pointer;
}

.photo-upload-container:hover {
    border-color: #667eea;
    background: linear-gradient(135deg, #f0f2ff, #f8f9ff);
    transform: scale(1.02);
}

.photo-upload-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 10;
}

.photo-upload-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #667eea;
    z-index: 1;
    font-weight: 600;
}

.photo-upload-placeholder i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.photo-upload-preview {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 5;
}

/* Checkbox Styles */
fieldset {
    border: 2px solid #e1e8ed;
    border-radius: 15px;
    padding: 1.5rem;
    margin: 1rem 0;
    background: rgba(255, 255, 255, 0.5);
}

fieldset legend {
    font-weight: 700;
    color: #2c3e50;
    padding: 0 1rem;
    background: white;
    border-radius: 8px;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.checkbox-group label:hover {
    background: #f8f9ff;
    border-color: #667eea;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #e1e8ed;
}

.nav-buttons {
    display: flex;
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 1rem;
        padding: 1.5rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    nav {
        flex-wrap: wrap;
        justify-content: center;
    }

    .tabs {
        flex-direction: column;
    }

    .filter-form {
        flex-direction: column;
        align-items: stretch;
    }

    .actions {
        flex-direction: column;
    }

    table {
        font-size: 0.9rem;
    }

    th, td {
        padding: 0.5rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Card Styles */
.card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #2c3e50;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}