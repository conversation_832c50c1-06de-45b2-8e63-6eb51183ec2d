#!/usr/bin/env node

const { spawn } = require('child_process');
const { exec } = require('child_process');

console.log('🚀 SHEGE SRS Web Launcher');
console.log('========================');

// Start the server
const server = spawn('node', ['app.bundle.js'], {
    stdio: 'pipe'
});

let serverStarted = false;

server.stdout.on('data', (data) => {
    const output = data.toString();
    console.log(output);
    
    if (output.includes('Server running on') && !serverStarted) {
        serverStarted = true;
        console.log('');
        console.log('🌐 Opening web browser...');
        
        // Open browser after a short delay
        setTimeout(() => {
            const url = 'http://localhost:3000';
            
            // Cross-platform browser opening
            const platform = process.platform;
            let command;
            
            if (platform === 'darwin') {
                command = `open ${url}`;
            } else if (platform === 'win32') {
                command = `start ${url}`;
            } else {
                command = `xdg-open ${url}`;
            }
            
            exec(command, (error) => {
                if (error) {
                    console.log(`Please open your browser and go to: ${url}`);
                } else {
                    console.log(`✅ Browser opened to: ${url}`);
                }
            });
        }, 2000);
    }
});

server.stderr.on('data', (data) => {
    console.error(data.toString());
});

server.on('close', (code) => {
    console.log(`Server process exited with code ${code}`);
});

// Handle Ctrl+C
process.on('SIGINT', () => {
    console.log('\n🛑 Stopping server...');
    server.kill();
    process.exit();
});

console.log('Starting server...');
console.log('Press Ctrl+C to stop');
