{"name": "method-override", "description": "Override HTTP verbs", "version": "3.0.0", "license": "MIT", "repository": "expressjs/method-override", "dependencies": {"debug": "3.1.0", "methods": "~1.1.2", "parseurl": "~1.3.2", "vary": "~1.1.2"}, "devDependencies": {"eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.13.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.8.0", "eslint-plugin-standard": "3.1.0", "istanbul": "0.4.5", "mocha": "3.5.3", "supertest": "1.2.0"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.10"}}