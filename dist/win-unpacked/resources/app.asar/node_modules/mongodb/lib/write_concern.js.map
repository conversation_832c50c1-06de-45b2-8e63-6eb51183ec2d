{"version": 3, "file": "write_concern.js", "sourceRoot": "", "sources": ["../src/write_concern.ts"], "names": [], "mappings": ";;;AA2KA,4DAaC;AAvLD,8DAAiE;AACjE,mCAAiD;AAuCpC,QAAA,kBAAkB,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;AAY7E;;;;;;GAMG;AACH,MAAa,YAAY;IA4BvB;;;;;;OAMG;IACH,YAAY,CAAK,EAAE,UAAmB,EAAE,OAAiB,EAAE,KAAmB;QAC5E,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,CAAC;QACH,CAAC;QACD,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;QAC/C,CAAC;QACD,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC;QAClC,CAAC;QACD,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,OAAiB,EAAE,YAA0B;QACxD,MAAM,EAAE,GAA+B,EAAE,CAAC;QAC1C,yEAAyE;QACzE,IAAI,YAAY,CAAC,CAAC,IAAI,IAAI;YAAE,EAAE,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;QAClD,IAAI,YAAY,CAAC,UAAU,IAAI,IAAI;YAAE,EAAE,CAAC,QAAQ,GAAG,YAAY,CAAC,UAAU,CAAC;QAC3E,IAAI,YAAY,CAAC,OAAO,IAAI,IAAI;YAAE,EAAE,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;QACxD,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC;QAC1B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,wDAAwD;IACxD,MAAM,CAAC,WAAW,CAChB,OAAgD,EAChD,OAA4C;QAE5C,IAAI,OAAO,IAAI,IAAI;YAAE,OAAO,SAAS,CAAC;QACtC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,IAAqD,CAAC;QAC1D,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC/D,IAAI,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;QACxB,CAAC;aAAM,IAAI,OAAO,YAAY,YAAY,EAAE,CAAC;YAC3C,IAAI,GAAG,OAAO,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC;QAC9B,CAAC;QACD,MAAM,UAAU,GACd,OAAO,YAAY,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;QAEnE,MAAM,EACJ,CAAC,GAAG,SAAS,EACb,QAAQ,GAAG,SAAS,EACpB,CAAC,GAAG,SAAS,EACb,KAAK,GAAG,SAAS,EACjB,OAAO,GAAG,SAAS,EACnB,UAAU,GAAG,SAAS,EACvB,GAAG;YACF,GAAG,UAAU;YACb,GAAG,IAAI;SACR,CAAC;QACF,IACE,CAAC,IAAI,IAAI;YACT,QAAQ,IAAI,IAAI;YAChB,UAAU,IAAI,IAAI;YAClB,CAAC,IAAI,IAAI;YACT,OAAO,IAAI,IAAI;YACf,KAAK,IAAI,IAAI,EACb,CAAC;YACD,OAAO,IAAI,YAAY,CAAC,CAAC,EAAE,QAAQ,IAAI,UAAU,EAAE,CAAC,IAAI,OAAO,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AA5GD,oCA4GC;AAED,2DAA2D;AAC3D,SAAgB,wBAAwB,CAAC,QAAiB;IACxD,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACrD,MAAM,iBAAiB,GACrB,2BAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,mBAAmB,CAAC;YAC/D,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACrB,CAAC,CAAC,CAAC,2BAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,mBAAmB,IAAI,QAAQ;gBAChE,CAAC,CAAC,QAAQ;gBACV,CAAC,CAAC,IAAI,CAAC;QAEb,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;YAC9B,MAAM,IAAI,8BAAsB,CAAC,iBAAwB,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;AACH,CAAC"}