// Enhanced error handling for preload script
try {
    const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

    // Expose protected methods that allow the renderer process to use
    // the ipcRenderer without exposing the entire object
    contextBridge.exposeInMainWorld('electronAPI', {
    // System information
    platform: process.platform,
    versions: process.versions,
    
    // Navigation helpers
    openExternal: (url) => ipcRenderer.invoke('open-external', url),
    
    // App information
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),
    
    // Window controls
    minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
    maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
    closeWindow: () => ipcRenderer.invoke('close-window'),
    
    // File operations
    showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
    showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
    
    // Notifications
    showNotification: (title, body) => ipcRenderer.invoke('show-notification', { title, body }),
    
    // Print operations
    printPage: () => ipcRenderer.invoke('print-page'),
    
    // Development helpers
    isDev: process.env.NODE_ENV === 'development',
    
    // Event listeners
    onWindowEvent: (callback) => ipcRenderer.on('window-event', callback),
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// Add desktop-specific styling when the page loads
window.addEventListener('DOMContentLoaded', () => {
    // Add desktop class to body for desktop-specific styling
    document.body.classList.add('desktop-app');
    
    // Add platform-specific class
    document.body.classList.add(`platform-${process.platform}`);
    
    // Disable text selection for better desktop feel (optional)
    document.body.style.userSelect = 'none';
    
    // Allow text selection in input fields and content areas
    const selectableElements = document.querySelectorAll('input, textarea, [contenteditable], .selectable');
    selectableElements.forEach(element => {
        element.style.userSelect = 'text';
    });
    
    // Add custom title bar controls if needed
    if (process.platform === 'win32') {
        addWindowsControls();
    }
});

function addWindowsControls() {
    // Add Windows-style window controls if custom title bar is used
    const titleBar = document.querySelector('.title-bar');
    if (titleBar) {
        const controls = document.createElement('div');
        controls.className = 'window-controls';
        controls.innerHTML = `
            <button class="window-control minimize" onclick="window.electronAPI.minimizeWindow()">
                <i class="fas fa-window-minimize"></i>
            </button>
            <button class="window-control maximize" onclick="window.electronAPI.maximizeWindow()">
                <i class="fas fa-window-maximize"></i>
            </button>
            <button class="window-control close" onclick="window.electronAPI.closeWindow()">
                <i class="fas fa-times"></i>
            </button>
        `;
        titleBar.appendChild(controls);
    }
}

// Handle keyboard shortcuts
window.addEventListener('keydown', (event) => {
    // Ctrl+R or F5 - Reload
    if ((event.ctrlKey && event.key === 'r') || event.key === 'F5') {
        event.preventDefault();
        window.location.reload();
    }
    
    // Ctrl+Shift+I - Toggle DevTools (in development)
    if (event.ctrlKey && event.shiftKey && event.key === 'I' && window.electronAPI.isDev) {
        event.preventDefault();
        // DevTools toggle is handled by the main process
    }
    
    // F11 - Toggle fullscreen
    if (event.key === 'F11') {
        event.preventDefault();
        // Fullscreen toggle is handled by the main process
    }
});

// Add print functionality
window.addEventListener('beforeprint', () => {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', () => {
    document.body.classList.remove('printing');
});

// Enhanced error handling for desktop app
window.addEventListener('error', (event) => {
    console.error('Desktop App Error:', event.error);
    
    // Show user-friendly error message
    if (window.electronAPI && window.electronAPI.showNotification) {
        window.electronAPI.showNotification(
            'Application Error',
            'An error occurred. Please try refreshing the page.'
        );
    }
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled Promise Rejection:', event.reason);
    
    if (window.electronAPI && window.electronAPI.showNotification) {
        window.electronAPI.showNotification(
            'Application Warning',
            'A background operation failed. The application should continue to work normally.'
        );
    }
});

// Add desktop-specific utilities
window.desktopUtils = {
    // Check if running in desktop app
    isDesktop: () => typeof window.electronAPI !== 'undefined',
    
    // Get platform information
    getPlatform: () => window.electronAPI ? window.electronAPI.platform : 'web',
    
    // Show desktop notification
    notify: (title, message) => {
        if (window.electronAPI && window.electronAPI.showNotification) {
            window.electronAPI.showNotification(title, message);
        } else if ('Notification' in window) {
            new Notification(title, { body: message });
        }
    },
    
    // Print current page
    print: () => {
        if (window.electronAPI && window.electronAPI.printPage) {
            window.electronAPI.printPage();
        } else {
            window.print();
        }
    },
    
    // Open external link
    openExternal: (url) => {
        if (window.electronAPI && window.electronAPI.openExternal) {
            window.electronAPI.openExternal(url);
        } else {
            window.open(url, '_blank');
        }
    }
};

} catch (error) {
    console.error('Error in preload script:', error);

    // Fallback API for when contextBridge fails
    window.electronAPI = {
        platform: 'unknown',
        versions: {},
        isDev: false,
        isDesktop: () => true,
        getPlatform: () => 'unknown',
        notify: (title, message) => {
            if ('Notification' in window) {
                new Notification(title, { body: message });
            }
        },
        print: () => window.print(),
        openExternal: (url) => window.open(url, '_blank')
    };

    window.desktopUtils = window.electronAPI;
}
