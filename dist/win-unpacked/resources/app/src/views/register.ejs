<!DOCTYPE html>
<html>
<head>
    <title>Register Student - <%= systemName || 'SHEGE SRS' %></title>
    <link rel="stylesheet" href="/styles-enhanced.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        function showTab(tabIndex) {
            var tabs = document.querySelectorAll('.tab');
            var contents = document.querySelectorAll('.tab-content');
            var progressSteps = document.querySelectorAll('.tab-progress-step');

            tabs.forEach((tab, i) => {
                tab.classList.toggle('active', i === tabIndex);
                contents[i].classList.toggle('active', i === tabIndex);
            });

            // Update progress indicator
            progressSteps.forEach((step, i) => {
                step.classList.toggle('active', i === tabIndex);
                step.classList.toggle('completed', i < tabIndex);
            });

            updateNavButtons(tabIndex, tabs.length);
        }
        function updateNavButtons(tabIndex, totalTabs) {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const submitBtn = document.getElementById('submitBtn');

            prevBtn.style.display = tabIndex === 0 ? 'none' : 'inline-flex';

            if (tabIndex === totalTabs - 1) {
                nextBtn.style.display = 'none';
                submitBtn.style.display = 'inline-flex';
            } else {
                nextBtn.style.display = 'inline-flex';
                submitBtn.style.display = 'none';
            }
        }
        document.addEventListener('DOMContentLoaded', function() {
            let currentTab = 0;
            showTab(currentTab);
            document.querySelectorAll('.tab').forEach((tab, i) => {
                tab.addEventListener('click', function() { currentTab = i; showTab(i); });
            });
            document.getElementById('nextBtn').addEventListener('click', function(e) {
                e.preventDefault();
                let tabs = document.querySelectorAll('.tab');
                if (currentTab < tabs.length - 1) {
                    currentTab++;
                    showTab(currentTab);
                }
            });
            document.getElementById('prevBtn').addEventListener('click', function(e) {
                e.preventDefault();
                if (currentTab > 0) {
                    currentTab--;
                    showTab(currentTab);
                }
            });
        });
    </script>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">
                <h1><i class="fas fa-graduation-cap"></i> SHEGE SRS</h1>
                <p class="logo-subtitle">Student Registration System</p>
            </div>
            <nav>
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/register"><i class="fas fa-user-plus"></i> Register Student</a>
                <a href="/students"><i class="fas fa-users"></i> View Students</a>
                <a href="/login"><i class="fas fa-sign-in-alt"></i> Login</a>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <h1><i class="fas fa-user-plus"></i> Student Registration</h1>
            <form action="/register" method="POST" enctype="multipart/form-data">
                <div class="tabs">
                    <button type="button" class="tab active"><i class="fas fa-user"></i> Student Info</button>
                    <button type="button" class="tab"><i class="fas fa-users"></i> Parent Info</button>
                    <button type="button" class="tab"><i class="fas fa-user-shield"></i> Guardian</button>
                    <button type="button" class="tab"><i class="fas fa-bus"></i> Transport</button>
                    <button type="button" class="tab"><i class="fas fa-file-alt"></i> Documents</button>
                </div>

                <!-- Progress Indicator -->
                <div class="tab-progress">
                    <div class="tab-progress-step active"></div>
                    <div class="tab-progress-step"></div>
                    <div class="tab-progress-step"></div>
                    <div class="tab-progress-step"></div>
                    <div class="tab-progress-step"></div>
                </div>
                <div class="tab-content active">
                    <div class="form-section">
                        <h2><i class="fas fa-clipboard-list"></i> Registration Information</h2>
                        <div class="form-group">
                            <label><i class="fas fa-calendar-alt"></i> Registration Date *</label>
                            <input type="date" name="registrationDate" required value="<%= new Date().toISOString().split('T')[0] %>">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-calendar-check"></i> Quarter/Term *</label>
                            <select name="quarter" required>
                                <option value="">Select Quarter/Term</option>
                                <option value="Quarter 1">Quarter 1</option>
                                <option value="Quarter 2">Quarter 2</option>
                                <option value="Quarter 3">Quarter 3</option>
                                <option value="Quarter 4">Quarter 4</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2><i class="fas fa-user"></i> Basic Information</h2>
                        <div class="form-group">
                            <label><i class="fas fa-camera"></i> Student Photo</label>
                            <div class="simple-photo-upload">
                                <input type="file" id="studentPic" name="studentPic" accept="image/*" onchange="previewImage(this, 'studentPreview')" style="display: none;">
                                <button type="button" class="photo-upload-btn" onclick="document.getElementById('studentPic').click()">
                                    <i class="fas fa-camera"></i>
                                    <span>Choose Student Photo</span>
                                </button>
                                <div class="photo-preview-container">
                                    <img id="studentPreview" class="photo-preview" style="display: none;">
                                    <div id="studentPlaceholder" class="photo-placeholder">
                                        <i class="fas fa-user"></i>
                                        <span>No photo selected</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-user"></i> Full Name *</label>
                            <input type="text" name="fullName" required placeholder="Enter full name">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-venus-mars"></i> Sex</label>
                            <select name="sex" required>
                                <option value="">Select Sex</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-calendar"></i> Date of Birth</label>
                            <input type="date" name="dob">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-birthday-cake"></i> Age</label>
                            <input type="number" name="age" min="1" max="100" placeholder="Enter age">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-layer-group"></i> Former Grade</label>
                            <select name="formerGrade" class="form-select">
                                <option value="">Select Former Grade</option>
                                <option value="Pre-K">Pre-K</option>
                                <option value="Kindergarten">Kindergarten</option>
                                <option value="Grade 1">Grade 1</option>
                                <option value="Grade 2">Grade 2</option>
                                <option value="Grade 3">Grade 3</option>
                                <option value="Grade 4">Grade 4</option>
                                <option value="Grade 5">Grade 5</option>
                                <option value="Grade 6">Grade 6</option>
                                <option value="Grade 7">Grade 7</option>
                                <option value="Grade 8">Grade 8</option>
                                <option value="Grade 9">Grade 9</option>
                                <option value="Grade 10">Grade 10</option>
                                <option value="Grade 11">Grade 11</option>
                                <option value="Grade 12">Grade 12</option>
                                <option value="New Student">New Student (No Previous Grade)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-layer-group"></i> Registered for Grade *</label>
                            <select name="registeredGrade" class="form-select" required>
                                <option value="">Select Grade to Register For</option>
                                <option value="Pre-K">Pre-K</option>
                                <option value="Kindergarten">Kindergarten</option>
                                <option value="Grade 1">Grade 1</option>
                                <option value="Grade 2">Grade 2</option>
                                <option value="Grade 3">Grade 3</option>
                                <option value="Grade 4">Grade 4</option>
                                <option value="Grade 5">Grade 5</option>
                                <option value="Grade 6">Grade 6</option>
                                <option value="Grade 7">Grade 7</option>
                                <option value="Grade 8">Grade 8</option>
                                <option value="Grade 9">Grade 9</option>
                                <option value="Grade 10">Grade 10</option>
                                <option value="Grade 11">Grade 11</option>
                                <option value="Grade 12">Grade 12</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-users"></i> Section</label>
                            <select name="section" class="form-select">
                                <option value="">Select Section</option>
                                <option value="A">Section A</option>
                                <option value="B">Section B</option>
                                <option value="C">Section C</option>
                                <option value="D">Section D</option>
                                <option value="E">Section E</option>
                                <option value="F">Section F</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-language"></i> Mother Tongue / First Language</label>
                            <select name="motherTongue" class="form-select">
                                <option value="">Select Mother Tongue</option>
                                <option value="Amharic">Amharic</option>
                                <option value="Oromo">Oromo</option>
                                <option value="Tigrinya">Tigrinya</option>
                                <option value="Somali">Somali</option>
                                <option value="Sidamo">Sidamo</option>
                                <option value="Wolaytta">Wolaytta</option>
                                <option value="Gurage">Gurage</option>
                                <option value="Afar">Afar</option>
                                <option value="Hadiyya">Hadiyya</option>
                                <option value="Gamo">Gamo</option>
                                <option value="English">English</option>
                                <option value="Arabic">Arabic</option>
                                <option value="French">French</option>
                                <option value="Italian">Italian</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-tint"></i> Blood Type</label>
                            <select name="bloodType" class="form-select">
                                <option value="">Select Blood Type</option>
                                <option value="A+">A+ (A Positive)</option>
                                <option value="A-">A- (A Negative)</option>
                                <option value="B+">B+ (B Positive)</option>
                                <option value="B-">B- (B Negative)</option>
                                <option value="AB+">AB+ (AB Positive)</option>
                                <option value="AB-">AB- (AB Negative)</option>
                                <option value="O+">O+ (O Positive)</option>
                                <option value="O-">O- (O Negative)</option>
                                <option value="Unknown">Unknown</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-weight"></i> Weight</label>
                            <input type="text" name="weight" placeholder="Enter weight (kg)">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-ruler-vertical"></i> Height</label>
                            <input type="text" name="height" placeholder="Enter height (cm)">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-hand-paper"></i> Hand Use</label>
                            <select name="handUse">
                                <option value="">Select hand preference</option>
                                <option value="Right">Right</option>
                                <option value="Left">Left</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2><i class="fas fa-map-marker-alt"></i> Address Information</h2>
                        <div class="form-group">
                            <label><i class="fas fa-city"></i> City</label>
                            <input type="text" name="address.city" placeholder="Enter city">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-building"></i> Sub City</label>
                            <input type="text" name="address.subCity" placeholder="Enter sub city">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-map"></i> Tabia</label>
                            <input type="text" name="address.tabia" placeholder="Enter tabia">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-home"></i> Ketena</label>
                            <input type="text" name="address.ketena" placeholder="Enter ketena">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-building"></i> Block</label>
                            <input type="text" name="address.block" placeholder="Enter block">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-flag"></i> Nationality</label>
                            <input type="text" name="address.nationality" placeholder="Enter nationality">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-notes-medical"></i> Any Serious Sickness (Optional)</label>
                            <input type="text" name="seriousSickness" placeholder="Enter any serious medical conditions">
                        </div>
                    </div>

                    <fieldset class="medical-conditions">
                        <legend><i class="fas fa-notes-medical"></i> Medical & Special Conditions</legend>
                        <div class="checkbox-group">
                            <label>
                                <input type="checkbox" name="shortSight">
                                <i class="fas fa-eye"></i>
                                Short Sight
                            </label>
                            <label>
                                <input type="checkbox" name="longSight">
                                <i class="fas fa-eye"></i>
                                Long Sight
                            </label>
                            <label>
                                <input type="checkbox" name="allergic">
                                <i class="fas fa-allergies"></i>
                                Allergic Conditions
                            </label>
                            <label>
                                <input type="checkbox" name="autism">
                                <i class="fas fa-brain"></i>
                                Autism Spectrum
                            </label>
                            <label>
                                <input type="checkbox" name="languageProblem">
                                <i class="fas fa-comment-slash"></i>
                                Language Difficulties
                            </label>
                        </div>
                    </fieldset>
                </div>
            <div class="tab-content">
                <div class="form-section">
                    <h2><i class="fas fa-male"></i> Father's Information</h2>
                    <div class="form-group">
                        <label><i class="fas fa-camera"></i> Father's Photo</label>
                        <div class="simple-photo-upload">
                            <input type="file" id="fatherPic" name="fatherPic" accept="image/*" onchange="previewImage(this, 'fatherPreview')" style="display: none;">
                            <button type="button" class="photo-upload-btn" onclick="document.getElementById('fatherPic').click()">
                                <i class="fas fa-camera"></i>
                                <span>Choose Father's Photo</span>
                            </button>
                            <div class="photo-preview-container">
                                <img id="fatherPreview" class="photo-preview" style="display: none;">
                                <div id="fatherPlaceholder" class="photo-placeholder">
                                    <i class="fas fa-user"></i>
                                    <span>No photo selected</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group"><label>Full Name</label><input type="text" name="fatherFullName"></div>
                    <div class="form-group"><label>Phone No</label><input type="text" name="fatherPhone"></div>
                    <div class="form-group"><label>Nationality</label><input type="text" name="fatherNationality"></div>
                    <div class="form-group"><label>Level of Education</label><input type="text" name="fatherEducation"></div>
                    <div class="form-group"><label>Occupation</label><input type="text" name="fatherOccupation"></div>
                    <div class="form-group"><label>Work Place</label><input type="text" name="fatherWorkplace"></div>
                    <h2><i class="fas fa-female"></i> Mother's Information</h2>
                    <div class="form-group">
                        <label><i class="fas fa-camera"></i> Mother's Photo</label>
                        <div class="simple-photo-upload">
                            <input type="file" id="motherPic" name="motherPic" accept="image/*" onchange="previewImage(this, 'motherPreview')" style="display: none;">
                            <button type="button" class="photo-upload-btn" onclick="document.getElementById('motherPic').click()">
                                <i class="fas fa-camera"></i>
                                <span>Choose Mother's Photo</span>
                            </button>
                            <div class="photo-preview-container">
                                <img id="motherPreview" class="photo-preview" style="display: none;">
                                <div id="motherPlaceholder" class="photo-placeholder">
                                    <i class="fas fa-user"></i>
                                    <span>No photo selected</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group"><label>Full Name</label><input type="text" name="motherFullName"></div>
                    <div class="form-group"><label>Phone No</label><input type="text" name="motherPhone"></div>
                    <div class="form-group"><label>Nationality</label><input type="text" name="motherNationality"></div>
                    <div class="form-group"><label>Level of Education</label><input type="text" name="motherEducation"></div>
                    <div class="form-group"><label>Occupation</label><input type="text" name="motherOccupation"></div>
                    <div class="form-group"><label>Work Place</label><input type="text" name="motherWorkplace"></div>
                </div>
            </div>
            <div class="tab-content">
                <div class="form-section">
                    <div class="form-group"><label>Guardian Name</label><input type="text" name="guardianName"></div>
                    <div class="form-group">
                        <label><i class="fas fa-heart"></i> Relationship</label>
                        <select name="guardianRelationship" class="form-select">
                            <option value="">Select Relationship</option>
                            <option value="Father">Father</option>
                            <option value="Mother">Mother</option>
                            <option value="Grandfather">Grandfather</option>
                            <option value="Grandmother">Grandmother</option>
                            <option value="Uncle">Uncle</option>
                            <option value="Aunt">Aunt</option>
                            <option value="Brother">Brother</option>
                            <option value="Sister">Sister</option>
                            <option value="Legal Guardian">Legal Guardian</option>
                            <option value="Foster Parent">Foster Parent</option>
                            <option value="Step Father">Step Father</option>
                            <option value="Step Mother">Step Mother</option>
                            <option value="Other Relative">Other Relative</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="form-group"><label>Phone Number</label><input type="text" name="guardianPhone"></div>
                </div>
            </div>
            <div class="tab-content">
                <div class="form-section">
                    <div class="form-group"><label>Sub City</label><input type="text" name="transportSubCity"></div>
                    <div class="form-group"><label>Tabya</label><input type="text" name="transportTabya"></div>
                    <div class="form-group"><label>Which Bus</label><input type="text" name="transportBus"></div>
                    <div class="form-group"><label>Start Date</label><input type="date" name="transportStartDate"></div>
                </div>
            </div>
            <div class="tab-content">
                <div class="form-section">
                    <h2><i class="fas fa-file-alt"></i> Required Documents</h2>
                    <p style="color: var(--text-muted); margin-bottom: var(--space-lg); font-size: var(--font-size-sm);">
                        Please upload the required documents. Accepted formats: PDF, JPG, PNG
                    </p>

                    <div class="document-upload-grid">
                        <div class="document-upload-item">
                            <label><i class="fas fa-certificate"></i> Birth Certificate *</label>
                            <input type="file" name="birthCertificate" accept="application/pdf,image/*" required>
                            <small>Official birth certificate document</small>
                        </div>

                        <div class="document-upload-item">
                            <label><i class="fas fa-scroll"></i> Academic Transcript</label>
                            <input type="file" name="transcript" accept="application/pdf,image/*">
                            <small>Previous school transcript (if applicable)</small>
                        </div>

                        <div class="document-upload-item">
                            <label><i class="fas fa-id-card"></i> Parents ID Documents</label>
                            <input type="file" name="parentsID" accept="application/pdf,image/*">
                            <small>Parent/Guardian identification documents</small>
                        </div>
                    </div>

                    <fieldset style="margin-top: var(--space-2xl);">
                        <legend><i class="fas fa-shopping-cart"></i> Additional Services</legend>
                        <div class="checkbox-group">
                            <label>
                                <input type="checkbox" name="buyBook">
                                <i class="fas fa-book"></i>
                                Purchase School Books
                            </label>
                        </div>
                    </fieldset>
                </div>
            </div>
                <div class="tab-navigation-buttons">
                    <button id="prevBtn" type="button" class="tab-nav-btn">
                        <i class="fas fa-arrow-left"></i> Previous
                    </button>
                    <button id="nextBtn" type="button" class="tab-nav-btn primary">
                        Next <i class="fas fa-arrow-right"></i>
                    </button>
                    <button id="submitBtn" type="submit" class="tab-nav-btn primary" style="display: none;">
                        <i class="fas fa-check"></i> Register Student
                    </button>
                </div>
            </form>
        </div>
    </main>

    <script>
        function previewImage(input, previewId) {
            const file = input.files[0];
            const preview = document.getElementById(previewId);

            // Determine the correct placeholder based on the preview ID
            let placeholderId;
            if (previewId === 'studentPreview') {
                placeholderId = 'studentPlaceholder';
            } else if (previewId === 'fatherPreview') {
                placeholderId = 'fatherPlaceholder';
            } else if (previewId === 'motherPreview') {
                placeholderId = 'motherPlaceholder';
            }

            const placeholder = document.getElementById(placeholderId);

            if (file) {
                // Validate file type
                const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                if (!validTypes.includes(file.type.toLowerCase())) {
                    alert('Please select a valid image file (JPEG, PNG, GIF, or WebP)');
                    input.value = '';
                    return;
                }

                // Validate file size (5MB limit)
                const maxSize = 5 * 1024 * 1024; // 5MB in bytes
                if (file.size > maxSize) {
                    alert('File size must be less than 5MB');
                    input.value = '';
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                    if (placeholder) {
                        placeholder.style.display = 'none';
                    }

                    // Update button text to show file name
                    const button = input.parentElement.querySelector('.photo-upload-btn span');
                    if (button) {
                        button.textContent = file.name.length > 20 ? file.name.substring(0, 20) + '...' : file.name;
                    }
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
                if (placeholder) {
                    placeholder.style.display = 'flex';
                }
            }
        }

        // Simple file upload trigger function
        function triggerFileInput(inputId) {
            document.getElementById(inputId).click();
        }

        // Add smooth form validation feedback
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const inputs = form.querySelectorAll('input[required], select[required]');

            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value.trim() === '') {
                        this.style.borderColor = 'var(--error-color)';
                        this.style.boxShadow = '0 0 0 3px rgba(245, 101, 101, 0.1)';
                    } else {
                        this.style.borderColor = 'var(--success-color)';
                        this.style.boxShadow = '0 0 0 3px rgba(72, 187, 120, 0.1)';
                    }
                });

                input.addEventListener('focus', function() {
                    this.style.borderColor = 'var(--primary-color)';
                    this.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
                });
            });
        });
    </script>
</body>
</html>