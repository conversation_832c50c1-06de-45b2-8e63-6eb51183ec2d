<%- include('../partials/header', { title: 'Students List' }) %>

<style>
    .student-card .card-img-top {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }
    .student-card .placeholder-icon {
        width: 100%;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #e9ecef;
        color: #6c757d;
        font-size: 4rem;
    }
    .student-card .card-footer {
        background-color: #f8f9fa;
    }
</style>

<div class="container mt-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Students</h1>
        <% if (isAdmin) { %>
            <a href="/students/new" class="btn btn-primary">
                <i class="fas fa-plus"></i> Register New Student
            </a>
        <% } %>
    </div>

    <!-- Flash Messages -->
    <%- include('../partials/flash-messages') %>

    <!-- Filter and Search Card -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="/students" method="GET" class="row g-3 align-items-center">
                <div class="col-md-6">
                    <input type="text" name="search" class="form-control" placeholder="Search by name or ID..." value="<%= search %>">
                </div>
                <div class="col-md-3">
                    <select name="grade" class="form-select">
                        <option value="all" <%= (grade === 'all') ? 'selected' : '' %>>All Grades</option>
                        <% for(let i = 1; i <= 12; i++) { %>
                            <option value="<%= i %>" <%= (grade == i) ? 'selected' : '' %>>Grade <%= i %></option>
                        <% } %>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-info w-100">
                        <i class="fas fa-search"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Student Cards -->
    <div class="row">
        <% if (students && students.length > 0) { %>
            <% students.forEach(student => { %>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 student-card shadow-sm">
                        <% if (student.studentPic) { %>
                            <img src="/uploads/<%= student.studentPic %>" class="card-img-top" alt="<%= student.fullName %>">
                        <% } else { %>
                            <div class="placeholder-icon">
                                <i class="fas fa-user-graduate"></i>
                            </div>
                        <% } %>
                        <div class="card-body">
                            <h5 class="card-title"><%= student.fullName %></h5>
                            <p class="card-text mb-1">
                                <strong>Grade:</strong> <%= student.registeredGrade || 'N/A' %>
                            </p>
                            <p class="card-text">
                                <strong>Section:</strong> <%= student.section || 'N/A' %>
                            </p>
                        </div>
                        <div class="card-footer d-flex justify-content-end gap-2">
                            <a href="/students/<%= student._id %>" class="btn btn-sm btn-outline-info" title="View">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <% if (isAdmin) { %>
                                <a href="/students/<%= student._id %>/edit" class="btn btn-sm btn-outline-warning" title="Edit">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <form action="/students/<%= student._id %>?_method=DELETE" method="POST" class="d-inline">
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this student?')">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </form>
                            <% } %>
                        </div>
                    </div>
                </div>
            <% }); %>
        <% } else { %>
            <div class="col-12">
                <div class="card text-center py-5">
                    <div class="card-body">
                        <h5 class="card-title">No Students Found</h5>
                        <p class="card-text">There are no students matching your criteria. Why not add one?</p>
                        <% if (isAdmin) { %>
                            <a href="/students/new" class="btn btn-primary mt-3">
                                <i class="fas fa-plus"></i> Register New Student
                            </a>
                        <% } %>
                    </div>
                </div>
            </div>
        <% } %>
    </div>
</div>

<%- include('../partials/footer') %>