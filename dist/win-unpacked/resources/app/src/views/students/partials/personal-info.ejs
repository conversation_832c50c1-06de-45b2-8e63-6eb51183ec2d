<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Personal Information</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="fullName" class="form-label">Full Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="fullName" name="fullName" value="<%= student?.fullName || '' %>" required>
                <div class="invalid-feedback">Please enter student's full name.</div>
            </div>
            <div class="col-md-6 mb-3">
                <label for="dateOfBirth" class="form-label">Date of Birth <span class="text-danger">*</span></label>
                <input type="date" class="form-control" id="dateOfBirth" name="dateOfBirth" 
                       value="<%= student?.dateOfBirth ? new Date(student.dateOfBirth).toISOString().split('T')[0] : '' %>" 
                       max="<%= new Date().toISOString().split('T')[0] %>" required>
                <div class="invalid-feedback">Please enter a valid date of birth.</div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="sex" class="form-label">Gender <span class="text-danger">*</span></label>
                <select class="form-select" id="sex" name="sex" required>
                    <option value="" disabled <%= !student?.sex ? 'selected' : '' %>>Select gender</option>
                    <option value="Male" <%= student?.sex === 'Male' ? 'selected' : '' %>>Male</option>
                    <option value="Female" <%= student?.sex === 'Female' ? 'selected' : '' %>>Female</option>
                    <option value="Other" <%= student?.sex === 'Other' ? 'selected' : '' %>>Other</option>
                </select>
                <div class="invalid-feedback">Please select a gender.</div>
            </div>
            <div class="col-md-6 mb-3">
                <label for="bloodType" class="form-label">Blood Type</label>
                <select class="form-select" id="bloodType" name="bloodType">
                    <option value="">Select blood type</option>
                    <option value="A+" <%= student?.bloodType === 'A+' ? 'selected' : '' %>>A+</option>
                    <option value="A-" <%= student?.bloodType === 'A-' ? 'selected' : '' %>>A-</option>
                    <option value="B+" <%= student?.bloodType === 'B+' ? 'selected' : '' %>>B+</option>
                    <option value="B-" <%= student?.bloodType === 'B-' ? 'selected' : '' %>>B-</option>
                    <option value="AB+" <%= student?.bloodType === 'AB+' ? 'selected' : '' %>>AB+</option>
                    <option value="AB-" <%= student?.bloodType === 'AB-' ? 'selected' : '' %>>AB-</option>
                    <option value="O+" <%= student?.bloodType === 'O+' ? 'selected' : '' %>>O+</option>
                    <option value="O-" <%= student?.bloodType === 'O-' ? 'selected' : '' %>>O-</option>
                </select>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="phoneNumber" class="form-label">Phone Number <span class="text-danger">*</span></label>
                <div class="input-group">
                    <span class="input-group-text">+251</span>
                    <input type="tel" class="form-control" id="phoneNumber" name="phoneNumber" 
                           pattern="[0-9]{9}" 
                           title="Please enter a valid 9-digit phone number"
                           value="<%= student?.phoneNumber || '' %>" required>
                </div>
                <div class="invalid-feedback">Please enter a valid 9-digit phone number (e.g., 911223344).</div>
            </div>
            <div class="col-md-6 mb-3">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" class="form-control" id="email" name="email" 
                       value="<%= student?.email || '' %>">
                <div class="invalid-feedback">Please enter a valid email address.</div>
            </div>
        </div>
        <div class="mb-3">
            <label for="address" class="form-label">Address</label>
            <textarea class="form-control" id="address" name="address" rows="2"><%= student?.address || '' %></textarea>
        </div>
    </div>
</div>
