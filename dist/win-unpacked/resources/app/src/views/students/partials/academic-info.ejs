<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Academic Information</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="studentId" class="form-label">Student ID</label>
                <input type="text" class="form-control" id="studentId" name="studentId" 
                       value="<%= student?.studentId || '' %>"
                       pattern="[A-Za-z0-9-]+"
                       title="Alphanumeric characters and hyphens only">
                <div class="form-text">Leave blank to auto-generate</div>
            </div>
            <div class="col-md-6 mb-3">
                <label for="registrationDate" class="form-label">Registration Date <span class="text-danger">*</span></label>
                <input type="date" class="form-control" id="registrationDate" name="registrationDate" 
                       value="<%= student?.registrationDate ? new Date(student.registrationDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0] %>"
                       max="<%= new Date().toISOString().split('T')[0] %>" required>
                <div class="invalid-feedback">Please select a valid registration date.</div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="registeredGrade" class="form-label">Grade <span class="text-danger">*</span></label>
                <select class="form-select" id="registeredGrade" name="registeredGrade" required>
                    <option value="" disabled <%= !student?.registeredGrade ? 'selected' : '' %>>Select grade</option>
                    <% for (let i = 1; i <= 12; i++) { %>
                        <option value="<%= i %>" <%= student?.registeredGrade === i ? 'selected' : '' %>>
                            Grade <%= i %>
                        </option>
                    <% } %>
                </select>
                <div class="invalid-feedback">Please select a grade level.</div>
            </div>
            <div class="col-md-6 mb-3">
                <label for="section" class="form-label">Section</label>
                <select class="form-select" id="section" name="section">
                    <option value="" <%= !student?.section ? 'selected' : '' %>>No Section</option>
                    <% const sections = ['A', 'B', 'C', 'D', 'E', 'F']; %>
                    <% sections.forEach(section => { %>
                        <option value="<%= section %>" <%= student?.section === section ? 'selected' : '' %>>
                            Section <%= section %>
                        </option>
                    <% }); %>
                </select>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="hasSpecialNeeds" name="hasSpecialNeeds" 
                           <%= student?.hasSpecialNeeds ? 'checked' : '' %>>
                    <label class="form-check-label" for="hasSpecialNeeds">
                        Has Special Needs
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>
