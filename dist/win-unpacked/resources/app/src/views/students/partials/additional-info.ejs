<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Additional Information</h5>
    </div>
    <div class="card-body">
        <!-- Medical Information -->
        <h6 class="mb-3">Medical Information</h6>
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="hasMedicalCondition" name="hasMedicalCondition" 
                           <%= student?.medicalInfo?.hasMedicalCondition ? 'checked' : '' %>>
                    <label class="form-check-label" for="hasMedicalCondition">
                        Has Medical Condition
                    </label>
                </div>
                <div id="medicalConditionDetails" class="mb-3" style="display: none;">
                    <label for="medicalCondition" class="form-label">Please specify</label>
                    <textarea class="form-control" id="medicalCondition" name="medicalCondition" rows="2"><%= student?.medicalInfo?.condition || '' %></textarea>
                </div>

                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="takesMedication" name="takesMedication"
                           <%= student?.medicalInfo?.takesMedication ? 'checked' : '' %>>
                    <label class="form-check-label" for="takesMedication">
                        Takes Regular Medication
                    </label>
                </div>
                <div id="medicationDetails" class="mb-3" style="display: none;">
                    <label for="medicationDescription" class="form-label">Medication Details</label>
                    <textarea class="form-control" id="medicationDescription" name="medicationDescription" rows="2"><%= student?.medicalInfo?.medicationDetails || '' %></textarea>
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="hasAllergy" name="hasAllergy"
                           <%= student?.medicalInfo?.hasAllergy ? 'checked' : '' %>>
                    <label class="form-check-label" for="hasAllergy">
                        Has Allergies
                    </label>
                </div>
                <div id="allergyDetails" class="mb-3" style="display: none;">
                    <label for="allergyDescription" class="form-label">Allergy Details</label>
                    <textarea class="form-control" id="allergyDescription" name="allergyDescription" rows="2"><%= student?.medicalInfo?.allergyDetails || '' %></textarea>
                </div>

                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="hasDisability" name="hasDisability"
                           <%= student?.medicalInfo?.hasDisability ? 'checked' : '' %>>
                    <label class="form-check-label" for="hasDisability">
                        Has Disability
                    </label>
                </div>
                <div id="disabilityDetails" class="mb-3" style="display: none;">
                    <label for="disabilityDescription" class="form-label">Disability Details</label>
                    <textarea class="form-control" id="disabilityDescription" name="disabilityDescription" rows="2"><%= student?.medicalInfo?.disabilityDetails || '' %></textarea>
                </div>
            </div>
        </div>

        <!-- Emergency Contacts (Other than Parents) -->
        <h6 class="mb-3">Emergency Contacts</h6>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="emergencyContact1Name" class="form-label">Emergency Contact 1 - Name</label>
                <input type="text" class="form-control" id="emergencyContact1Name" name="emergencyContact1Name" 
                       value="<%= student?.emergencyContacts?.[0]?.name || '' %>">
            </div>
            <div class="col-md-6 mb-3">
                <label for="emergencyContact1Phone" class="form-label">Phone Number</label>
                <div class="input-group">
                    <span class="input-group-text">+251</span>
                    <input type="tel" class="form-control" id="emergencyContact1Phone" name="emergencyContact1Phone" 
                           pattern="[0-9]{9}" 
                           title="Please enter a valid 9-digit phone number"
                           value="<%= student?.emergencyContacts?.[0]?.phone || '' %>">
                </div>
                <div class="invalid-feedback">Please enter a valid 9-digit phone number.</div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="emergencyContact2Name" class="form-label">Emergency Contact 2 - Name</label>
                <input type="text" class="form-control" id="emergencyContact2Name" name="emergencyContact2Name" 
                       value="<%= student?.emergencyContacts?.[1]?.name || '' %>">
            </div>
            <div class="col-md-6 mb-3">
                <label for="emergencyContact2Phone" class="form-label">Phone Number</label>
                <div class="input-group">
                    <span class="input-group-text">+251</span>
                    <input type="tel" class="form-control" id="emergencyContact2Phone" name="emergencyContact2Phone" 
                           pattern="[0-9]{9}" 
                           title="Please enter a valid 9-digit phone number"
                           value="<%= student?.emergencyContacts?.[1]?.phone || '' %>">
                </div>
                <div class="invalid-feedback">Please enter a valid 9-digit phone number.</div>
            </div>
        </div>

        <!-- Additional Notes -->
        <div class="mb-3">
            <label for="additionalNotes" class="form-label">Additional Notes</label>
            <textarea class="form-control" id="additionalNotes" name="additionalNotes" rows="3"><%= student?.additionalNotes || '' %></textarea>
        </div>
    </div>
</div>

<!-- Form Submission Buttons -->
<div class="d-flex justify-content-between mb-5">
    <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
        <i class="fas fa-times"></i> Cancel
    </button>
    <div>
        <button type="reset" class="btn btn-warning me-2">
            <i class="fas fa-undo"></i> Reset Form
        </button>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> <%= student ? 'Update Student' : 'Register Student' %>
        </button>
    </div>
</div>
