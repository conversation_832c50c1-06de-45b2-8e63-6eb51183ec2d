<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Change Student Status - <%= student.fullName %></title>
    <link rel="stylesheet" href="/styles-enhanced.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">
                <h1><i class="fas fa-graduation-cap"></i> Student Registration System</h1>
            </div>
            <nav>
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/register"><i class="fas fa-user-plus"></i> Register Student</a>
                <a href="/students"><i class="fas fa-users"></i> View Students</a>
                <a href="/login"><i class="fas fa-sign-in-alt"></i> Login</a>
            </nav>
        </div>
    </header>
    
    <main>
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <h1><i class="fas fa-user-edit"></i> Change Student Status</h1>
                <a href="/students" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Students
                </a>
            </div>
            
            <!-- Student Info Card -->
            <div class="card" style="margin-bottom: 2rem;">
                <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                    <h3 style="margin: 0;"><i class="fas fa-user"></i> Student Information</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: grid; grid-template-columns: auto 1fr; gap: 2rem; align-items: center;">
                        <div>
                            <% if (student.studentPic) { %>
                                <img src="/uploads/<%= student.studentPic %>" alt="<%= student.fullName %>" 
                                     style="width: 100px; height: 100px; border-radius: 50%; object-fit: cover; border: 3px solid #667eea;">
                            <% } else { %>
                                <div style="width: 100px; height: 100px; border-radius: 50%; background: #e1e8ed; 
                                            display: flex; align-items: center; justify-content: center; border: 3px solid #667eea;">
                                    <i class="fas fa-user" style="font-size: 2rem; color: #666;"></i>
                                </div>
                            <% } %>
                        </div>
                        <div>
                            <h2 style="margin: 0 0 1rem 0; color: #2c3e50;"><%= student.fullName %></h2>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                                <div>
                                    <strong>Student ID:</strong><br>
                                    <%= student.studentId || 'N/A' %>
                                </div>
                                <div>
                                    <strong>Grade & Section:</strong><br>
                                    <%= student.registeredGrade || 'N/A' %> - <%= student.section || 'N/A' %>
                                </div>
                                <div>
                                    <strong>Current Status:</strong><br>
                                    <span style="background: <%= student.status === 'active' || !student.status ? '#27ae60' : 
                                                                student.status === 'pending' ? '#f39c12' :
                                                                student.status === 'graduated' ? '#3498db' : '#e74c3c' %>; 
                                                 color: white; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.9rem; text-transform: uppercase;">
                                        <%= student.status || 'Active' %>
                                    </span>
                                </div>
                            </div>
                            <% if (student.statusDate) { %>
                                <div style="margin-top: 1rem; color: #666; font-size: 0.9rem;">
                                    <strong>Status changed on:</strong> <%= new Date(student.statusDate).toLocaleDateString() %>
                                    <% if (student.statusReason) { %>
                                        <br><strong>Reason:</strong> <%= student.statusReason %>
                                    <% } %>
                                </div>
                            <% } %>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Status Change Form -->
            <div class="card">
                <div style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                    <h3 style="margin: 0;"><i class="fas fa-exchange-alt"></i> Change Status</h3>
                </div>
                <div style="padding: 2rem;">
                    <form action="/students/<%= student._id %>/status" method="POST">
                        <div class="form-group">
                            <label><i class="fas fa-flag"></i> New Status *</label>
                            <select name="status" required>
                                <option value="">Select new status</option>
                                <option value="active" <%= student.status === 'active' || !student.status ? 'selected' : '' %>>
                                    Active - Student is currently enrolled
                                </option>
                                <option value="pending" <%= student.status === 'pending' ? 'selected' : '' %>>
                                    Pending - Student has left/suspended
                                </option>
                                <option value="graduated" <%= student.status === 'graduated' ? 'selected' : '' %>>
                                    Graduated - Student has completed studies
                                </option>
                                <option value="transferred" <%= student.status === 'transferred' ? 'selected' : '' %>>
                                    Transferred - Student moved to another school
                                </option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label><i class="fas fa-comment"></i> Reason for Status Change *</label>
                            <input type="text" name="reason" required placeholder="Enter reason for status change" 
                                   value="<%= student.statusReason || '' %>">
                        </div>
                        
                        <div class="form-group">
                            <label><i class="fas fa-sticky-note"></i> Additional Notes (Optional)</label>
                            <textarea name="notes" rows="4" placeholder="Enter any additional notes about this status change"><%= student.statusNotes || '' %></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn">
                                <i class="fas fa-save"></i> Update Status
                            </button>
                            <a href="/students" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Status History (if available) -->
            <% if (student.statusDate) { %>
                <div class="card" style="margin-top: 2rem;">
                    <div style="background: linear-gradient(135deg, #9b59b6, #8e44ad); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                        <h3 style="margin: 0;"><i class="fas fa-history"></i> Status History</h3>
                    </div>
                    <div style="padding: 1.5rem;">
                        <div style="border-left: 3px solid #667eea; padding-left: 1rem;">
                            <div style="font-weight: 600; color: #2c3e50;">
                                Status changed to: <span style="text-transform: uppercase;"><%= student.status %></span>
                            </div>
                            <div style="color: #666; font-size: 0.9rem; margin: 0.5rem 0;">
                                Date: <%= new Date(student.statusDate).toLocaleDateString() %>
                            </div>
                            <% if (student.statusReason) { %>
                                <div style="color: #666; font-size: 0.9rem; margin: 0.5rem 0;">
                                    Reason: <%= student.statusReason %>
                                </div>
                            <% } %>
                            <% if (student.statusNotes) { %>
                                <div style="color: #666; font-size: 0.9rem; margin: 0.5rem 0;">
                                    Notes: <%= student.statusNotes %>
                                </div>
                            <% } %>
                        </div>
                    </div>
                </div>
            <% } %>
        </div>
    </main>
    
    <script>
        // Add confirmation for status changes
        document.querySelector('form').addEventListener('submit', function(e) {
            const status = document.querySelector('select[name="status"]').value;
            const studentName = '<%= student.fullName %>';
            
            let message = '';
            switch(status) {
                case 'pending':
                    message = `Are you sure you want to mark ${studentName} as PENDING? This will remove them from active student lists.`;
                    break;
                case 'graduated':
                    message = `Are you sure you want to mark ${studentName} as GRADUATED?`;
                    break;
                case 'transferred':
                    message = `Are you sure you want to mark ${studentName} as TRANSFERRED?`;
                    break;
                case 'active':
                    message = `Are you sure you want to reactivate ${studentName}?`;
                    break;
            }
            
            if (message && !confirm(message)) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
