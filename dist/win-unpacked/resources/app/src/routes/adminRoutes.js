const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin } = require('../middleware/auth');
const adminController = require('../controllers/adminController');

// Admin dashboard route
router.get('/dashboard', isAuthenticated, isAdmin, adminController.getDashboard);

// User management routes
router.get('/users', isAuthenticated, isAdmin, adminController.getUsers);
router.post('/users', isAuthenticated, isAdmin, adminController.createUser);
router.delete('/users/:id', isAuthenticated, isAdmin, adminController.deleteUser);

module.exports = router;
