{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,2CAAkD;AAClD,qCAIkB;AACT,uGAHP,+BAAsB,OAGO;AAE/B,MAAM,cAAc,GAAG,2BAA2B,CAAC;AAEnD,SAAS,8BAA8B,CAAC,gBAAwB;IAC9D,OAAO,CACL,gBAAgB,CAAC,UAAU,CAAC,YAAY,CAAC;QACzC,gBAAgB,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAC9C,CAAC;AACJ,CAAC;AAID,MAAM,WAAW,GACf,4GAA4G,CAAC;AAE/G,MAAM,kBAA8C,SAAQ,GAAc;IACxE,MAAM,CAAC,IAAO;QACZ,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,GAAG,CAAC,IAAO;QACT,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED,GAAG,CAAC,IAAO;QACT,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED,GAAG,CAAC,IAAO,EAAE,KAAU;QACrB,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,aAAa,CAAC,IAAS;QACrB,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;QACjB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE;YAC7B,IAAI,GAAG,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,EAAE;gBAC5C,IAAI,GAAG,GAAG,CAAC;gBACX,MAAM;aACP;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,SAAS,6BAA6B,CAA4B,IAA4B;IAC5F,OAAO,MAAM,6BAA8B,SAAQ,IAAI;QACrD,MAAM,CAAC,IAAO,EAAE,KAAU;YACxB,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,CAAC,IAAO;YACZ,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;QAChD,CAAC;QAED,GAAG,CAAC,IAAO;YACT,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,MAAM,CAAC,IAAO;YACZ,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;QAChD,CAAC;QAED,GAAG,CAAC,IAAO;YACT,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,GAAG,CAAC,IAAO,EAAE,KAAU;YACrB,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;QAED,IAAI;YACF,OAAO,KAAK,CAAC,IAAI,EAAyB,CAAC;QAC7C,CAAC;QAED,MAAM;YACJ,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC;QACxB,CAAC;QAED,OAAO;YACL,OAAO,KAAK,CAAC,OAAO,EAAmC,CAAC;QAC1D,CAAC;QAED,CAAC,MAAM,CAAC,QAAQ,CAAC;YACf,OAAO,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAmC,CAAC;QACnE,CAAC;QAED,aAAa,CAAC,IAAO;YACnB,OAAO,kBAAkB,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACrE,CAAC;KACF,CAAC;AACJ,CAAC;AAGD,MAAe,cAAe,SAAQ,gBAAG;CASxC;AAED,MAAM,eAAgB,SAAQ,KAAK;IACjC,IAAI,IAAI;QACN,OAAO,iBAAiB,CAAC;IAC3B,CAAC;CACF;AAUD,MAAa,gBAAiB,SAAQ,cAAc;IAIlD,YAAY,GAAW,EAAE,UAA0C,EAAE;;QACnE,MAAM,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;QACpC,IAAI,CAAC,eAAe,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,EAAE;YAC5D,MAAM,IAAI,eAAe,CAAC,2FAA2F,CAAC,CAAC;SACxH;QAED,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACrC,IAAI,CAAC,KAAK,EAAE;YACV,MAAM,IAAI,eAAe,CAAC,8BAA8B,GAAG,GAAG,CAAC,CAAC;SACjE;QAED,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAA,KAAK,CAAC,MAAM,mCAAI,EAAE,CAAC;QAEzE,IAAI,CAAC,eAAe,EAAE;YACpB,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;gBACvB,MAAM,IAAI,eAAe,CAAC,2CAA2C,GAAG,GAAG,CAAC,CAAC;aAC9E;YAED,IAAI;gBACF,kBAAkB,CAAC,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,EAAE,CAAC,CAAC;gBACnC,kBAAkB,CAAC,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,EAAE,CAAC,CAAC;aACpC;YAAC,OAAO,GAAG,EAAE;gBACZ,MAAM,IAAI,eAAe,CAAE,GAAa,CAAC,OAAO,CAAC,CAAC;aACnD;YAGD,MAAM,iBAAiB,GAAG,cAAc,CAAC;YACzC,IAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC,iBAAiB,CAAC,EAAE;gBACtC,MAAM,IAAI,eAAe,CAAC,0CAA0C,QAAQ,EAAE,CAAC,CAAC;aACjF;YACD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;gBAC1B,MAAM,kBAAkB,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,QAAQ,KAAK,EAAE,EAAE,CAAC,CAAC;gBAC7D,IAAI,kBAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,kBAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;oBAC5E,MAAM,IAAI,eAAe,CAAC,sCAAsC,CAAC,CAAC;iBACnE;aACF;YAED,IAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC,iBAAiB,CAAC,EAAE;gBACtC,MAAM,IAAI,eAAe,CAAC,wCAAwC,CAAC,CAAC;aACrE;SACF;QAED,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,IAAI,OAAO,QAAQ,KAAK,QAAQ;YAAE,UAAU,IAAI,QAAQ,CAAC;QACzD,IAAI,OAAO,QAAQ,KAAK,QAAQ;YAAE,UAAU,IAAI,IAAI,QAAQ,EAAE,CAAC;QAC/D,IAAI,UAAU;YAAE,UAAU,IAAI,GAAG,CAAC;QAElC,IAAI;YACF,KAAK,CAAC,GAAG,QAAQ,CAAC,WAAW,EAAE,MAAM,UAAU,GAAG,cAAc,GAAG,IAAI,EAAE,CAAC,CAAC;SAC5E;QAAC,OAAO,GAAQ,EAAE;YACjB,IAAI,eAAe,EAAE;gBAInB,IAAI,gBAAgB,CAAC,GAAG,EAAE;oBACxB,GAAG,OAAO;oBACV,eAAe,EAAE,KAAK;iBACvB,CAAC,CAAC;aACJ;YACD,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,EAAE;gBACnC,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;aAC1D;YACD,MAAM,GAAG,CAAC;SACX;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE/B,IAAI,CAAC,eAAe,EAAE;YACpB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzC,MAAM,IAAI,eAAe,CAAC,oDAAoD,CAAC,CAAC;aACjF;YACD,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC7D,MAAM,IAAI,eAAe,CAAC,yCAAyC,CAAC,CAAC;aACtE;SACF;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;SACrB;QACD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,EAAE,6BAA6B,CAAC,IAAI,CAAC,YAAY,CAAC,WAAkB,CAAC,CAAC,SAAS,CAAC,CAAC;IAC1H,CAAC;IAKD,IAAI,IAAI,KAAY,OAAO,cAAuB,CAAC,CAAC,CAAC;IACrD,IAAI,IAAI,CAAC,QAAe,IAAI,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC;IACtF,IAAI,QAAQ,KAAY,OAAO,cAAuB,CAAC,CAAC,CAAC;IACzD,IAAI,QAAQ,CAAC,QAAe,IAAI,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC;IAC1F,IAAI,IAAI,KAAY,OAAO,EAAW,CAAC,CAAC,CAAC;IACzC,IAAI,IAAI,CAAC,QAAe,IAAI,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC;IACtF,IAAI,IAAI,KAAa,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC9C,IAAI,IAAI,CAAC,QAAgB,IAAI,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC,CAAC,CAAC;IAEzF,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,IAAI,KAAK,CAAC,IAAc;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,CAAC;IAED,QAAQ;QACN,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACxE,CAAC;IAED,KAAK;QACH,OAAO,IAAI,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YAC3C,eAAe,EAAE,IAAI;SACtB,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,OAA0C;QAC/C,OAAO,IAAA,oCAA2B,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAGD,iBAAiB;QACf,MAAM,QAAQ,GAAI,KAAc,IAAI,IAAI,CAAC,6BAA6B,CAAmB,4BAAe,CAAC,CAAC,EAAE,CAAC;QAC7G,OAAO,IAAI,CAAC,YAA0C,CAAC;IACzD,CAAC;IAED,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QACxC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;QACzG,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;IACrG,CAAC;CACF;AArID,4CAqIC;AAOD,MAAa,4BAAqE,SAAQ,kBAAoC;IAC5H,YAAY,IAAoB;QAC9B,KAAK,EAAE,CAAC;QACR,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YAC3C,IAAI,CAAC,KAAK;gBAAE,SAAS;YACrB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAEtC,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE;gBACrB,IAAI,CAAC,GAAG,CAAC,KAA2B,EAAE,EAAE,CAAC,CAAC;aAC3C;iBAAM;gBACL,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAuB,EAAE,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;aACzF;SACF;IACH,CAAC;IAED,QAAQ;QACN,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3D,CAAC;CACF;AAlBD,oEAkBC;AAED,kBAAe,gBAAgB,CAAC"}