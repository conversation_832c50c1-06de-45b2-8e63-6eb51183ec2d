{"name": "mongodb", "version": "6.17.0", "description": "The official MongoDB driver for Node.js", "main": "lib/index.js", "files": ["lib", "src", "etc/prepare.js", "mongodb.d.ts", "tsconfig.json"], "types": "mongodb.d.ts", "repository": {"type": "git", "url": "**************:mongodb/node-mongodb-native.git"}, "author": {"name": "The MongoDB NodeJS Team", "email": "<EMAIL>"}, "dependencies": {"@mongodb-js/saslprep": "^1.1.9", "bson": "^6.10.4", "mongodb-connection-string-url": "^3.0.0"}, "peerDependencies": {"@aws-sdk/credential-providers": "^3.188.0", "@mongodb-js/zstd": "^1.1.0 || ^2.0.0", "gcp-metadata": "^5.2.0", "kerberos": "^2.0.1", "mongodb-client-encryption": ">=6.0.0 <7", "snappy": "^7.2.2", "socks": "^2.7.1"}, "peerDependenciesMeta": {"@aws-sdk/credential-providers": {"optional": true}, "@mongodb-js/zstd": {"optional": true}, "kerberos": {"optional": true}, "snappy": {"optional": true}, "mongodb-client-encryption": {"optional": true}, "gcp-metadata": {"optional": true}, "socks": {"optional": true}}, "devDependencies": {"@aws-sdk/credential-providers": "^3.632.0", "@iarna/toml": "^2.2.5", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@microsoft/api-extractor": "^7.52.5", "@microsoft/tsdoc-config": "^0.17.1", "@mongodb-js/zstd": "^2.0.1", "@types/chai": "^4.3.17", "@types/chai-subset": "^1.3.5", "@types/express": "^5.0.1", "@types/kerberos": "^1.1.5", "@types/mocha": "^10.0.9", "@types/node": "^22.15.3", "@types/saslprep": "^1.0.3", "@types/semver": "^7.7.0", "@types/sinon": "^17.0.4", "@types/sinon-chai": "^4.0.0", "@types/whatwg-url": "^13.0.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "chai": "^4.4.1", "chai-subset": "^1.6.0", "chalk": "^4.1.2", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-mocha": "^10.4.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tsdoc": "^0.4.0", "eslint-plugin-unused-imports": "^4.1.4", "express": "^5.1.0", "gcp-metadata": "^5.3.0", "js-yaml": "^4.1.0", "mocha": "^10.8.2", "mocha-sinon": "^2.1.2", "mongodb-client-encryption": "^6.4.0", "mongodb-legacy": "^6.1.3", "nyc": "^15.1.0", "prettier": "^3.5.3", "semver": "^7.7.0", "sinon": "^18.0.1", "sinon-chai": "^3.7.0", "snappy": "^7.2.2", "socks": "^2.8.1", "source-map-support": "^0.5.21", "ts-node": "^10.9.2", "tsd": "^0.31.2", "typescript": "5.5", "typescript-cached-transpile": "^0.0.6", "v8-heapsnapshot": "^1.3.1", "yargs": "^17.7.2"}, "license": "Apache-2.0", "engines": {"node": ">=16.20.1"}, "homepage": "https://github.com/mongodb/node-mongodb-native", "tsd": {"directory": "test/types", "compilerOptions": {"strict": true, "target": "esnext", "module": "commonjs", "moduleResolution": "node"}}}