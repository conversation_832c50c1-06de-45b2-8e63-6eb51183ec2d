# System Configuration
SYSTEM_NAME=SHEGE SRS
SYSTEM_FULL_NAME=Student Registration System
SYSTEM_VERSION=1.0.0

# School Information
SCHOOL_NAME=SHEGE SRS
SCHOOL_ADDRESS=123 Education Street, City, Country
SCHOOL_PHONE=******-567-8900
SCHOOL_EMAIL=<EMAIL>
SCHOOL_WEBSITE=https://www.shegesrs.edu

# Database Configuration
DB_NAME=student-registration
MONGODB_URI=mongodb://localhost:27017/student-registration

# Server Configuration
PORT=3000
NODE_ENV=development
SESSION_SECRET=your_strong_secret_key_here

# System Settings
DEFAULT_LANGUAGE=en
TIMEZONE=UTC
DATE_FORMAT=MM/DD/YYYY

# Branding
PRIMARY_COLOR=#ff6b35
SECONDARY_COLOR=#2c3e50
LOGO_PATH=/images/logo.png

# Features
ENABLE_REGISTRATION=true
ENABLE_PRINT_FEATURES=true
ENABLE_EXPORT_FEATURES=true

# File Upload Settings
MAX_FILE_SIZE=5MB

# Pagination
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# Security
SESSION_TIMEOUT=3600000
MAX_LOGIN_ATTEMPTS=5
COOKIE_SECURE=false # Set to true in production with HTTPS

# Development
DEBUG_MODE=false

# Email Configuration (Optional)
EMAIL_ENABLED=false
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Backup Configuration (Optional)
BACKUP_ENABLED=false
BACKUP_INTERVAL=24h
BACKUP_PATH=./backups

# API Settings
API_VERSION=v1
API_RATE_LIMIT=100

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Cache
CACHE_ENABLED=true
CACHE_TTL=300

# Reports
DEFAULT_REPORT_FORMAT=pdf
REPORTS_PATH=./reports

# Student ID Generation
STUDENT_ID_PREFIX=STU
STUDENT_ID_LENGTH=8

# Academic Year
CURRENT_ACADEMIC_YEAR=2024
ACADEMIC_YEAR_START=09-01
ACADEMIC_YEAR_END=08-31
