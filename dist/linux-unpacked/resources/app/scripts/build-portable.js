#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class PortableBuilder {
    constructor() {
        this.buildDir = path.join(__dirname, '..', 'dist');
        this.packageJson = require('../package.json');
    }

    log(message) {
        console.log(`🔧 ${message}`);
    }

    error(message) {
        console.error(`❌ ${message}`);
    }

    success(message) {
        console.log(`✅ ${message}`);
    }

    async checkPrerequisites() {
        this.log('Checking build prerequisites...');
        
        // Check if electron-builder is installed
        try {
            execSync('npx electron-builder --version', { stdio: 'pipe' });
            this.success('electron-builder is available');
        } catch (error) {
            this.error('electron-builder not found. Installing...');
            execSync('npm install electron-builder --save-dev', { stdio: 'inherit' });
        }

        // Check if all dependencies are installed
        if (!fs.existsSync('node_modules')) {
            this.log('Installing dependencies...');
            execSync('npm install', { stdio: 'inherit' });
        }

        // Check if assets directory exists
        if (!fs.existsSync('assets')) {
            this.log('Creating assets directory...');
            fs.mkdirSync('assets', { recursive: true });
            
            // Create a simple icon if none exists
            this.createDefaultIcon();
        }
    }

    createDefaultIcon() {
        this.log('Creating default application icon...');

        // Create a simple SVG icon
        const svgIcon = `
<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <rect width="512" height="512" fill="#ff6b35"/>
  <text x="256" y="280" font-family="Arial, sans-serif" font-size="120" font-weight="bold" text-anchor="middle" fill="white">SRS</text>
  <text x="256" y="350" font-family="Arial, sans-serif" font-size="40" text-anchor="middle" fill="white">SHEGE</text>
</svg>`;

        fs.writeFileSync('assets/icon.svg', svgIcon);

        // Note: In production, you should convert this to proper icon formats
        this.log('Default icon created. Consider replacing with a proper icon.');
    }

    ensureEnvFile() {
        if (!fs.existsSync('.env')) {
            this.log('Creating .env file from template...');
            if (fs.existsSync('.env.example')) {
                fs.copyFileSync('.env.example', '.env');
            } else {
                // Create a minimal .env file
                const envContent = `# SHEGE SRS Configuration
SYSTEM_NAME=SHEGE SRS
MONGODB_URI=mongodb://localhost:27017/student-registration
PORT=3000
NODE_ENV=production
SESSION_SECRET=change-this-secret-key
`;
                fs.writeFileSync('.env', envContent);
            }
            this.success('.env file created');
        }
    }

    async buildWindows() {
        this.log('Building Windows portable application...');

        try {
            // Ensure .env file exists for portable build
            this.ensureEnvFile();

            // Create temporary config file for electron-builder
            const configPath = path.join(__dirname, '..', 'electron-builder-temp.json');
            const buildConfig = {
                appId: "com.shegesrs.desktop",
                productName: "SHEGE SRS",
                directories: {
                    output: "dist"
                },
                files: [
                    "src/**/*",
                    "electron-main.js",
                    "electron-preload.js",
                    "package.json",
                    ".env",
                    "node_modules/**/*",
                    "!node_modules/.cache/**/*",
                    "!node_modules/.bin/**/*"
                ],
                win: {
                    target: {
                        target: "portable",
                        arch: ["x64"]
                    },
                    artifactName: "${productName}-${version}-win-portable.${ext}",
                    icon: "assets/icon.ico"
                }
            };

            // Write config to temporary file
            fs.writeFileSync(configPath, JSON.stringify(buildConfig, null, 2));

            // Build using config file
            execSync(`npx electron-builder --win --config "${configPath}"`, {
                stdio: 'inherit'
            });

            // Clean up temporary config file
            if (fs.existsSync(configPath)) {
                fs.unlinkSync(configPath);
            }

            this.success('Windows portable build completed');
            return true;
        } catch (error) {
            this.error(`Windows build failed: ${error.message}`);
            return false;
        }
    }

    async buildLinux() {
        this.log('Building Linux portable application...');

        try {
            // Ensure .env file exists for portable build
            this.ensureEnvFile();

            // Create temporary config file for electron-builder
            const configPath = path.join(__dirname, '..', 'electron-builder-temp.json');
            const buildConfig = {
                appId: "com.shegesrs.desktop",
                productName: "SHEGE SRS",
                directories: {
                    output: "dist"
                },
                files: [
                    "src/**/*",
                    "electron-main.js",
                    "electron-preload.js",
                    "package.json",
                    ".env",
                    "node_modules/**/*",
                    "!node_modules/.cache/**/*",
                    "!node_modules/.bin/**/*"
                ],
                linux: {
                    target: {
                        target: "AppImage",
                        arch: ["x64"]
                    },
                    artifactName: "${productName}-${version}-linux-portable.${ext}",
                    icon: "assets/icon.png",
                    category: "Education"
                }
            };

            // Write config to temporary file
            fs.writeFileSync(configPath, JSON.stringify(buildConfig, null, 2));

            // Build using config file
            execSync(`npx electron-builder --linux --config "${configPath}"`, {
                stdio: 'inherit'
            });

            // Clean up temporary config file
            if (fs.existsSync(configPath)) {
                fs.unlinkSync(configPath);
            }

            this.success('Linux portable build completed');
            return true;
        } catch (error) {
            this.error(`Linux build failed: ${error.message}`);
            return false;
        }
    }

    async buildAll() {
        this.log('Building portable applications for all platforms...');
        
        const windowsSuccess = await this.buildWindows();
        const linuxSuccess = await this.buildLinux();
        
        return windowsSuccess && linuxSuccess;
    }

    listBuiltFiles() {
        this.log('Built files:');
        
        if (fs.existsSync(this.buildDir)) {
            const files = fs.readdirSync(this.buildDir);
            files.forEach(file => {
                const filePath = path.join(this.buildDir, file);
                const stats = fs.statSync(filePath);
                const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
                console.log(`  📦 ${file} (${sizeInMB} MB)`);
            });
        } else {
            this.error('No build directory found');
        }
    }

    async createReadme() {
        const readmeContent = `# SHEGE SRS Portable Applications

## Windows Portable
- **File**: SHEGE SRS-${this.packageJson.version}-win-portable.exe
- **Requirements**: Windows 10 or later
- **Installation**: No installation required, just run the executable
- **Data**: Application data is stored in the same directory as the executable

## Linux Portable (AppImage)
- **File**: SHEGE SRS-${this.packageJson.version}-linux-portable.AppImage
- **Requirements**: Most Linux distributions
- **Installation**: Make executable and run: \`chmod +x *.AppImage && ./SHEGE*.AppImage\`
- **Data**: Application data is stored in ~/.config/shege-srs-desktop

## Prerequisites
Both versions require:
- **MongoDB**: Must be installed and running
- **Network**: Port 3000 must be available
- **Memory**: At least 4GB RAM recommended
- **Storage**: At least 2GB free space

## First Run
1. Ensure MongoDB is installed and running
2. Run the portable application
3. Wait for the server to start (may take 30-60 seconds on first run)
4. Login with default credentials (change immediately):
   - Username: admin
   - Password: admin123

## Troubleshooting
- **"Unable to start"**: Ensure MongoDB is running
- **"Application Server is Not Running"**: Check if port 3000 is available
- **Slow startup**: Normal on first run, subsequent starts should be faster

## Support
For support and documentation, visit: [Your Support URL]

Built on: ${new Date().toISOString()}
Version: ${this.packageJson.version}
`;

        fs.writeFileSync(path.join(this.buildDir, 'README.txt'), readmeContent);
        this.success('README.txt created in build directory');
    }

    async run(platform = 'all') {
        console.log('🚀 SHEGE SRS Portable Builder');
        console.log('=' .repeat(50));
        
        try {
            await this.checkPrerequisites();
            
            let success = false;
            
            switch (platform.toLowerCase()) {
                case 'windows':
                case 'win':
                    success = await this.buildWindows();
                    break;
                case 'linux':
                    success = await this.buildLinux();
                    break;
                case 'all':
                default:
                    success = await this.buildAll();
                    break;
            }
            
            if (success) {
                this.listBuiltFiles();
                await this.createReadme();
                
                console.log('');
                console.log('=' .repeat(50));
                this.success('Portable build process completed successfully!');
                console.log('');
                console.log('📁 Built files are in the "dist" directory');
                console.log('📖 See README.txt in the dist directory for usage instructions');
            } else {
                this.error('Build process failed. Check the errors above.');
                process.exit(1);
            }
            
        } catch (error) {
            this.error(`Build process failed: ${error.message}`);
            process.exit(1);
        }
    }
}

// Run if called directly
if (require.main === module) {
    const platform = process.argv[2] || 'all';
    const builder = new PortableBuilder();
    builder.run(platform);
}

module.exports = PortableBuilder;
