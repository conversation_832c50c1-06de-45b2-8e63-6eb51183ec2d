#!/usr/bin/env node

const { spawn } = require('child_process');
const net = require('net');
const fs = require('fs');
const path = require('path');

class DependencyChecker {
    constructor() {
        this.checks = [];
        this.results = [];
    }

    async checkMongoDB() {
        return new Promise((resolve) => {
            console.log('Checking MongoDB connection...');
            
            const client = net.createConnection({ port: 27017, host: 'localhost' });
            
            const timeout = setTimeout(() => {
                client.destroy();
                resolve({
                    name: 'MongoDB',
                    status: 'failed',
                    message: 'MongoDB is not running on localhost:27017',
                    solution: 'Please start MongoDB service or install MongoDB'
                });
            }, 3000);
            
            client.on('connect', () => {
                clearTimeout(timeout);
                client.end();
                resolve({
                    name: 'MongoDB',
                    status: 'success',
                    message: 'MongoDB is running and accessible'
                });
            });
            
            client.on('error', () => {
                clearTimeout(timeout);
                resolve({
                    name: 'MongoDB',
                    status: 'failed',
                    message: 'Cannot connect to MongoDB',
                    solution: 'Please start MongoDB service: sudo systemctl start mongod'
                });
            });
        });
    }

    async checkNodeVersion() {
        return new Promise((resolve) => {
            console.log('Checking Node.js version...');
            
            const nodeVersion = process.version;
            const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
            
            if (majorVersion >= 16) {
                resolve({
                    name: 'Node.js',
                    status: 'success',
                    message: `Node.js ${nodeVersion} is compatible`
                });
            } else {
                resolve({
                    name: 'Node.js',
                    status: 'warning',
                    message: `Node.js ${nodeVersion} detected. Recommended: v16+`,
                    solution: 'Consider upgrading Node.js for better performance'
                });
            }
        });
    }

    async checkRequiredFiles() {
        return new Promise((resolve) => {
            console.log('Checking required files...');
            
            const requiredFiles = [
                'src/app.js',
                'src/models/student.js',
                'src/routes/studentRoutes.js',
                'package.json'
            ];
            
            const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
            
            if (missingFiles.length === 0) {
                resolve({
                    name: 'Required Files',
                    status: 'success',
                    message: 'All required files are present'
                });
            } else {
                resolve({
                    name: 'Required Files',
                    status: 'failed',
                    message: `Missing files: ${missingFiles.join(', ')}`,
                    solution: 'Ensure all application files are properly installed'
                });
            }
        });
    }

    async checkEnvironmentFile() {
        return new Promise((resolve) => {
            console.log('Checking environment configuration...');
            
            if (!fs.existsSync('.env')) {
                resolve({
                    name: 'Environment Config',
                    status: 'warning',
                    message: '.env file not found',
                    solution: 'Copy .env.example to .env and configure settings'
                });
                return;
            }

            const envContent = fs.readFileSync('.env', 'utf8');
            const hasMongoUri = envContent.includes('MONGODB_URI');
            const hasSessionSecret = envContent.includes('SESSION_SECRET');
            
            if (hasMongoUri && hasSessionSecret) {
                resolve({
                    name: 'Environment Config',
                    status: 'success',
                    message: 'Environment configuration is present'
                });
            } else {
                resolve({
                    name: 'Environment Config',
                    status: 'warning',
                    message: 'Environment configuration may be incomplete',
                    solution: 'Review .env file for required settings'
                });
            }
        });
    }

    async checkDiskSpace() {
        return new Promise((resolve) => {
            console.log('Checking disk space...');
            
            try {
                const stats = fs.statSync('.');
                // This is a simplified check - in production you'd want more sophisticated disk space checking
                resolve({
                    name: 'Disk Space',
                    status: 'success',
                    message: 'Sufficient disk space available'
                });
            } catch (error) {
                resolve({
                    name: 'Disk Space',
                    status: 'warning',
                    message: 'Could not check disk space',
                    solution: 'Ensure sufficient disk space (at least 1GB free)'
                });
            }
        });
    }

    async runAllChecks() {
        console.log('🔍 Running dependency checks...\n');
        
        const checks = [
            this.checkNodeVersion(),
            this.checkMongoDB(),
            this.checkRequiredFiles(),
            this.checkEnvironmentFile(),
            this.checkDiskSpace()
        ];
        
        this.results = await Promise.all(checks);
        
        return this.generateReport();
    }

    generateReport() {
        console.log('📋 Dependency Check Report');
        console.log('=' .repeat(50));
        
        let hasErrors = false;
        let hasWarnings = false;
        
        this.results.forEach(result => {
            const icon = result.status === 'success' ? '✅' : 
                        result.status === 'warning' ? '⚠️' : '❌';
            
            console.log(`${icon} ${result.name}: ${result.message}`);
            
            if (result.solution) {
                console.log(`   💡 Solution: ${result.solution}`);
            }
            
            if (result.status === 'failed') hasErrors = true;
            if (result.status === 'warning') hasWarnings = true;
            
            console.log('');
        });
        
        console.log('=' .repeat(50));
        
        if (hasErrors) {
            console.log('❌ Some critical dependencies are missing. Please fix the issues above before running the application.');
            return false;
        } else if (hasWarnings) {
            console.log('⚠️  Some warnings detected. The application should work but consider addressing the warnings.');
            return true;
        } else {
            console.log('✅ All dependency checks passed! The application should run successfully.');
            return true;
        }
    }
}

// Run checks if this script is executed directly
if (require.main === module) {
    const checker = new DependencyChecker();
    
    checker.runAllChecks().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('Error running dependency checks:', error);
        process.exit(1);
    });
}

module.exports = DependencyChecker;
