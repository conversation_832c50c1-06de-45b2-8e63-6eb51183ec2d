const mongoose = require('mongoose');
const User = require('../src/models/user');
const dotenv = require('dotenv');
const readline = require('readline');

// Load environment variables
dotenv.config();

// Database connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/student-registration', {
    useNewUrlParser: true,
    useUnifiedTopology: true
}).then(() => {
    console.log('Connected to MongoDB');
    createAdmin();
}).catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
});

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

async function createAdmin() {
    try {
        // Check if admin already exists
        const adminExists = await User.findOne({ isAdmin: true });
        if (adminExists) {
            console.log('Admin user already exists:', adminExists.username);
            process.exit(0);
        }

        rl.question('Enter admin username: ', async (username) => {
            rl.question('Enter admin password (min 6 characters): ', { silent: true }, async (password) => {
                if (password.length < 6) {
                    console.error('Password must be at least 6 characters long');
                    process.exit(1);
                }

                try {
                    const admin = new User({
                        username: username.trim().toLowerCase(),
                        password,
                        isAdmin: true
                    });

                    await admin.save();
                    console.log('Admin user created successfully!');
                    process.exit(0);
                } catch (error) {
                    console.error('Error creating admin user:', error.message);
                    process.exit(1);
                } finally {
                    rl.close();
                }
            });
        });
    } catch (error) {
        console.error('Error:', error.message);
        process.exit(1);
    }
}
