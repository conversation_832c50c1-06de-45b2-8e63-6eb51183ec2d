#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class SimpleBuilder {
    constructor() {
        this.buildDir = path.join(__dirname, '..', 'dist');
    }

    log(message) {
        console.log(`🔧 ${message}`);
    }

    error(message) {
        console.error(`❌ ${message}`);
    }

    success(message) {
        console.log(`✅ ${message}`);
    }

    ensureEnvFile() {
        if (!fs.existsSync('.env')) {
            this.log('Creating .env file from template...');
            if (fs.existsSync('.env.example')) {
                fs.copyFileSync('.env.example', '.env');
            } else {
                const envContent = `# SHEGE SRS Configuration
SYSTEM_NAME=SHEGE SRS
MONGODB_URI=mongodb://localhost:27017/student-registration
PORT=3000
NODE_ENV=production
SESSION_SECRET=change-this-secret-key-here
`;
                fs.writeFileSync('.env', envContent);
            }
            this.success('.env file created');
        }
    }

    ensureAssets() {
        if (!fs.existsSync('assets')) {
            fs.mkdirSync('assets', { recursive: true });
        }

        // Create simple PNG icon if it doesn't exist
        if (!fs.existsSync('assets/icon.png')) {
            this.log('Creating default icon...');
            // Copy the SVG icon we created earlier
            if (fs.existsSync('assets/icon.svg')) {
                this.log('SVG icon exists, you may want to convert it to PNG for better compatibility');
            }
        }
    }

    async buildWindows() {
        this.log('Building Windows portable application...');

        try {
            this.ensureEnvFile();
            this.ensureAssets();

            // Use the package.json configuration directly, skip icon requirement
            execSync('npx electron-builder --win --config.win.target=portable --config.win.icon=null', {
                stdio: 'inherit',
                cwd: path.join(__dirname, '..')
            });

            this.success('Windows portable build completed');
            return true;
        } catch (error) {
            this.error(`Windows build failed: ${error.message}`);
            return false;
        }
    }

    async buildLinux() {
        this.log('Building Linux portable application...');

        try {
            this.ensureEnvFile();
            this.ensureAssets();

            // Use the package.json configuration directly, skip icon requirement
            execSync('npx electron-builder --linux --config.linux.target=AppImage --config.linux.icon=null', {
                stdio: 'inherit',
                cwd: path.join(__dirname, '..')
            });

            this.success('Linux portable build completed');
            return true;
        } catch (error) {
            this.error(`Linux build failed: ${error.message}`);
            return false;
        }
    }

    async buildAll() {
        this.log('Building portable applications for all platforms...');
        
        const windowsSuccess = await this.buildWindows();
        const linuxSuccess = await this.buildLinux();
        
        return windowsSuccess && linuxSuccess;
    }

    listBuiltFiles() {
        this.log('Built files:');
        
        if (fs.existsSync(this.buildDir)) {
            const files = fs.readdirSync(this.buildDir);
            files.forEach(file => {
                const filePath = path.join(this.buildDir, file);
                const stats = fs.statSync(filePath);
                const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
                console.log(`  📦 ${file} (${sizeInMB} MB)`);
            });
        } else {
            this.error('No build directory found');
        }
    }

    async run(platform = 'all') {
        console.log('🚀 SHEGE SRS Simple Portable Builder');
        console.log('=' .repeat(50));
        
        try {
            let success = false;
            
            switch (platform.toLowerCase()) {
                case 'windows':
                case 'win':
                    success = await this.buildWindows();
                    break;
                case 'linux':
                    success = await this.buildLinux();
                    break;
                case 'all':
                default:
                    success = await this.buildAll();
                    break;
            }
            
            if (success) {
                this.listBuiltFiles();
                
                console.log('');
                console.log('=' .repeat(50));
                this.success('Build process completed successfully!');
                console.log('');
                console.log('📁 Built files are in the "dist" directory');
                console.log('');
                console.log('🔧 To test your builds:');
                console.log('   Windows: Double-click the .exe file');
                console.log('   Linux: chmod +x *.AppImage && ./SHEGE*.AppImage');
            } else {
                this.error('Build process failed. Check the errors above.');
                process.exit(1);
            }
            
        } catch (error) {
            this.error(`Build process failed: ${error.message}`);
            process.exit(1);
        }
    }
}

// Run if called directly
if (require.main === module) {
    const platform = process.argv[2] || 'all';
    const builder = new SimpleBuilder();
    builder.run(platform);
}

module.exports = SimpleBuilder;
