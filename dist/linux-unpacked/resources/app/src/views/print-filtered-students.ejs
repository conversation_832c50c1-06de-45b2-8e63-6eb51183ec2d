<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Filtered Students</title>
    <link rel="stylesheet" href="/styles-enhanced.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @media print {
            body { 
                background: white !important; 
                font-size: 10px;
                line-height: 1.2;
                margin: 0 !important;
                padding: 15px !important;
            }
            .no-print { display: none !important; }
            
            .report-header {
                text-align: center;
                border-bottom: 2px solid #000;
                padding-bottom: 15px;
                margin-bottom: 20px;
            }
            
            .report-header h1 {
                font-size: 18px !important;
                margin: 0 0 5px 0 !important;
                font-weight: bold;
            }
            
            .report-header p {
                margin: 2px 0 !important;
                font-size: 10px;
            }
            
            .filter-summary {
                background: #f9f9f9;
                border: 1px solid #ccc;
                padding: 10px;
                margin-bottom: 20px;
                font-size: 9px;
            }
            
            .filter-summary h3 {
                margin: 0 0 8px 0;
                font-size: 11px;
            }
            
            .filter-item {
                display: inline-block;
                margin-right: 15px;
                margin-bottom: 5px;
            }
            
            .students-table {
                width: 100%;
                border-collapse: collapse;
                font-size: 8px;
            }
            
            .students-table th,
            .students-table td {
                border: 1px solid #000;
                padding: 4px;
                text-align: left;
                vertical-align: top;
            }
            
            .students-table th {
                background: #000;
                color: white;
                font-weight: bold;
                text-transform: uppercase;
                font-size: 7px;
            }
            
            .students-table tr:nth-child(even) {
                background: #f9f9f9;
            }
            
            .student-photo-small {
                width: 25px;
                height: 30px;
                object-fit: cover;
                border: 1px solid #ccc;
            }
            
            .status-badge {
                padding: 1px 4px;
                border-radius: 2px;
                font-size: 7px;
                font-weight: bold;
                text-transform: uppercase;
            }
            
            .status-active { background: #d4edda; color: #155724; }
            .status-pending { background: #fff3cd; color: #856404; }
            .status-graduated { background: #d1ecf1; color: #0c5460; }
            .status-transferred { background: #f8d7da; color: #721c24; }
            
            .summary-stats {
                margin-top: 20px;
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 10px;
                font-size: 9px;
            }
            
            .stat-box {
                border: 1px solid #000;
                padding: 8px;
                text-align: center;
                background: #f9f9f9;
            }
            
            .stat-number {
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 3px;
            }
            
            .stat-label {
                font-size: 8px;
                text-transform: uppercase;
            }
            
            .page-break {
                page-break-before: always;
            }
        }
    </style>
</head>
<body>
    <!-- Print Actions (No Print) -->
    <div class="print-actions no-print">
        <button onclick="window.print()" class="btn">
            <i class="fas fa-print"></i> Print Report
        </button>
        <button onclick="window.history.back()" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back
        </button>
    </div>
    
    <div class="report-header">
        <h1>SHEGE SRS</h1>
        <p>Filtered Students Report</p>
        <p>Generated on: <%= new Date().toLocaleDateString() %> at <%= new Date().toLocaleTimeString() %></p>
        <p>Total Students: <%= students.length %></p>
    </div>
    
    <% if (filters && Object.keys(filters).length > 0) { %>
        <div class="filter-summary">
            <h3>Applied Filters:</h3>
            <% if (filters.quarter) { %>
                <span class="filter-item"><strong>Quarter:</strong> <%= filters.quarter %></span>
            <% } %>
            <% if (filters.grade) { %>
                <span class="filter-item"><strong>Grade:</strong> <%= filters.grade %></span>
            <% } %>
            <% if (filters.section) { %>
                <span class="filter-item"><strong>Section:</strong> <%= filters.section %></span>
            <% } %>
            <% if (filters.sex) { %>
                <span class="filter-item"><strong>Gender:</strong> <%= filters.sex %></span>
            <% } %>
            <% if (filters.status) { %>
                <span class="filter-item"><strong>Status:</strong> <%= filters.status %></span>
            <% } %>
            <% if (filters.nameRange) { %>
                <span class="filter-item"><strong>Name Range:</strong> <%= filters.nameRange %></span>
            <% } %>
        </div>
    <% } %>
    
    <table class="students-table">
        <thead>
            <tr>
                <th>Photo</th>
                <th>Student ID</th>
                <th>Full Name</th>
                <th>Grade</th>
                <th>Section</th>
                <th>Quarter</th>
                <th>Gender</th>
                <th>DOB</th>
                <th>Status</th>
                <th>Registration Date</th>
            </tr>
        </thead>
        <tbody>
            <% students.forEach((student, index) => { %>
                <tr>
                    <td>
                        <% if (student.studentPic) { %>
                            <img src="/uploads/<%= student.studentPic %>" alt="<%= student.fullName %>" class="student-photo-small">
                        <% } else { %>
                            <div style="width: 25px; height: 30px; background: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; font-size: 6px;">
                                NO
                            </div>
                        <% } %>
                    </td>
                    <td><%= student.studentId || 'N/A' %></td>
                    <td><%= student.fullName %></td>
                    <td><%= student.registeredGrade || 'N/A' %></td>
                    <td><%= student.section || 'N/A' %></td>
                    <td><%= student.quarter || 'N/A' %></td>
                    <td><%= student.sex || 'N/A' %></td>
                    <td><%= student.dob ? new Date(student.dob).toLocaleDateString() : 'N/A' %></td>
                    <td>
                        <% 
                            const status = student.status || 'active';
                            const statusClass = 'status-' + status;
                        %>
                        <span class="status-badge <%= statusClass %>"><%= status %></span>
                    </td>
                    <td><%= student.registrationDate ? new Date(student.registrationDate).toLocaleDateString() : 'N/A' %></td>
                </tr>
            <% }) %>
        </tbody>
    </table>
    
    <div class="summary-stats">
        <div class="stat-box">
            <div class="stat-number"><%= students.length %></div>
            <div class="stat-label">Total Students</div>
        </div>
        <div class="stat-box">
            <div class="stat-number"><%= students.filter(s => s.sex === 'Male').length %></div>
            <div class="stat-label">Male</div>
        </div>
        <div class="stat-box">
            <div class="stat-number"><%= students.filter(s => s.sex === 'Female').length %></div>
            <div class="stat-label">Female</div>
        </div>
        <div class="stat-box">
            <div class="stat-number"><%= students.filter(s => (s.status || 'active') === 'active').length %></div>
            <div class="stat-label">Active</div>
        </div>
    </div>
    
    <% if (students.length > 20) { %>
        <div class="page-break">
            <div class="report-header">
                <h1>SHEGE SRS</h1>
                <p>Detailed Student Information</p>
                <p>Page 2 - Detailed View</p>
            </div>
            
            <% students.forEach((student, index) => { %>
                <% if (index % 3 === 0 && index > 0) { %>
                    <div style="page-break-before: always;"></div>
                <% } %>
                
                <div style="border: 1px solid #000; margin-bottom: 15px; padding: 10px; background: #f9f9f9;">
                    <div style="display: flex; align-items: flex-start; margin-bottom: 10px;">
                        <% if (student.studentPic) { %>
                            <img src="/uploads/<%= student.studentPic %>" alt="<%= student.fullName %>" style="width: 40px; height: 50px; object-fit: cover; border: 1px solid #000; margin-right: 10px;">
                        <% } %>
                        <div>
                            <div style="font-weight: bold; font-size: 11px; margin-bottom: 3px;"><%= student.fullName %></div>
                            <div style="font-size: 8px;">
                                <strong>ID:</strong> <%= student.studentId || 'N/A' %> | 
                                <strong>Grade:</strong> <%= student.registeredGrade || 'N/A' %> | 
                                <strong>Section:</strong> <%= student.section || 'N/A' %> | 
                                <strong>Quarter:</strong> <%= student.quarter || 'N/A' %>
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 7px;">
                        <div><strong>DOB:</strong> <%= student.dob ? new Date(student.dob).toLocaleDateString() : 'N/A' %></div>
                        <div><strong>Age:</strong> <%= student.age || 'N/A' %></div>
                        <div><strong>Blood Type:</strong> <%= student.bloodType || 'N/A' %></div>
                        <div><strong>Mother Tongue:</strong> <%= student.motherTongue || 'N/A' %></div>
                        <% if (student.father) { %>
                            <div><strong>Father:</strong> <%= student.father.fullName || 'N/A' %></div>
                        <% } %>
                        <% if (student.mother) { %>
                            <div><strong>Mother:</strong> <%= student.mother.fullName || 'N/A' %></div>
                        <% } %>
                    </div>
                </div>
            <% }) %>
        </div>
    <% } %>
</body>
</html>
