const Student = require('../models/student');
const User = require('../models/user');
const bcrypt = require('bcryptjs');

exports.register = async (req, res) => {
    // Registration logic handled in routes for now
};

exports.list = async (req, res) => {
    // Listing logic handled in routes for now
};

exports.edit = async (req, res) => {
    // Edit logic handled in routes for now
};

exports.update = async (req, res) => {
    // Update logic handled in routes for now
};

exports.ensureAdmin = (req, res, next) => {
    if (req.session && req.session.user && req.session.username) {
        return next();
    }
    req.flash('error_msg', 'Please log in to view this page.');
    res.redirect('/login');
};

exports.loginForm = (req, res) => {
    res.render('auth/login');
};

exports.login = async (req, res) => {
    try {
        const { username, password } = req.body;
        const user = await User.findOne({ username });
        if (user && await bcrypt.compare(password, user.password)) {
            // Set session variables that the views expect
            req.session.user = user;
            req.session.username = user.username;
            req.session.isAdmin = true; // Since we only have admin users for now
            req.session.userId = user._id;

            req.flash('success_msg', 'Login successful!');
            return res.redirect('/dashboard');
        }
        req.flash('error_msg', 'Invalid username or password');
        res.redirect('/login');
    } catch (err) {
        console.error('Login error:', err);
        req.flash('error_msg', 'Login failed. Please try again.');
        res.redirect('/login');
    }
};

exports.logout = (req, res) => {
    req.session.destroy(() => {
        res.redirect('/login');
    });
};