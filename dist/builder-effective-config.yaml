directories:
  output: dist
  buildResources: build
appId: com.shegesrs.desktop
productName: SHEGE SRS
asar: false
files:
  - filter:
      - src/**/*
      - electron-main.js
      - electron-preload.js
      - package.json
      - .env
      - .env.example
      - scripts/**/*
      - node_modules/**/*
      - '!node_modules/.cache/**/*'
      - '!node_modules/.bin/**/*'
      - '!**/*.md'
      - '!docs/**/*'
extraResources:
  - from: assets
    to: assets
    filter:
      - '**/*'
win:
  target: portable
  requestedExecutionLevel: asInvoker
  artifactName: ${productName}-${version}-${arch}.${ext}
  icon: 'null'
linux:
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
    - target: rpm
      arch:
        - x64
    - target: tar.gz
      arch:
        - x64
  category: Education
  artifactName: ${productName}-${version}-${arch}.${ext}
  desktop:
    Name: SHEGE SRS
    Comment: Student Registration System
    Categories: Education;Office;
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: SHEGE SRS
publish: null
electronVersion: 28.3.3
