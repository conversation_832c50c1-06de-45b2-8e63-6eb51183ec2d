directories:
  output: dist
  buildResources: build
appId: com.shegesrs.desktop
productName: SHEGE SRS
asar: false
files:
  - filter:
      - src/**/*
      - electron-main.js
      - electron-preload.js
      - package.json
      - .env
      - .env.example
      - scripts/**/*
      - node_modules/**/*
      - '!node_modules/.cache/**/*'
      - '!node_modules/.bin/**/*'
      - '!**/*.md'
      - '!docs/**/*'
extraResources:
  - from: assets
    to: assets
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
    - target: portable
      arch:
        - x64
        - ia32
    - target: zip
      arch:
        - x64
        - ia32
  requestedExecutionLevel: asInvoker
  artifactName: ${productName}-${version}-${arch}.${ext}
linux:
  target: AppImage
  category: Education
  artifactName: ${productName}-${version}-${arch}.${ext}
  desktop:
    Name: SHEGE SRS
    Comment: Student Registration System
    Categories: Education;Office;
  icon: 'null'
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: SHEGE SRS
publish: null
electronVersion: 28.3.3
