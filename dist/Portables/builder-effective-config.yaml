directories:
  output: dist
  buildResources: build
appId: com.shegesrs.desktop
productName: SHEGE SRS
files:
  - filter:
      - src/**/*
      - electron-main.js
      - electron-preload.js
      - package.json
      - node_modules/**/*
      - '!node_modules/.cache/**/*'
extraResources:
  - from: assets
    to: assets
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
    - target: portable
      arch:
        - x64
        - ia32
    - target: zip
      arch:
        - x64
        - ia32
  icon: assets/icon.ico
  requestedExecutionLevel: asInvoker
  artifactName: ${productName}-${version}-${arch}.${ext}
linux:
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
    - target: rpm
      arch:
        - x64
    - target: tar.gz
      arch:
        - x64
  icon: assets/icon.png
  category: Education
  artifactName: ${productName}-${version}-${arch}.${ext}
  desktop:
    Name: SHEGE SRS
    Comment: Student Registration System
    Categories: Education;Office;
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: SHEGE SRS
publish: null
electronVersion: 28.3.3
