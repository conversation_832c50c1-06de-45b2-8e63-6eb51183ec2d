const path = require('path');
const TerserPlugin = require('terser-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = {
  target: 'node',
  mode: 'production',
  entry: './src/app.js',
  output: {
    path: path.resolve(__dirname, 'build'),
    filename: 'app.bundle.js',
    clean: true
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
          },
          mangle: {
            reserved: ['require', 'module', 'exports']
          },
          format: {
            comments: false,
          },
        },
        extractComments: false,
      }),
    ],
  },
  plugins: [
    new CopyWebpackPlugin({
      patterns: [
        {
          from: 'src/views',
          to: 'views',
        },
        {
          from: 'src/public',
          to: 'public',
          noErrorOnMissing: true
        },
        {
          from: 'package.json',
          to: 'package.json',
          transform(content) {
            const pkg = JSON.parse(content);
            return JSON.stringify({
              name: pkg.name,
              version: pkg.version,
              description: pkg.description,
              main: 'app.bundle.js',
              scripts: {
                start: 'node app.bundle.js'
              },
              dependencies: {
                // Only include runtime dependencies
                express: pkg.dependencies.express,
                mongoose: pkg.dependencies.mongoose,
                ejs: pkg.dependencies.ejs,
                multer: pkg.dependencies.multer,
                bcrypt: pkg.dependencies.bcrypt,
                'express-session': pkg.dependencies['express-session'],
                'connect-flash': pkg.dependencies['connect-flash'],
                'method-override': pkg.dependencies['method-override'],
                dotenv: pkg.dependencies.dotenv
              }
            }, null, 2);
          }
        }
      ],
    }),
  ],
  externals: {
    // Keep these as external dependencies
    express: 'commonjs express',
    mongoose: 'commonjs mongoose',
    ejs: 'commonjs ejs',
    multer: 'commonjs multer',
    bcrypt: 'commonjs bcrypt',
    'express-session': 'commonjs express-session',
    'connect-flash': 'commonjs connect-flash',
    'method-override': 'commonjs method-override',
    dotenv: 'commonjs dotenv'
  },
  resolve: {
    extensions: ['.js', '.json'],
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', {
                targets: {
                  node: '16'
                }
              }]
            ]
          }
        }
      }
    ]
  }
};
