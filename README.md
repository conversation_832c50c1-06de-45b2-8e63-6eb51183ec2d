# Student Registration System

This is a simple student registration system built with Express.js, EJS, and MongoDB. The application allows users to register students, view student data, edit student information, and filter the data.

## Features

- Register new students
- View a list of registered students
- Edit existing student information
- Filter students based on specific criteria

## Technologies Used

- Node.js
- Express.js
- EJS (Embedded JavaScript)
- MongoDB
- Mongoose

## Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   ```

2. Navigate to the project directory:
   ```
   cd student-registration-app
   ```

3. Install the dependencies:
   ```
   npm install
   ```

4. Set up your MongoDB database and update the connection string in `src/app.js`.

## Usage

1. Start the application:
   ```
   npm start
   ```

2. Open your browser and go to `http://localhost:3000` to access the application.

## Routes

- `GET /students` - View all registered students
- `GET /students/register` - Register a new student
- `POST /students/register` - Submit the registration form
- `GET /students/edit/:id` - Edit student information
- `POST /students/edit/:id` - Update student information
- `GET /students/filter` - Filter students based on criteria

## Contributing

Feel free to submit issues or pull requests for improvements or bug fixes.

## License

This project is licensed under the MIT License.