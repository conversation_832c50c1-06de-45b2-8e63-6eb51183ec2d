// Debug version of electron-main.js with extensive logging
const { app, BrowserWindow, Menu, shell, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

// Create debug log function
function debugLog(message, data = null) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    if (data) {
        console.log(JSON.stringify(data, null, 2));
    }
}

// Enhanced error handling
process.on('uncaughtException', (error) => {
    debugLog('UNCAUGHT EXCEPTION:', error);
    console.error('Stack trace:', error.stack);
    dialog.showErrorBox('Critical Error', `Uncaught Exception:\n\n${error.message}\n\nStack:\n${error.stack}`);
});

process.on('unhandledRejection', (reason, promise) => {
    debugLog('UNHANDLED REJECTION:', reason);
    dialog.showErrorBox('Promise Rejection', `Unhandled Promise Rejection:\n\n${reason}`);
});

// App info
debugLog('Starting SHEGE SRS Debug Mode');
debugLog('Electron version:', process.versions.electron);
debugLog('Node version:', process.versions.node);
debugLog('Platform:', process.platform);
debugLog('Architecture:', process.arch);
debugLog('App path:', app.getAppPath());
debugLog('User data path:', app.getPath('userData'));
debugLog('Current working directory:', process.cwd());
debugLog('Resource path:', process.resourcesPath);
debugLog('Executable path:', process.execPath);

let mainWindow;

function createDebugWindow() {
    debugLog('Creating debug window...');
    
    try {
        // Simple window without preload for debugging
        mainWindow = new BrowserWindow({
            width: 1200,
            height: 800,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false
            },
            show: true
        });

        debugLog('Window created successfully');

        // Load a simple debug page
        const debugHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>SHEGE SRS Debug</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .debug-info { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 10px 0; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 4px; 
            overflow-x: auto; 
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔍 SHEGE SRS Debug Information</h1>
    
    <div class="debug-info">
        <h2>System Information</h2>
        <pre id="system-info">Loading...</pre>
    </div>
    
    <div class="debug-info">
        <h2>File System Check</h2>
        <pre id="file-check">Loading...</pre>
    </div>
    
    <div class="debug-info">
        <h2>Actions</h2>
        <button onclick="checkMongoDB()">Test MongoDB Connection</button>
        <button onclick="checkServer()">Test Server Start</button>
        <button onclick="showLogs()">Show Logs</button>
        <button onclick="location.reload()">Refresh</button>
    </div>
    
    <div class="debug-info">
        <h2>Results</h2>
        <pre id="results"></pre>
    </div>

    <script>
        // Display system information
        document.getElementById('system-info').textContent = JSON.stringify({
            platform: navigator.platform,
            userAgent: navigator.userAgent,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            location: window.location.href
        }, null, 2);

        // Check file system
        document.getElementById('file-check').textContent = 'File system check would require Node.js access';

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            results.innerHTML += \`<div class="\${className}">[\${timestamp}] \${message}</div>\`;
            results.scrollTop = results.scrollHeight;
        }

        function checkMongoDB() {
            log('Testing MongoDB connection...', 'info');
            fetch('http://localhost:27017')
                .then(() => log('MongoDB appears to be running', 'success'))
                .catch(err => log('MongoDB connection failed: ' + err.message, 'error'));
        }

        function checkServer() {
            log('Testing SHEGE SRS server...', 'info');
            fetch('http://localhost:3000')
                .then(response => {
                    if (response.ok) {
                        log('SHEGE SRS server is running!', 'success');
                        log('Redirecting to application...', 'info');
                        setTimeout(() => {
                            window.location.href = 'http://localhost:3000';
                        }, 2000);
                    } else {
                        log('Server responded with status: ' + response.status, 'warning');
                    }
                })
                .catch(err => log('Server connection failed: ' + err.message, 'error'));
        }

        function showLogs() {
            log('Console logs are visible in the developer tools', 'info');
            log('Press F12 or Ctrl+Shift+I to open developer tools', 'info');
        }

        // Auto-check server every 5 seconds
        setInterval(() => {
            fetch('http://localhost:3000')
                .then(response => {
                    if (response.ok) {
                        log('✅ Server is running - you can now use the application', 'success');
                    }
                })
                .catch(() => {
                    // Silent fail for auto-check
                });
        }, 5000);

        log('Debug page loaded successfully', 'success');
        log('Checking for SHEGE SRS server...', 'info');
        checkServer();
    </script>
</body>
</html>`;

        mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(debugHtml)}`);

        // Open DevTools automatically
        mainWindow.webContents.openDevTools();

        debugLog('Debug page loaded');

        mainWindow.on('closed', () => {
            debugLog('Window closed');
            mainWindow = null;
        });

    } catch (error) {
        debugLog('Error creating window:', error);
        dialog.showErrorBox('Window Creation Failed', error.message);
    }
}

app.whenReady().then(() => {
    debugLog('App ready, creating window...');
    createDebugWindow();
});

app.on('window-all-closed', () => {
    debugLog('All windows closed');
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    debugLog('App activated');
    if (BrowserWindow.getAllWindows().length === 0) {
        createDebugWindow();
    }
});

debugLog('Debug script loaded successfully');
