#!/bin/bash

echo "🧪 Testing SHEGE SRS AppImage"
echo "=============================="

# Check if AppImage exists
if [ ! -f dist/SHEGE*.AppImage ]; then
    echo "❌ AppImage not found in dist/ directory"
    echo "Run 'npm run build-portable-linux' first"
    exit 1
fi

# Make executable
chmod +x dist/SHEGE*.AppImage
echo "✅ Made AppImage executable"

# Check MongoDB status
echo "🔍 Checking MongoDB status..."
if systemctl is-active --quiet mongod; then
    echo "✅ MongoDB is running"
elif pgrep mongod > /dev/null; then
    echo "✅ MongoDB process found"
else
    echo "⚠️  MongoDB may not be running"
    echo "   Start with: sudo systemctl start mongod"
    echo "   Or install with: sudo apt install mongodb"
fi

# Check if port 3000 is available
if netstat -tlnp 2>/dev/null | grep -q ":3000 "; then
    echo "⚠️  Port 3000 is already in use"
    echo "   Stop other services using port 3000"
else
    echo "✅ Port 3000 is available"
fi

echo ""
echo "🚀 Starting SHEGE SRS AppImage..."
echo "   This will open the application window"
echo "   Wait for the server to start (may take 30-60 seconds)"
echo "   The application should open at http://localhost:3000"
echo ""
echo "Press Ctrl+C to stop the test"
echo ""

# Start the AppImage
cd dist
./SHEGE*.AppImage
