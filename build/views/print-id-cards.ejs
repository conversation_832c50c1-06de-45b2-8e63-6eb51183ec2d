<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print ID Cards</title>
    <link rel="stylesheet" href="/styles.css">
    <style>
        @media print {
            body { 
                background: white !important; 
                margin: 0 !important;
                padding: 0 !important;
            }
            .no-print { display: none !important; }
            .id-cards-container {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
                padding: 20px;
            }
            .id-card-print {
                width: 350px;
                height: 220px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                border-radius: 15px;
                padding: 20px;
                color: white;
                box-shadow: none;
                position: relative;
                overflow: hidden;
                border: 2px solid #333;
                page-break-inside: avoid;
                margin-bottom: 20px;
            }
            .id-card-print::before {
                content: '';
                position: absolute;
                top: -50%;
                right: -50%;
                width: 100%;
                height: 100%;
                background: rgba(255,255,255,0.1);
                border-radius: 50%;
            }
            .school-name-print {
                text-align: center;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
            .card-content-print {
                display: flex;
                gap: 15px;
                position: relative;
                z-index: 1;
            }
            .student-photo-print {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                border: 3px solid white;
                object-fit: cover;
                background: rgba(255,255,255,0.2);
            }
            .student-info-print {
                flex: 1;
            }
            .student-name-print {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 5px;
            }
            .student-details-print {
                font-size: 11px;
                line-height: 1.4;
            }
            .student-id-print {
                background: rgba(255,255,255,0.2);
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 10px;
                margin-top: 8px;
                text-align: center;
            }
        }
        
        .print-actions {
            text-align: center;
            margin: 2rem 0;
            gap: 1rem;
            display: flex;
            justify-content: center;
        }
        
        .id-cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .id-card-print {
            width: 400px;
            height: 250px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            padding: 25px;
            color: white;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(255,255,255,0.2);
        }
        
        .id-card-print::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }
        
        .school-name-print {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .card-content-print {
            display: flex;
            gap: 15px;
            position: relative;
            z-index: 1;
        }
        
        .student-photo-print {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            border: 4px solid white;
            object-fit: cover;
            background: rgba(255,255,255,0.2);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .student-info-print {
            flex: 1;
        }
        
        .student-name-print {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .student-details-print {
            font-size: 12px;
            line-height: 1.4;
        }
        
        .student-id-print {
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="print-actions no-print">
        <button onclick="window.print()" class="btn">
            <i class="fas fa-print"></i> Print All ID Cards
        </button>
        <button onclick="window.history.back()" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back
        </button>
    </div>
    
    <div class="no-print" style="text-align: center; margin: 2rem 0; color: #666;">
        <h2>ID Cards for Printing</h2>
        <p>Total Cards: <%= students.length %></p>
        <p>Click "Print All ID Cards" to print these cards</p>
    </div>
    
    <div class="id-cards-container">
        <% students.forEach(student => { %>
            <div class="id-card-print">
                <div class="school-name-print">Student Registration System</div>
                <div class="card-content-print">
                    <% if (student.studentPic) { %>
                        <img src="/uploads/<%= student.studentPic %>" alt="Student Photo" class="student-photo-print">
                    <% } else { %>
                        <div class="student-photo-print"></div>
                    <% } %>
                    <div class="student-info-print">
                        <div class="student-name-print"><%= student.fullName %></div>
                        <div class="student-details-print">
                            <div><strong>Grade:</strong> <%= student.registeredGrade || 'N/A' %></div>
                            <div><strong>Section:</strong> <%= student.section || 'N/A' %></div>
                            <div><strong>Sex:</strong> <%= student.sex || 'N/A' %></div>
                            <% if (student.dob) { %>
                                <div><strong>DOB:</strong> <%= new Date(student.dob).toLocaleDateString() %></div>
                            <% } %>
                            <% if (student.quarter) { %>
                                <div><strong>Quarter:</strong> <%= student.quarter %></div>
                            <% } %>
                        </div>
                        <div class="student-id-print">
                            ID: <%= student.studentId || 'STU' + student._id.toString().slice(-6).toUpperCase() %>
                        </div>
                    </div>
                </div>
            </div>
        <% }) %>
    </div>
    
    <% if (students.length === 0) { %>
        <div style="text-align: center; padding: 4rem; color: #666;">
            <i class="fas fa-id-card" style="font-size: 4rem; margin-bottom: 2rem; display: block;"></i>
            <h3>No Students Found</h3>
            <p>No students match the selected criteria for ID card printing.</p>
            <a href="/students" class="btn">
                <i class="fas fa-arrow-left"></i> Back to Students
            </a>
        </div>
    <% } %>
</body>
</html>
