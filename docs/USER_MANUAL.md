# SHEGE SRS User Manual

## Table of Contents
1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Dashboard Overview](#dashboard-overview)
4. [Student Registration](#student-registration)
5. [Student Management](#student-management)
6. [Reports and Exports](#reports-and-exports)
7. [ID Card Generation](#id-card-generation)
8. [Troubleshooting](#troubleshooting)

## Introduction

SHEGE SRS (Student Registration System) is a comprehensive desktop application designed to manage student information, registration, and administrative tasks for educational institutions.

### Key Features
- **Student Registration**: Complete student enrollment with photos and documents
- **Data Management**: Organize students by quarters, grades, and sections
- **Export Capabilities**: CSV exports with advanced filtering options
- **ID Card Generation**: Professional student ID cards with guardian information
- **Document Tracking**: Monitor document submission status
- **Print Functions**: Various print formats for reports and ID cards

### System Requirements
- **Operating System**: Windows 7+ or Linux (Ubuntu 16.04+)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 1GB free space
- **Database**: MongoDB (included in installation)

## Getting Started

### First Time Setup

1. **Launch Application**
   - Windows: Double-click desktop shortcut or find in Start Menu
   - Linux: Launch from Applications menu or run from terminal

2. **Initial Login**
   - Default credentials are provided during installation
   - Change default password immediately after first login

3. **System Configuration**
   - Navigate to Settings (if available) to configure:
     - School information
     - Academic year settings
     - System preferences

### Navigation Basics

#### Main Menu Bar
- **File**: New Student, Dashboard, Students List, Exit
- **Edit**: Standard editing functions (Undo, Redo, Copy, Paste)
- **View**: Refresh, Zoom controls, Toggle fullscreen
- **Window**: Window management functions
- **Help**: About information and system details

#### Keyboard Shortcuts
- `Ctrl+N`: New Student Registration
- `Ctrl+D`: Open Dashboard
- `Ctrl+L`: Open Students List
- `Ctrl+Q`: Quit Application
- `F5`: Refresh current page
- `F11`: Toggle fullscreen mode

## Dashboard Overview

The Dashboard provides a comprehensive overview of your student data and system status.

### Dashboard Components

#### Quick Statistics
- **Total Students**: Overall student count
- **Active Students**: Currently enrolled students
- **Pending Students**: Students with incomplete registration
- **Recent Registrations**: Latest student additions

#### Quarter Distribution
Visual representation of student distribution across academic quarters:
- **Quarter 1**: Fall semester students
- **Quarter 2**: Winter semester students  
- **Quarter 3**: Spring semester students
- **Quarter 4**: Summer semester students

Each quarter shows:
- Student count
- Percentage of total
- Visual progress bar
- Seasonal icon representation

#### Recent Activity
- Latest student registrations
- Recent document uploads
- System notifications
- Pending tasks

### Dashboard Actions
- **Register New Student**: Quick access to registration form
- **View All Students**: Navigate to complete student list
- **Generate Reports**: Access reporting functions
- **Export Data**: Quick export options

## Student Registration

### Registration Process

#### Step 1: Basic Information
1. **Personal Details**
   - Full Name (required)
   - Sex/Gender selection
   - Date of Birth
   - Age (auto-calculated)
   - Blood Type (dropdown selection)

2. **Academic Information**
   - Former Grade (dropdown)
   - Registered Grade (required, dropdown)
   - Section (dropdown: A, B, C, D, E, F)
   - Quarter assignment
   - Mother Tongue (dropdown with common languages)

3. **Physical Information**
   - Weight and Height
   - Hand Use (Left/Right/Ambidextrous)
   - Medical conditions checkboxes

#### Step 2: Address Information
- City, Sub-City, Tabia
- Ketena, Block
- Nationality
- Complete address details

#### Step 3: Parent Information

**Father's Information:**
- Full Name, Phone Number
- Nationality, Education Level
- Occupation, Workplace
- Photo upload

**Mother's Information:**
- Full Name, Phone Number
- Nationality, Education Level
- Occupation, Workplace
- Photo upload

#### Step 4: Guardian Information
- Guardian Name
- Relationship (dropdown: Father, Mother, Grandfather, etc.)
- Phone Number
- Emergency contact details

#### Step 5: Documents
**Required Documents:**
- Birth Certificate
- Academic Transcript
- Parents' ID Documents
- Buy Book (checkbox)

**Photo Uploads:**
- Student Photo
- Father's Photo
- Mother's Photo

### Photo Upload Guidelines
- **Supported Formats**: JPEG, PNG, GIF, WebP
- **Maximum Size**: 5MB per file
- **Recommended Size**: 300x400 pixels for student photos
- **Quality**: Clear, well-lit photos preferred

### Form Validation
- Required fields marked with asterisk (*)
- Real-time validation feedback
- Error messages for invalid data
- Confirmation before submission

## Student Management

### Students List View

#### Table Columns
- **Checkbox**: Select students for bulk operations
- **Photo**: Student thumbnail image
- **Name**: Full student name
- **ID**: Student identification number
- **Grade**: Current grade level
- **Section**: Class section
- **Quarter**: Academic quarter
- **Status**: Active, Pending, Graduated, Transferred
- **Actions**: View, Edit, Delete, ID Card

#### Filtering Options
- **Search**: Text search across all fields
- **Grade Filter**: Filter by specific grade levels
- **Section Filter**: Filter by class sections
- **Quarter Filter**: Filter by academic quarters
- **Status Filter**: Filter by student status

#### Sorting
- Click column headers to sort
- Ascending/descending order toggle
- Multi-column sorting support

### Individual Student Actions

#### View Student
- Complete student profile
- All personal and academic information
- Photo gallery
- Document status
- Guardian information

#### Edit Student
- Modify any student information
- Update photos and documents
- Change academic assignments
- Update contact information

#### Delete Student
- Permanent removal from system
- Confirmation dialog required
- Cannot be undone

### Bulk Operations
Select multiple students using checkboxes for:
- Bulk export to CSV
- Bulk ID card printing
- Bulk status updates
- Bulk document reports

## Reports and Exports

### Export Options

#### CSV Export Types
1. **Export Current View**
   - Exports students currently visible
   - Respects applied filters
   - Quick export option

2. **Export Selected Students**
   - Exports only checked students
   - Useful for specific groups
   - Maintains selection

3. **Export by Filters**
   - Advanced filtering modal
   - Multiple filter criteria
   - Custom export options

#### Advanced Filter Export

**Filter Categories:**
- **Quarter/Term**: Select specific quarters
- **Grade Level**: Choose grade ranges
- **Section**: Select class sections
- **Gender**: Male/Female filtering
- **Status**: Active, Pending, etc.
- **Name Range**: Alphabetical or custom ranges

**Export Formats:**
- **CSV Spreadsheet**: Excel-compatible format
- **PDF Report**: Formatted document (future feature)
- **Print Preview**: Browser print format

### Print Options

#### Document Status Reports
- Individual student document status
- Professional formatted reports
- Shows document completion status
- Includes student photo and basic info

#### Student Information Reports
- Complete student profiles
- Two-page format (front and back)
- Includes all registration information
- Professional layout for official use

#### Filtered Student Reports
- Custom student lists based on filters
- Summary statistics included
- Tabular format with all details
- Suitable for administrative use

### Report Customization
- Select specific data fields
- Choose date ranges
- Apply multiple filters
- Custom sorting options

## ID Card Generation

### ID Card Features

#### Front Page
- **Student Photo**: Professional display
- **Student Name**: Prominent placement
- **Basic Information**:
  - Date of Birth
  - Entry Date
  - Class and Section
  - Student ID number
- **Address Information**: Complete address details
- **School Branding**: SHEGE SRS logo and colors

#### Back Page
- **Guardian Information**:
  - Guardian name and relationship
  - Guardian phone number
  - Father's contact (if available)
  - Mother's contact (if available)
- **Emergency Instructions**: Clear emergency procedures
- **School Contact**: Emergency contact information
- **Validity Information**: Academic year validity

### ID Card Generation Process

1. **Select Students**
   - Individual student selection
   - Bulk selection from student list
   - Filter-based selection

2. **Generate Cards**
   - Click "Print ID Cards" option
   - System generates front and back pages
   - Professional layout applied

3. **Print Options**
   - Print preview available
   - Standard card size (400x280px)
   - Optimized for card printers
   - High-quality output

### ID Card Best Practices
- Ensure student photos are clear and recent
- Verify guardian information accuracy
- Print on quality card stock
- Consider lamination for durability
- Regular updates for academic year changes

## Troubleshooting

### Common Issues

#### Login Problems
**Issue**: Cannot log in to system
**Solutions**:
- Verify username and password
- Check caps lock status
- Contact administrator for password reset
- Ensure application is fully loaded

#### Photo Upload Issues
**Issue**: Photos won't upload or display
**Solutions**:
- Check file format (JPEG, PNG, GIF, WebP only)
- Verify file size (under 5MB)
- Ensure stable internet connection
- Try different photo file
- Clear browser cache and retry

#### Export/Print Problems
**Issue**: Exports fail or prints are blank
**Solutions**:
- Check printer connection and status
- Verify export permissions
- Try smaller data sets
- Update browser if using web version
- Check available disk space

#### Performance Issues
**Issue**: Application runs slowly
**Solutions**:
- Close unnecessary applications
- Restart the application
- Check available RAM and storage
- Update to latest version
- Contact IT support

### Error Messages

#### "Validation Failed"
- Check required fields are completed
- Verify data format (dates, numbers)
- Ensure dropdown selections are made
- Review error messages for specific fields

#### "File Upload Failed"
- Check file size and format
- Verify network connection
- Try uploading one file at a time
- Restart application if persistent

#### "Database Connection Error"
- Contact system administrator
- Check network connectivity
- Verify database server status
- Restart application

### Getting Help

#### Self-Help Resources
1. Check this user manual
2. Review error messages carefully
3. Try basic troubleshooting steps
4. Restart the application

#### Contact Support
- **IT Department**: For technical issues
- **System Administrator**: For access problems
- **Training Team**: For usage questions
- **Documentation**: Refer to additional guides

#### Reporting Issues
When reporting problems, include:
- Exact error message
- Steps that led to the issue
- Screenshots if applicable
- System information (Windows/Linux version)
- Time and date of occurrence

### Maintenance Tips

#### Regular Tasks
- **Weekly**: Backup important data
- **Monthly**: Review and clean old records
- **Quarterly**: Update student statuses
- **Annually**: Archive graduated students

#### Data Management
- Keep student photos updated
- Verify contact information regularly
- Maintain document status accuracy
- Regular system health checks

#### Security Practices
- Change passwords regularly
- Log out when finished
- Don't share login credentials
- Report suspicious activity

---

*For additional support, contact your system administrator or refer to the Administrator's Guide.*
