const express = require('express');
const router = express.Router();
const Student = require('../models/student');
const multer = require('multer');
const path = require('path');
const studentController = require('../controllers/studentController');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const fs = require('fs');

// Multer setup for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, path.join(__dirname, '../public/uploads'));
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + '-' + file.originalname);
    }
});
const upload = multer({ storage: storage });

// Public routes (no authentication required)
router.get('/login', studentController.loginForm);
router.post('/login', studentController.login);
router.get('/logout', studentController.logout);

// Registration form (protected)
router.get('/register', studentController.ensureAdmin, (req, res) => {
    res.render('register');
});

// Register student (protected)
router.post('/register', studentController.ensureAdmin, upload.fields([
    { name: 'studentPic', maxCount: 1 },
    { name: 'fatherPic', maxCount: 1 },
    { name: 'motherPic', maxCount: 1 },
    { name: 'birthCertificate', maxCount: 1 },
    { name: 'transcript', maxCount: 1 },
    { name: 'parentsID', maxCount: 1 }
]), async (req, res) => {
    try {
        const { body, files } = req;

        // Input validation and sanitization
        const sanitizeString = (str) => {
            if (!str || typeof str !== 'string') return '';
            return str.trim().replace(/[<>]/g, ''); // Basic XSS protection
        };

        const parseNumber = (value) => {
            if (!value) return undefined;
            const num = parseFloat(value);
            return isNaN(num) ? undefined : num;
        };

        const parseDate = (dateStr) => {
            if (!dateStr) return undefined;
            const date = new Date(dateStr);
            return isNaN(date.getTime()) ? undefined : date;
        };

        const parseBoolean = (value) => {
            if (typeof value === 'boolean') return value;
            if (typeof value === 'string') {
                return value.toLowerCase() === 'true' || value.toLowerCase() === 'on' || value === '1';
            }
            return false;
        };

        // Validate required fields
        if (!body.fullName || !body.registeredGrade) {
            req.flash('error_msg', 'Full name and registered grade are required fields.');
            return res.redirect('/register');
        }

        // Parse nested address fields with validation
        const address = {
            city: sanitizeString(body['address.city'] || body.addressCity),
            subCity: sanitizeString(body['address.subCity'] || body.addressSubCity),
            tabia: sanitizeString(body['address.tabia'] || body.addressTabia),
            ketena: sanitizeString(body['address.ketena'] || body.addressKetena),
            block: sanitizeString(body['address.block'] || body.addressBlock),
            nationality: sanitizeString(body['address.nationality'] || body.addressNationality)
        };

        const student = new Student({
            // Registration info
            registrationDate: parseDate(body.registrationDate) || new Date(),
            quarter: sanitizeString(body.quarter),

            // Basic student info
            fullName: sanitizeString(body.fullName),
            sex: sanitizeString(body.sex),
            dob: parseDate(body.dob),
            age: parseNumber(body.age),
            formerGrade: sanitizeString(body.formerGrade),
            registeredGrade: sanitizeString(body.registeredGrade),
            section: sanitizeString(body.section),
            motherTongue: sanitizeString(body.motherTongue),
            bloodType: sanitizeString(body.bloodType),
            weight: parseNumber(body.weight),
            height: parseNumber(body.height),
            handUse: sanitizeString(body.handUse),
            address: address,
            seriousSickness: sanitizeString(body.seriousSickness),

            // File uploads
            studentPic: files.studentPic ? files.studentPic[0].filename : '',

            // Father info
            father: {
                fullName: sanitizeString(body.fatherFullName),
                phone: sanitizeString(body.fatherPhone),
                nationality: sanitizeString(body.fatherNationality),
                education: sanitizeString(body.fatherEducation),
                occupation: sanitizeString(body.fatherOccupation),
                workplace: sanitizeString(body.fatherWorkplace),
                photo: files.fatherPic ? files.fatherPic[0].filename : ''
            },

            // Mother info
            mother: {
                fullName: sanitizeString(body.motherFullName),
                phone: sanitizeString(body.motherPhone),
                nationality: sanitizeString(body.motherNationality),
                education: sanitizeString(body.motherEducation),
                occupation: sanitizeString(body.motherOccupation),
                workplace: sanitizeString(body.motherWorkplace),
                photo: files.motherPic ? files.motherPic[0].filename : ''
            },

            // Guardian info
            guardian: {
                name: sanitizeString(body.guardianName),
                relationship: sanitizeString(body.guardianRelationship),
                phone: sanitizeString(body.guardianPhone)
            },

            // Transport info
            transport: {
                subCity: sanitizeString(body.transportSubCity),
                tabya: sanitizeString(body.transportTabya),
                bus: sanitizeString(body.transportBus),
                startDate: parseDate(body.transportStartDate)
            },

            // Documents
            documents: {
                birthCertificate: files.birthCertificate ? files.birthCertificate[0].filename : '',
                transcript: files.transcript ? files.transcript[0].filename : '',
                parentsID: files.parentsID ? files.parentsID[0].filename : '',
                buyBook: parseBoolean(body.buyBook)
            },

            // Others
            others: {
                shortSight: parseBoolean(body.shortSight),
                longSight: parseBoolean(body.longSight),
                allergic: parseBoolean(body.allergic),
                autism: parseBoolean(body.autism),
                languageProblem: parseBoolean(body.languageProblem)
            }
        });

        // Validate the student object before saving
        const validationError = student.validateSync();
        if (validationError) {
            const errorMessages = Object.values(validationError.errors).map(err => err.message);
            req.flash('error_msg', 'Validation failed: ' + errorMessages.join(', '));
            return res.redirect('/register');
        }

        await student.save();
        req.flash('success_msg', 'Student registered successfully!');
        res.redirect('/students');
    } catch (err) {
        console.error('Registration error:', err);

        // Handle different types of errors
        let errorMessage = 'Registration failed. Please try again.';

        if (err.name === 'ValidationError') {
            const errorMessages = Object.values(err.errors).map(error => {
                if (error.kind === 'required') {
                    return `${error.path} is required`;
                } else if (error.kind === 'enum') {
                    return `${error.path} must be one of: ${error.enumValues.join(', ')}`;
                } else if (error.kind === 'Number') {
                    return `${error.path} must be a valid number`;
                } else if (error.kind === 'Date') {
                    return `${error.path} must be a valid date`;
                } else if (error.kind === 'Boolean') {
                    return `${error.path} must be true or false`;
                } else {
                    return error.message;
                }
            });
            errorMessage = 'Validation failed: ' + errorMessages.join(', ');
        } else if (err.code === 11000) {
            errorMessage = 'A student with this information already exists.';
        } else if (err.name === 'CastError') {
            errorMessage = `Invalid data format for ${err.path}: ${err.value}`;
        }

        req.flash('error_msg', errorMessage);
        res.redirect('/register');
    }
});

// List students with filtering (protected)
router.get('/students', studentController.ensureAdmin, async (req, res) => {
    try {
        const filter = {};

        // Name search (case-insensitive)
        if (req.query.name) {
            filter.fullName = { $regex: req.query.name, $options: 'i' };
        }

        // Grade filter
        if (req.query.grade) {
            filter.registeredGrade = { $regex: req.query.grade, $options: 'i' };
        }

        // Section filter
        if (req.query.section) {
            filter.section = { $regex: req.query.section, $options: 'i' };
        }

        // Student ID filter
        if (req.query.studentId) {
            filter.studentId = { $regex: req.query.studentId, $options: 'i' };
        }

        // Quarter filter
        if (req.query.quarter) {
            filter.quarter = req.query.quarter;
        }

        // Sex filter
        if (req.query.sex) {
            filter.sex = req.query.sex;
        }

        // Status filter
        if (req.query.status) {
            if (req.query.status === 'all') {
                // Show all statuses - don't add status filter
            } else {
                filter.status = req.query.status;
            }
        } else {
            // By default, show only active students unless specifically filtering
            filter.$or = [
                { status: 'active' },
                { status: { $exists: false } },
                { status: null }
            ];
        }

        const students = await Student.find(filter).sort({ registrationDate: -1 });

        res.render('index', {
            students,
            name: req.query.name,
            grade: req.query.grade,
            section: req.query.section,
            studentId: req.query.studentId,
            quarter: req.query.quarter,
            sex: req.query.sex,
            status: req.query.status
        });
    } catch (err) {
        console.error('Error fetching students:', err);
        req.flash('error_msg', 'Error loading students');
        res.render('index', { students: [] });
    }
});

// Edit student form (protected)
router.get('/students/:id/edit', studentController.ensureAdmin, async (req, res) => {
    const student = await Student.findById(req.params.id);
    res.render('edit', { student });
});

// Update student (protected)
router.post('/students/:id', studentController.ensureAdmin, upload.fields([
    { name: 'studentPic', maxCount: 1 },
    { name: 'fatherPic', maxCount: 1 },
    { name: 'motherPic', maxCount: 1 },
    { name: 'birthCertificate', maxCount: 1 },
    { name: 'transcript', maxCount: 1 },
    { name: 'parentsID', maxCount: 1 }
]), async (req, res) => {
    try {
        const { body, files } = req;
        const update = {
            // Registration info
            registrationDate: body.registrationDate,
            quarter: body.quarter,

            // Basic info
            fullName: body.fullName,
            sex: body.sex,
            dob: body.dob,
            age: body.age,
            formerGrade: body.formerGrade,
            registeredGrade: body.registeredGrade,
            section: body.section,
            motherTongue: body.motherTongue,
            bloodType: body.bloodType,
            weight: body.weight,
            height: body.height,
            handUse: body.handUse,
            seriousSickness: body.seriousSickness,

            // Files
            studentPic: files.studentPic ? files.studentPic[0].filename : undefined,
            parentPics: files.parentPics ? files.parentPics.map(f => f.filename) : undefined,
            father: {
                fullName: body.fatherFullName,
                phone: body.fatherPhone,
                nationality: body.fatherNationality,
                education: body.fatherEducation,
                occupation: body.fatherOccupation,
                workplace: body.fatherWorkplace,
                photo: files.parentPics && files.parentPics[0] ? files.parentPics[0].filename : undefined
            },
            mother: {
                fullName: body.motherFullName,
                phone: body.motherPhone,
                nationality: body.motherNationality,
                education: body.motherEducation,
                occupation: body.motherOccupation,
                workplace: body.motherWorkplace,
                photo: files.parentPics && files.parentPics[1] ? files.parentPics[1].filename : undefined
            },
            guardian: {
                name: body.guardianName,
                relationship: body.guardianRelationship,
                phone: body.guardianPhone
            },
            transport: {
                subCity: body.transportSubCity,
                tabya: body.transportTabya,
                bus: body.transportBus,
                startDate: body.transportStartDate
            },
            documents: {
                birthCertificate: !!body.birthCertificate,
                transcript: !!body.transcript,
                parentsID: !!body.parentsID,
                studentPhoto: !!body.studentPhoto,
                fatherPhoto: !!body.fatherPhoto,
                motherPhoto: !!body.motherPhoto,
                buyBook: !!body.buyBook
            },
            others: {
                shortSight: !!body.shortSight,
                longSight: !!body.longSight,
                allergic: !!body.allergic,
                autism: !!body.autism,
                languageProblem: !!body.languageProblem
            }
        };

        // Remove undefined values to avoid overwriting existing data
        Object.keys(update).forEach(key => {
            if (update[key] === undefined) {
                delete update[key];
            }
        });

        await Student.findByIdAndUpdate(req.params.id, update);
        req.flash('success_msg', 'Student updated successfully!');
        res.redirect('/students');
    } catch (err) {
        console.error('Update error:', err);
        req.flash('error_msg', 'Update failed.');
        res.redirect(`/students/${req.params.id}/edit`);
    }
});

// Generate ID Card (protected)
router.get('/students/:id/id-card', studentController.ensureAdmin, async (req, res) => {
    try {
        const student = await Student.findById(req.params.id);
        if (!student) {
            req.flash('error_msg', 'Student not found');
            return res.redirect('/students');
        }
        res.render('id-card', { student });
    } catch (err) {
        console.error('ID Card generation error:', err);
        req.flash('error_msg', 'Failed to generate ID card');
        res.redirect('/students');
    }
});

// View student details (protected)
router.get('/students/:id', studentController.ensureAdmin, async (req, res) => {
    try {
        const student = await Student.findById(req.params.id);
        if (!student) {
            req.flash('error_msg', 'Student not found');
            return res.redirect('/students');
        }
        res.render('students/view', { student });
    } catch (err) {
        console.error('View student error:', err);
        req.flash('error_msg', 'Failed to load student details');
        res.redirect('/students');
    }
});

// Dashboard route (protected)
router.get('/dashboard', studentController.ensureAdmin, async (req, res) => {
    try {
        const students = await Student.find({}).sort({ registrationDate: -1 });

        // Calculate statistics with explicit status checking
        const stats = {
            total: students.length,
            active: students.filter(s => {
                const status = s.status || 'active'; // Default to active if no status set
                return status === 'active';
            }).length,
            pending: students.filter(s => s.status === 'pending').length,
            graduated: students.filter(s => s.status === 'graduated').length,
            transferred: students.filter(s => s.status === 'transferred').length,
            male: students.filter(s => s.sex === 'Male').length,
            female: students.filter(s => s.sex === 'Female').length,
            quarter1: students.filter(s => s.quarter === 'Quarter 1').length,
            quarter2: students.filter(s => s.quarter === 'Quarter 2').length,
            quarter3: students.filter(s => s.quarter === 'Quarter 3').length,
            quarter4: students.filter(s => s.quarter === 'Quarter 4').length,
            thisMonth: students.filter(s => {
                const regDate = new Date(s.registrationDate);
                const now = new Date();
                return regDate.getMonth() === now.getMonth() && regDate.getFullYear() === now.getFullYear();
            }).length,
            thisWeek: students.filter(s => {
                const regDate = new Date(s.registrationDate);
                const now = new Date();
                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                return regDate >= weekAgo;
            }).length
        };

        // Debug logging
        console.log('Dashboard Stats:', {
            total: stats.total,
            active: stats.active,
            pending: stats.pending,
            studentsWithStatus: students.map(s => ({ name: s.fullName, status: s.status || 'undefined' }))
        });

        // Recent registrations (last 10 active students)
        const recentStudents = students.filter(s => s.status === 'active' || !s.status).slice(0, 10);

        // Pending students (last 10)
        const pendingStudents = students.filter(s => s.status === 'pending').slice(0, 10);

        // Grade distribution (active students only)
        const gradeStats = {};
        students.filter(s => s.status === 'active' || !s.status).forEach(student => {
            const grade = student.registeredGrade || 'Unspecified';
            gradeStats[grade] = (gradeStats[grade] || 0) + 1;
        });

        res.render('dashboard', {
            stats,
            recentStudents,
            pendingStudents,
            gradeStats,
            totalStudents: students.length
        });
    } catch (err) {
        console.error('Dashboard error:', err);
        req.flash('error_msg', 'Error loading dashboard');
        res.render('dashboard', {
            stats: {},
            recentStudents: [],
            gradeStats: {},
            totalStudents: 0
        });
    }
});

// Export students to CSV
router.get('/students/export/csv', studentController.ensureAdmin, async (req, res) => {
    try {
        const filter = {};

        // Apply same filters as student list
        if (req.query.name) filter.fullName = { $regex: req.query.name, $options: 'i' };
        if (req.query.grade) filter.registeredGrade = { $regex: req.query.grade, $options: 'i' };
        if (req.query.section) filter.section = { $regex: req.query.section, $options: 'i' };
        if (req.query.studentId) filter.studentId = { $regex: req.query.studentId, $options: 'i' };
        if (req.query.quarter) filter.quarter = req.query.quarter;
        if (req.query.sex) filter.sex = req.query.sex;

        const students = await Student.find(filter).sort({ registrationDate: -1 });

        // Create CSV file path
        const csvFilePath = path.join(__dirname, '../public/exports/students.csv');

        // Ensure exports directory exists
        const exportsDir = path.dirname(csvFilePath);
        if (!fs.existsSync(exportsDir)) {
            fs.mkdirSync(exportsDir, { recursive: true });
        }

        // Define CSV headers
        const csvWriter = createCsvWriter({
            path: csvFilePath,
            header: [
                { id: 'studentId', title: 'Student ID' },
                { id: 'fullName', title: 'Full Name' },
                { id: 'sex', title: 'Sex' },
                { id: 'dob', title: 'Date of Birth' },
                { id: 'age', title: 'Age' },
                { id: 'registeredGrade', title: 'Grade' },
                { id: 'section', title: 'Section' },
                { id: 'quarter', title: 'Quarter' },
                { id: 'registrationDate', title: 'Registration Date' },
                { id: 'motherTongue', title: 'Mother Tongue' },
                { id: 'bloodType', title: 'Blood Type' },
                { id: 'weight', title: 'Weight' },
                { id: 'height', title: 'Height' },
                { id: 'handUse', title: 'Hand Use' },
                { id: 'addressCity', title: 'City' },
                { id: 'addressSubCity', title: 'Sub City' },
                { id: 'addressTabia', title: 'Tabia' },
                { id: 'addressKetena', title: 'Ketena' },
                { id: 'addressBlock', title: 'Block' },
                { id: 'addressNationality', title: 'Nationality' },
                { id: 'seriousSickness', title: 'Serious Sickness' },
                { id: 'fatherName', title: 'Father Name' },
                { id: 'fatherPhone', title: 'Father Phone' },
                { id: 'fatherOccupation', title: 'Father Occupation' },
                { id: 'motherName', title: 'Mother Name' },
                { id: 'motherPhone', title: 'Mother Phone' },
                { id: 'motherOccupation', title: 'Mother Occupation' },
                { id: 'guardianName', title: 'Guardian Name' },
                { id: 'guardianRelationship', title: 'Guardian Relationship' },
                { id: 'guardianPhone', title: 'Guardian Phone' },
                { id: 'transportSubCity', title: 'Transport Sub City' },
                { id: 'transportBus', title: 'Transport Bus' },
                { id: 'shortSight', title: 'Short Sight' },
                { id: 'longSight', title: 'Long Sight' },
                { id: 'allergic', title: 'Allergic' },
                { id: 'autism', title: 'Autism' },
                { id: 'languageProblem', title: 'Language Problem' }
            ]
        });

        // Prepare data for CSV
        const csvData = students.map(student => ({
            studentId: student.studentId || '',
            fullName: student.fullName || '',
            sex: student.sex || '',
            dob: student.dob ? new Date(student.dob).toLocaleDateString() : '',
            age: student.age || '',
            registeredGrade: student.registeredGrade || '',
            section: student.section || '',
            quarter: student.quarter || '',
            registrationDate: student.registrationDate ? new Date(student.registrationDate).toLocaleDateString() : '',
            motherTongue: student.motherTongue || '',
            bloodType: student.bloodType || '',
            weight: student.weight || '',
            height: student.height || '',
            handUse: student.handUse || '',
            addressCity: student.address?.city || '',
            addressSubCity: student.address?.subCity || '',
            addressTabia: student.address?.tabia || '',
            addressKetena: student.address?.ketena || '',
            addressBlock: student.address?.block || '',
            addressNationality: student.address?.nationality || '',
            seriousSickness: student.seriousSickness || '',
            fatherName: student.father?.fullName || '',
            fatherPhone: student.father?.phone || '',
            fatherOccupation: student.father?.occupation || '',
            motherName: student.mother?.fullName || '',
            motherPhone: student.mother?.phone || '',
            motherOccupation: student.mother?.occupation || '',
            guardianName: student.guardian?.name || '',
            guardianRelationship: student.guardian?.relationship || '',
            guardianPhone: student.guardian?.phone || '',
            transportSubCity: student.transport?.subCity || '',
            transportBus: student.transport?.bus || '',
            shortSight: student.others?.shortSight ? 'Yes' : 'No',
            longSight: student.others?.longSight ? 'Yes' : 'No',
            allergic: student.others?.allergic ? 'Yes' : 'No',
            autism: student.others?.autism ? 'Yes' : 'No',
            languageProblem: student.others?.languageProblem ? 'Yes' : 'No'
        }));

        // Write CSV file
        await csvWriter.writeRecords(csvData);

        // Set headers for file download
        const filename = `students_export_${new Date().toISOString().split('T')[0]}.csv`;
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

        // Send file
        res.download(csvFilePath, filename, (err) => {
            if (err) {
                console.error('Download error:', err);
                req.flash('error_msg', 'Failed to download CSV file');
                res.redirect('/students');
            } else {
                // Clean up file after download
                setTimeout(() => {
                    if (fs.existsSync(csvFilePath)) {
                        fs.unlinkSync(csvFilePath);
                    }
                }, 5000);
            }
        });

    } catch (err) {
        console.error('CSV export error:', err);
        req.flash('error_msg', 'Failed to export students to CSV');
        res.redirect('/students');
    }
});

// Print student data
router.get('/students/:id/print', studentController.ensureAdmin, async (req, res) => {
    try {
        const student = await Student.findById(req.params.id);
        if (!student) {
            req.flash('error_msg', 'Student not found');
            return res.redirect('/students');
        }
        res.render('print-student', { student });
    } catch (err) {
        console.error('Print student error:', err);
        req.flash('error_msg', 'Failed to load student for printing');
        res.redirect('/students');
    }
});

// Bulk print ID cards
router.post('/students/print/id-cards', studentController.ensureAdmin, async (req, res) => {
    try {
        const { studentIds } = req.body;
        let students;

        if (studentIds && studentIds.length > 0) {
            // Print selected students
            students = await Student.find({ _id: { $in: studentIds } });
        } else {
            // Print all students (with current filters if any)
            const filter = {};
            if (req.body.name) filter.fullName = { $regex: req.body.name, $options: 'i' };
            if (req.body.grade) filter.registeredGrade = { $regex: req.body.grade, $options: 'i' };
            if (req.body.section) filter.section = { $regex: req.body.section, $options: 'i' };
            if (req.body.quarter) filter.quarter = req.body.quarter;
            if (req.body.sex) filter.sex = req.body.sex;

            students = await Student.find(filter).sort({ registrationDate: -1 });
        }

        res.render('print-id-cards', { students });
    } catch (err) {
        console.error('Bulk print error:', err);
        req.flash('error_msg', 'Failed to load students for printing');
        res.redirect('/students');
    }
});

// Export selected students
router.post('/students/export/selected', studentController.ensureAdmin, async (req, res) => {
    try {
        const { studentIds } = req.body;
        const students = await Student.find({ _id: { $in: studentIds } }).sort({ registrationDate: -1 });

        // Generate CSV
        const csvData = generateCSV(students);
        const filename = `selected_students_${new Date().toISOString().split('T')[0]}.csv`;

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.send(csvData);
    } catch (err) {
        console.error('Export selected error:', err);
        req.flash('error_msg', 'Failed to export selected students');
        res.redirect('/students');
    }
});

// Print selected documents
router.post('/students/print/documents', studentController.ensureAdmin, async (req, res) => {
    try {
        const { studentIds } = req.body;
        const students = await Student.find({ _id: { $in: studentIds } }).sort({ registrationDate: -1 });

        res.render('print-documents', { students });
    } catch (err) {
        console.error('Print documents error:', err);
        req.flash('error_msg', 'Failed to load students for document printing');
        res.redirect('/students');
    }
});

// Export with filters
router.get('/students/export/pdf', studentController.ensureAdmin, async (req, res) => {
    try {
        const filter = buildFilterFromQuery(req.query);
        const students = await Student.find(filter).sort({ registrationDate: -1 });

        // For now, redirect to print view - can be enhanced with PDF generation later
        res.render('print-filtered-students', { students, filters: req.query });
    } catch (err) {
        console.error('PDF export error:', err);
        req.flash('error_msg', 'Failed to generate PDF export');
        res.redirect('/students');
    }
});

// Print filtered students
router.get('/students/print/filtered', studentController.ensureAdmin, async (req, res) => {
    try {
        const filter = buildFilterFromQuery(req.query);
        const students = await Student.find(filter).sort({ registrationDate: -1 });

        res.render('print-filtered-students', { students, filters: req.query });
    } catch (err) {
        console.error('Print filtered error:', err);
        req.flash('error_msg', 'Failed to load filtered students for printing');
        res.redirect('/students');
    }
});

// Change student status
router.post('/students/:id/status', studentController.ensureAdmin, async (req, res) => {
    try {
        const { status, reason, notes } = req.body;
        const student = await Student.findById(req.params.id);

        if (!student) {
            req.flash('error_msg', 'Student not found');
            return res.redirect('/students');
        }

        console.log('Changing student status:', {
            studentId: student._id,
            oldStatus: student.status,
            newStatus: status,
            reason: reason
        });

        // Update student status using findByIdAndUpdate to ensure it saves properly
        await Student.findByIdAndUpdate(req.params.id, {
            status: status,
            statusDate: new Date(),
            statusReason: reason || '',
            statusNotes: notes || ''
        }, { new: true });

        const statusText = status === 'pending' ? 'marked as pending' :
                          status === 'graduated' ? 'marked as graduated' :
                          status === 'transferred' ? 'marked as transferred' : 'reactivated';

        req.flash('success_msg', `Student ${student.fullName} has been ${statusText}`);
        res.redirect('/students');
    } catch (err) {
        console.error('Status change error:', err);
        req.flash('error_msg', 'Failed to update student status');
        res.redirect('/students');
    }
});

// Get status change form
router.get('/students/:id/status', studentController.ensureAdmin, async (req, res) => {
    try {
        const student = await Student.findById(req.params.id);
        if (!student) {
            req.flash('error_msg', 'Student not found');
            return res.redirect('/students');
        }
        res.render('change-status', { student });
    } catch (err) {
        console.error('Status form error:', err);
        req.flash('error_msg', 'Failed to load status change form');
        res.redirect('/students');
    }
});

// Redirect from home page to dashboard
router.get('/', (req, res) => {
    res.redirect('/dashboard');
});

// Helper function to build filter from query parameters
function buildFilterFromQuery(query) {
    const filter = {};

    // Name filter
    if (query.name) {
        filter.fullName = { $regex: query.name, $options: 'i' };
    }

    // Grade filter
    if (query.grade) {
        filter.registeredGrade = { $regex: query.grade, $options: 'i' };
    }

    // Section filter
    if (query.section) {
        filter.section = { $regex: query.section, $options: 'i' };
    }

    // Quarter filter
    if (query.quarter) {
        filter.quarter = query.quarter;
    }

    // Sex filter
    if (query.sex) {
        filter.sex = query.sex;
    }

    // Status filter
    if (query.status) {
        if (query.status === 'all') {
            // Show all statuses - don't add status filter
        } else {
            filter.status = query.status;
        }
    } else {
        // By default, show only active students unless specifically filtering
        filter.$or = [
            { status: 'active' },
            { status: { $exists: false } },
            { status: null }
        ];
    }

    // Name range filter (e.g., "A-M" or "John-Mary")
    if (query.nameRange) {
        const range = query.nameRange.split('-');
        if (range.length === 2) {
            const start = range[0].trim();
            const end = range[1].trim();

            // Check if it's alphabetical range (single letters) or name range
            if (start.length === 1 && end.length === 1) {
                // Alphabetical range
                filter.fullName = {
                    $regex: `^[${start.toUpperCase()}-${end.toUpperCase()}]`,
                    $options: 'i'
                };
            } else {
                // Name range
                filter.fullName = {
                    $gte: start,
                    $lte: end + 'z' // Add 'z' to include names starting with the end name
                };
            }
        }
    }

    return filter;
}

module.exports = router;