const User = require('../models/user');

// Check if user is authenticated
const isAuthenticated = (req, res, next) => {
    if (req.session && req.session.userId) {
        return next();
    }
    req.flash('error_msg', 'Please log in to access this page');
    res.redirect('/login');
};

// Check if user is admin
const isAdmin = async (req, res, next) => {
    try {
        const user = await User.findById(req.session.userId);
        if (user && user.isAdmin) {
            return next();
        }
        req.flash('error_msg', 'Access denied. Admin privileges required.');
        res.redirect('/login');
    } catch (error) {
        console.error('Admin check error:', error);
        req.flash('error_msg', 'An error occurred');
        res.redirect('/login');
    }
};

module.exports = {
    isAuthenticated,
    isAdmin
};
