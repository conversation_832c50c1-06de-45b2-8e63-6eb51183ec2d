#!/bin/bash

echo "🚀 Starting SHEGE SRS Web Application"
echo "====================================="

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "📝 Creating .env from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install --production

# Setup database
echo "🔧 Setting up database..."
npm run setup

# Start application
echo "🌐 Starting web server..."
npm start
