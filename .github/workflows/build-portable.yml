name: Build Portable Applications

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:  # Allow manual trigger

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
        include:
          - os: ubuntu-latest
            platform: linux
            artifact: SHEGE SRS-*.AppImage
          - os: windows-latest
            platform: windows
            artifact: SHEGE SRS-*.exe

    runs-on: ${{ matrix.os }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Create .env file
      run: |
        echo "SYSTEM_NAME=SHEGE SRS" > .env
        echo "MONGODB_URI=mongodb://localhost:27017/student-registration" >> .env
        echo "PORT=3000" >> .env
        echo "NODE_ENV=production" >> .env
        echo "SESSION_SECRET=github-actions-build-secret" >> .env

    - name: Build Linux AppImage
      if: matrix.platform == 'linux'
      run: npm run build-portable-linux

    - name: Build Windows Portable
      if: matrix.platform == 'windows'
      run: npm run build-portable-win

    - name: List build artifacts
      run: |
        echo "Build completed. Artifacts:"
        ls -la dist/

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: shege-srs-${{ matrix.platform }}
        path: |
          dist/${{ matrix.artifact }}
          dist/*.exe
          dist/*.AppImage
        retention-days: 30

  create-release:
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')
    
    steps:
    - name: Download Linux artifacts
      uses: actions/download-artifact@v4
      with:
        name: shege-srs-linux
        path: ./linux-build

    - name: Download Windows artifacts
      uses: actions/download-artifact@v4
      with:
        name: shege-srs-windows
        path: ./windows-build

    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v1.0.0-${{ github.run_number }}
        release_name: SHEGE SRS v1.0.0-${{ github.run_number }}
        body: |
          ## SHEGE SRS Portable Applications
          
          ### Downloads
          - **Linux**: SHEGE SRS AppImage (works on all Linux distributions)
          - **Windows**: SHEGE SRS Portable EXE (no installation required)
          
          ### Prerequisites
          - **MongoDB**: Must be installed and running on target system
          - **Port 3000**: Must be available
          
          ### Installation
          1. Download the appropriate file for your operating system
          2. Make executable (Linux) or run directly (Windows)
          3. Ensure MongoDB is installed and running
          4. Launch the application
          
          ### First Login
          - Username: admin
          - Password: admin123
          - Change password immediately after first login
        draft: false
        prerelease: false

    - name: Upload Linux Release Asset
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./linux-build/SHEGE SRS-1.0.0-x86_64.AppImage
        asset_name: SHEGE-SRS-1.0.0-Linux.AppImage
        asset_content_type: application/octet-stream

    - name: Upload Windows Release Asset
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./windows-build/SHEGE SRS-1.0.0.exe
        asset_name: SHEGE-SRS-1.0.0-Windows.exe
        asset_content_type: application/octet-stream
