// Form validation
(function () {
    'use strict'

    // Fetch all the forms we want to apply custom Bootstrap validation styles to
    const forms = document.querySelectorAll('.needs-validation')

    // Loop over them and prevent submission
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()

// Image preview functionality
function previewImage(input, previewId) {
    const preview = document.getElementById(previewId);
    const file = input.files[0];
    const reader = new FileReader();

    reader.onloadend = function () {
        preview.innerHTML = ''; // Clear previous content
        const img = document.createElement('img');
        img.src = reader.result;
        img.className = 'img-thumbnail';
        img.style.maxWidth = '200px';
        img.style.maxHeight = '200px';
        preview.appendChild(img);
    }

    if (file) {
        reader.readAsDataURL(file);
    } else {
        // Reset to default if no file is selected
        preview.innerHTML = `
            <div class="bg-light d-flex align-items-center justify-content-center" 
                 style="width: 200px; height: 200px; border: 2px dashed #ccc; border-radius: 5px;">
                <div class="text-center p-3">
                    <i class="fas fa-user fa-4x text-muted mb-2"></i>
                    <p class="mb-0 text-muted">No photo selected</p>
                </div>
            </div>
        `;
    }
}

// Toggle medical information fields
document.addEventListener('DOMContentLoaded', function() {
    // Medical condition toggle
    const hasMedicalCondition = document.getElementById('hasMedicalCondition');
    const medicalConditionDetails = document.getElementById('medicalConditionDetails');
    
    if (hasMedicalCondition) {
        toggleField(hasMedicalCondition, medicalConditionDetails);
        hasMedicalCondition.addEventListener('change', () => toggleField(hasMedicalCondition, medicalConditionDetails));
    }

    // Medication toggle
    const takesMedication = document.getElementById('takesMedication');
    const medicationDetails = document.getElementById('medicationDetails');
    
    if (takesMedication) {
        toggleField(takesMedication, medicationDetails);
        takesMedication.addEventListener('change', () => toggleField(takesMedication, medicationDetails));
    }

    // Allergy toggle
    const hasAllergy = document.getElementById('hasAllergy');
    const allergyDetails = document.getElementById('allergyDetails');
    
    if (hasAllergy) {
        toggleField(hasAllergy, allergyDetails);
        hasAllergy.addEventListener('change', () => toggleField(hasAllergy, allergyDetails));
    }

    // Disability toggle
    const hasDisability = document.getElementById('hasDisability');
    const disabilityDetails = document.getElementById('disabilityDetails');
    
    if (hasDisability) {
        toggleField(hasDisability, disabilityDetails);
        hasDisability.addEventListener('change', () => toggleField(hasDisability, disabilityDetails));
    }
});

function toggleField(checkbox, field) {
    if (checkbox && field) {
        field.style.display = checkbox.checked ? 'block' : 'none';
    }
}

// Phone number formatting
const phoneInputs = document.querySelectorAll('input[type="tel"]');
phoneInputs.forEach(input => {
    input.addEventListener('input', function(e) {
        // Remove any non-digit characters
        let value = this.value.replace(/\D/g, '');
        
        // Limit to 9 digits for Ethiopian phone numbers
        if (value.length > 9) {
            value = value.substring(0, 9);
        }
        
        this.value = value;
    });
});

// Date of birth validation
const dobInput = document.getElementById('dateOfBirth');
if (dobInput) {
    dobInput.addEventListener('change', function() {
        const selectedDate = new Date(this.value);
        const today = new Date();
        let age = today.getFullYear() - selectedDate.getFullYear();
        const monthDiff = today.getMonth() - selectedDate.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < selectedDate.getDate())) {
            age--;
        }
        
        // You can add age validation here if needed
        // For example, to ensure the student is at least 4 years old
        if (age < 4) {
            this.setCustomValidity('Student must be at least 4 years old');
        } else {
            this.setCustomValidity('');
        }
    });
}

// Form reset handler
document.querySelector('form').addEventListener('reset', function() {
    // Reset all file inputs
    document.querySelectorAll('input[type="file"]').forEach(input => {
        input.value = '';
    });
    
    // Reset image previews
    document.querySelectorAll('.img-preview').forEach(preview => {
        preview.innerHTML = `
            <div class="bg-light d-flex align-items-center justify-content-center" 
                 style="width: 200px; height: 200px; border: 2px dashed #ccc; border-radius: 5px;">
                <div class="text-center p-3">
                    <i class="fas fa-user fa-4x text-muted mb-2"></i>
                    <p class="mb-0 text-muted">No photo selected</p>
                </div>
            </div>
        `;
    });
    
    // Reset validation
    this.classList.remove('was-validated');
});
