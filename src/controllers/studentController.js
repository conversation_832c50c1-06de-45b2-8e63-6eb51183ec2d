const Student = require('../models/student');
const User = require('../models/user');
const bcrypt = require('bcryptjs');

exports.register = async (req, res) => {
    // Registration logic handled in routes for now
};

exports.list = async (req, res) => {
    // Listing logic handled in routes for now
};

exports.edit = async (req, res) => {
    // Edit logic handled in routes for now
};

exports.update = async (req, res) => {
    // Update logic handled in routes for now
};

exports.ensureAdmin = (req, res, next) => {
    if (req.session && req.session.user) {
        return next();
    }
    req.flash('error_msg', 'Please log in to view this page.');
    res.redirect('/login');
};

exports.loginForm = (req, res) => {
    res.render('login');
};

exports.login = async (req, res) => {
    const { username, password } = req.body;
    const user = await User.findOne({ username });
    if (user && await bcrypt.compare(password, user.password)) {
        req.session.user = user;
        return res.redirect('/students');
    }
    req.flash('error_msg', 'Invalid credentials');
    res.redirect('/login');
};

exports.logout = (req, res) => {
    req.session.destroy(() => {
        res.redirect('/login');
    });
};