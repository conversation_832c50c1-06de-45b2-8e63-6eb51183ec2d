const Student = require('../models/student');
const User = require('../models/user');

// Admin Dashboard
exports.getDashboard = async (req, res) => {
    try {
        const studentCount = await Student.countDocuments();
        const userCount = await User.countDocuments();
        
        res.render('admin/dashboard', {
            title: 'Admin Dashboard',
            studentCount,
            userCount
        });
    } catch (error) {
        console.error('Dashboard error:', error);
        req.flash('error_msg', 'Error loading dashboard');
        res.redirect('/');
    }
};

// List all users (admin only)
exports.getUsers = async (req, res) => {
    try {
        const users = await User.find().sort({ createdAt: -1 });
        res.render('admin/users', {
            title: 'Manage Users',
            users,
            userId: req.session.userId // Add current user ID for comparison in the view
        });
    } catch (error) {
        console.error('Get users error:', error);
        req.flash('error_msg', 'Error fetching users');
        res.redirect('/admin/dashboard');
    }
};

// Create a new user (admin only)
exports.createUser = async (req, res) => {
    const { username, password, isAdmin } = req.body;
    
    try {
        const userExists = await User.findOne({ username });
        if (userExists) {
            req.flash('error_msg', 'Username already exists');
            return res.redirect('/admin/users');
        }

        const newUser = new User({
            username,
            password,
            isAdmin: isAdmin === 'on'
        });

        await newUser.save();
        req.flash('success_msg', 'User created successfully');
        res.redirect('/admin/users');
    } catch (error) {
        console.error('Create user error:', error);
        req.flash('error_msg', 'Error creating user');
        res.redirect('/admin/users');
    }
};

// Delete a user (admin only)
exports.deleteUser = async (req, res) => {
    try {
        const user = await User.findById(req.params.id);
        
        // Prevent deleting self
        if (user._id.toString() === req.session.userId) {
            req.flash('error_msg', 'You cannot delete your own account');
            return res.redirect('/admin/users');
        }

        await User.findByIdAndDelete(req.params.id);
        req.flash('success_msg', 'User deleted successfully');
        res.redirect('/admin/users');
    } catch (error) {
        console.error('Delete user error:', error);
        req.flash('error_msg', 'Error deleting user');
        res.redirect('/admin/users');
    }
};
