const express = require('express');
const router = express.Router();
const Student = require('../models/student');
const multer = require('multer');
const path = require('path');
const studentController = require('../controllers/studentController');

// Multer setup for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, path.join(__dirname, '../public/uploads'));
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + '-' + file.originalname);
    }
});
const upload = multer({ storage: storage });

// Login routes
router.get('/login', studentController.loginForm);
router.post('/login', studentController.login);
router.get('/logout', studentController.logout);

// Protect all routes below
router.use(studentController.ensureAdmin);

// Registration form
router.get('/register', (req, res) => {
    res.render('register');
});

// Register student
router.post('/register', upload.fields([
    { name: 'studentPic', maxCount: 1 },
    { name: 'parentPics', maxCount: 2 }
]), async (req, res) => {
    try {
        const { body, files } = req;
        const student = new Student({
            ...body,
            studentPic: files.studentPic ? files.studentPic[0].filename : '',
            parentPics: files.parentPics ? files.parentPics.map(f => f.filename) : [],
            father: {
                fullName: body.fatherFullName,
                phone: body.fatherPhone,
                nationality: body.fatherNationality,
                education: body.fatherEducation,
                occupation: body.fatherOccupation,
                workplace: body.fatherWorkplace,
                photo: files.parentPics && files.parentPics[0] ? files.parentPics[0].filename : ''
            },
            mother: {
                fullName: body.motherFullName,
                phone: body.motherPhone,
                nationality: body.motherNationality,
                education: body.motherEducation,
                occupation: body.motherOccupation,
                workplace: body.motherWorkplace,
                photo: files.parentPics && files.parentPics[1] ? files.parentPics[1].filename : ''
            },
            guardian: {
                name: body.guardianName,
                relationship: body.guardianRelationship,
                phone: body.guardianPhone
            },
            transport: {
                subCity: body.transportSubCity,
                tabya: body.transportTabya,
                bus: body.transportBus,
                startDate: body.transportStartDate
            },
            documents: {
                birthCertificate: !!body.birthCertificate,
                transcript: !!body.transcript,
                parentsID: !!body.parentsID,
                studentPhoto: !!body.studentPhoto,
                fatherPhoto: !!body.fatherPhoto,
                motherPhoto: !!body.motherPhoto,
                buyBook: !!body.buyBook
            },
            others: {
                shortSight: !!body.shortSight,
                longSight: !!body.longSight,
                allergic: !!body.allergic,
                autism: !!body.autism,
                languageProblem: !!body.languageProblem
            }
        });
        await student.save();
        req.flash('success_msg', 'Student registered successfully!');
        res.redirect('/students');
    } catch (err) {
        req.flash('error_msg', 'Registration failed.');
        res.redirect('/register');
    }
});

// List students with filtering
router.get('/students', async (req, res) => {
    const filter = {};
    if (req.query.grade) filter.registeredGrade = req.query.grade;
    if (req.query.section) filter.section = req.query.section;
    if (req.query.sex) filter.sex = req.query.sex;
    const students = await Student.find(filter);
    res.render('index', { students });
});

// Edit student form
router.get('/students/:id/edit', async (req, res) => {
    const student = await Student.findById(req.params.id);
    res.render('edit', { student });
});

// Update student
router.post('/students/:id', upload.fields([
    { name: 'studentPic', maxCount: 1 },
    { name: 'parentPics', maxCount: 2 }
]), async (req, res) => {
    try {
        const { body, files } = req;
        const update = {
            ...body,
            others: {
        await Student.findByIdAndUpdate(req.params.id, update);
        req.flash('success_msg', 'Student updated successfully!');
        res.redirect('/students');
    } catch (err) {
        req.flash('error_msg', 'Update failed.');
        res.redirect(`/students/${req.params.id}/edit`);
    }
});

// Redirect from home page to students page
router.get('/', (req, res) => {
    res.redirect('/students');
});

module.exports = router;