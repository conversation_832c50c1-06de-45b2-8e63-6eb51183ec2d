const express = require('express');
const router = express.Router();
const Student = require('../models/student');
const multer = require('multer');
const path = require('path');
const studentController = require('../controllers/studentController');

// Multer setup for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, path.join(__dirname, '../public/uploads'));
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + '-' + file.originalname);
    }
});
const upload = multer({ storage: storage });

// Login routes
router.get('/login', studentController.loginForm);
router.post('/login', studentController.login);
router.get('/logout', studentController.logout);

// Protect all routes below
router.use(studentController.ensureAdmin);

// Registration form
router.get('/register', (req, res) => {
    res.render('register');
});

// Register student
router.post('/register', upload.fields([
    { name: 'studentPic', maxCount: 1 },
    { name: 'parentPics', maxCount: 2 },
    { name: 'birthCertificate', maxCount: 1 },
    { name: 'transcript', maxCount: 1 },
    { name: 'parentsID', maxCount: 1 },
    { name: 'studentPhoto', maxCount: 1 },
    { name: 'fatherPhoto', maxCount: 1 },
    { name: 'motherPhoto', maxCount: 1 }
]), async (req, res) => {
    try {
        const { body, files } = req;

        // Parse nested address fields
        const address = {
            city: body['address.city'] || body.addressCity,
            subCity: body['address.subCity'] || body.addressSubCity,
            tabia: body['address.tabia'] || body.addressTabia,
            ketena: body['address.ketena'] || body.addressKetena,
            block: body['address.block'] || body.addressBlock,
            nationality: body['address.nationality'] || body.addressNationality
        };

        const student = new Student({
            // Registration info
            registrationDate: body.registrationDate || new Date(),
            quarter: body.quarter,

            // Basic student info
            fullName: body.fullName,
            sex: body.sex,
            dob: body.dob,
            age: body.age,
            formerGrade: body.formerGrade,
            registeredGrade: body.registeredGrade,
            section: body.section,
            motherTongue: body.motherTongue,
            bloodType: body.bloodType,
            weight: body.weight,
            height: body.height,
            handUse: body.handUse,
            address: address,
            seriousSickness: body.seriousSickness,

            // File uploads
            studentPic: files.studentPic ? files.studentPic[0].filename : '',
            parentPics: files.parentPics ? files.parentPics.map(f => f.filename) : [],

            // Father info
            father: {
                fullName: body.fatherFullName,
                phone: body.fatherPhone,
                nationality: body.fatherNationality,
                education: body.fatherEducation,
                occupation: body.fatherOccupation,
                workplace: body.fatherWorkplace,
                photo: files.parentPics && files.parentPics[0] ? files.parentPics[0].filename : ''
            },

            // Mother info
            mother: {
                fullName: body.motherFullName,
                phone: body.motherPhone,
                nationality: body.motherNationality,
                education: body.motherEducation,
                occupation: body.motherOccupation,
                workplace: body.motherWorkplace,
                photo: files.parentPics && files.parentPics[1] ? files.parentPics[1].filename : ''
            },

            // Guardian info
            guardian: {
                name: body.guardianName,
                relationship: body.guardianRelationship,
                phone: body.guardianPhone
            },

            // Transport info
            transport: {
                subCity: body.transportSubCity,
                tabya: body.transportTabya,
                bus: body.transportBus,
                startDate: body.transportStartDate
            },

            // Documents
            documents: {
                birthCertificate: files.birthCertificate ? files.birthCertificate[0].filename : '',
                transcript: files.transcript ? files.transcript[0].filename : '',
                parentsID: files.parentsID ? files.parentsID[0].filename : '',
                studentPhoto: files.studentPhoto ? files.studentPhoto[0].filename : '',
                fatherPhoto: files.fatherPhoto ? files.fatherPhoto[0].filename : '',
                motherPhoto: files.motherPhoto ? files.motherPhoto[0].filename : '',
                buyBook: !!body.buyBook
            },

            // Others
            others: {
                shortSight: !!body.shortSight,
                longSight: !!body.longSight,
                allergic: !!body.allergic,
                autism: !!body.autism,
                languageProblem: !!body.languageProblem
            }
        });

        await student.save();
        req.flash('success_msg', 'Student registered successfully!');
        res.redirect('/students');
    } catch (err) {
        console.error('Registration error:', err);
        req.flash('error_msg', 'Registration failed: ' + err.message);
        res.redirect('/register');
    }
});

// List students with filtering
router.get('/students', async (req, res) => {
    try {
        const filter = {};

        // Name search (case-insensitive)
        if (req.query.name) {
            filter.fullName = { $regex: req.query.name, $options: 'i' };
        }

        // Grade filter
        if (req.query.grade) {
            filter.registeredGrade = { $regex: req.query.grade, $options: 'i' };
        }

        // Section filter
        if (req.query.section) {
            filter.section = { $regex: req.query.section, $options: 'i' };
        }

        // Student ID filter
        if (req.query.studentId) {
            filter.studentId = { $regex: req.query.studentId, $options: 'i' };
        }

        // Quarter filter
        if (req.query.quarter) {
            filter.quarter = req.query.quarter;
        }

        // Sex filter
        if (req.query.sex) {
            filter.sex = req.query.sex;
        }

        const students = await Student.find(filter).sort({ registrationDate: -1 });

        res.render('index', {
            students,
            name: req.query.name,
            grade: req.query.grade,
            section: req.query.section,
            studentId: req.query.studentId,
            quarter: req.query.quarter,
            sex: req.query.sex
        });
    } catch (err) {
        console.error('Error fetching students:', err);
        req.flash('error_msg', 'Error loading students');
        res.render('index', { students: [] });
    }
});

// Edit student form
router.get('/students/:id/edit', async (req, res) => {
    const student = await Student.findById(req.params.id);
    res.render('edit', { student });
});

// Update student
router.post('/students/:id', upload.fields([
    { name: 'studentPic', maxCount: 1 },
    { name: 'parentPics', maxCount: 2 }
]), async (req, res) => {
    try {
        const { body, files } = req;
        const update = {
            // Registration info
            registrationDate: body.registrationDate,
            quarter: body.quarter,

            // Basic info
            fullName: body.fullName,
            sex: body.sex,
            dob: body.dob,
            age: body.age,
            formerGrade: body.formerGrade,
            registeredGrade: body.registeredGrade,
            section: body.section,
            motherTongue: body.motherTongue,
            bloodType: body.bloodType,
            weight: body.weight,
            height: body.height,
            handUse: body.handUse,
            seriousSickness: body.seriousSickness,

            // Files
            studentPic: files.studentPic ? files.studentPic[0].filename : undefined,
            parentPics: files.parentPics ? files.parentPics.map(f => f.filename) : undefined,
            father: {
                fullName: body.fatherFullName,
                phone: body.fatherPhone,
                nationality: body.fatherNationality,
                education: body.fatherEducation,
                occupation: body.fatherOccupation,
                workplace: body.fatherWorkplace,
                photo: files.parentPics && files.parentPics[0] ? files.parentPics[0].filename : undefined
            },
            mother: {
                fullName: body.motherFullName,
                phone: body.motherPhone,
                nationality: body.motherNationality,
                education: body.motherEducation,
                occupation: body.motherOccupation,
                workplace: body.motherWorkplace,
                photo: files.parentPics && files.parentPics[1] ? files.parentPics[1].filename : undefined
            },
            guardian: {
                name: body.guardianName,
                relationship: body.guardianRelationship,
                phone: body.guardianPhone
            },
            transport: {
                subCity: body.transportSubCity,
                tabya: body.transportTabya,
                bus: body.transportBus,
                startDate: body.transportStartDate
            },
            documents: {
                birthCertificate: !!body.birthCertificate,
                transcript: !!body.transcript,
                parentsID: !!body.parentsID,
                studentPhoto: !!body.studentPhoto,
                fatherPhoto: !!body.fatherPhoto,
                motherPhoto: !!body.motherPhoto,
                buyBook: !!body.buyBook
            },
            others: {
                shortSight: !!body.shortSight,
                longSight: !!body.longSight,
                allergic: !!body.allergic,
                autism: !!body.autism,
                languageProblem: !!body.languageProblem
            }
        };

        // Remove undefined values to avoid overwriting existing data
        Object.keys(update).forEach(key => {
            if (update[key] === undefined) {
                delete update[key];
            }
        });

        await Student.findByIdAndUpdate(req.params.id, update);
        req.flash('success_msg', 'Student updated successfully!');
        res.redirect('/students');
    } catch (err) {
        console.error('Update error:', err);
        req.flash('error_msg', 'Update failed.');
        res.redirect(`/students/${req.params.id}/edit`);
    }
});

// Generate ID Card
router.get('/students/:id/id-card', async (req, res) => {
    try {
        const student = await Student.findById(req.params.id);
        if (!student) {
            req.flash('error_msg', 'Student not found');
            return res.redirect('/students');
        }
        res.render('id-card', { student });
    } catch (err) {
        console.error('ID Card generation error:', err);
        req.flash('error_msg', 'Failed to generate ID card');
        res.redirect('/students');
    }
});

// View student details
router.get('/students/:id', async (req, res) => {
    try {
        const student = await Student.findById(req.params.id);
        if (!student) {
            req.flash('error_msg', 'Student not found');
            return res.redirect('/students');
        }
        res.render('students/view', { student });
    } catch (err) {
        console.error('View student error:', err);
        req.flash('error_msg', 'Failed to load student details');
        res.redirect('/students');
    }
});

// Dashboard route
router.get('/dashboard', async (req, res) => {
    try {
        const students = await Student.find({}).sort({ registrationDate: -1 });

        // Calculate statistics
        const stats = {
            total: students.length,
            male: students.filter(s => s.sex === 'Male').length,
            female: students.filter(s => s.sex === 'Female').length,
            quarter1: students.filter(s => s.quarter === 'Quarter 1').length,
            quarter2: students.filter(s => s.quarter === 'Quarter 2').length,
            quarter3: students.filter(s => s.quarter === 'Quarter 3').length,
            quarter4: students.filter(s => s.quarter === 'Quarter 4').length,
            thisMonth: students.filter(s => {
                const regDate = new Date(s.registrationDate);
                const now = new Date();
                return regDate.getMonth() === now.getMonth() && regDate.getFullYear() === now.getFullYear();
            }).length,
            thisWeek: students.filter(s => {
                const regDate = new Date(s.registrationDate);
                const now = new Date();
                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                return regDate >= weekAgo;
            }).length
        };

        // Recent registrations (last 10)
        const recentStudents = students.slice(0, 10);

        // Grade distribution
        const gradeStats = {};
        students.forEach(student => {
            const grade = student.registeredGrade || 'Unspecified';
            gradeStats[grade] = (gradeStats[grade] || 0) + 1;
        });

        res.render('dashboard', {
            stats,
            recentStudents,
            gradeStats,
            totalStudents: students.length
        });
    } catch (err) {
        console.error('Dashboard error:', err);
        req.flash('error_msg', 'Error loading dashboard');
        res.render('dashboard', {
            stats: {},
            recentStudents: [],
            gradeStats: {},
            totalStudents: 0
        });
    }
});

// Redirect from home page to dashboard
router.get('/', (req, res) => {
    res.redirect('/dashboard');
});

module.exports = router;