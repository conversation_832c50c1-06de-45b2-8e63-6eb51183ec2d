/* Enhanced Design System */
:root {
    /* Primary Colors */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --secondary-color: #764ba2;
    
    /* Status Colors */
    --success-color: #48bb78;
    --success-light: #c6f6d5;
    --warning-color: #ed8936;
    --warning-light: #fbd38d;
    --error-color: #f56565;
    --error-light: #fed7d7;
    --info-color: #4299e1;
    --info-light: #bee3f8;
    
    /* Neutral Colors */
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --border-color: #e2e8f0;
    --border-light: #f7fafc;
    --bg-light: #f7fafc;
    --bg-white: #ffffff;
    --bg-gray-50: #f9fafb;
    --bg-gray-100: #f3f4f6;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
    
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;
    
    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--primary-gradient);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: var(--font-size-sm);
    overflow-x: hidden;
    position: relative;
}

/* Animated Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    z-index: -1;
    pointer-events: none;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Container */
.container {
    max-width: 1400px;
    margin: var(--space-xl) auto;
    padding: var(--space-xl);
    background: rgba(255, 255, 255, 0.98);
    border-radius: var(--radius-3xl);
    box-shadow: var(--shadow-2xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: var(--radius-3xl) var(--radius-3xl) 0 0;
}

/* Header */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-lg) var(--space-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-lg);
}

.logo h1 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.logo h1 i {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-right: var(--space-sm);
}

/* Navigation */
nav {
    display: flex;
    gap: var(--space-md);
    align-items: center;
    flex-wrap: wrap;
}

nav a {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-xl);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    transition: var(--transition-base);
    z-index: -1;
}

nav a:hover::before {
    left: 0;
}

nav a:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

nav a i {
    margin-right: var(--space-xs);
}

.user-info {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    padding: var(--space-sm) var(--space-md);
    background: var(--bg-gray-50);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

/* Cards */
.card {
    background: var(--bg-white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: var(--transition-base);
    position: relative;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-sm) var(--space-lg);
    background: var(--primary-gradient);
    color: white;
    text-decoration: none;
    border-radius: var(--radius-xl);
    font-weight: 600;
    font-size: var(--font-size-sm);
    border: none;
    cursor: pointer;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transition: var(--transition-base);
    z-index: -1;
}

.btn:hover::before {
    left: 0;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0);
}

.btn-secondary {
    background: var(--bg-gray-100);
    color: var(--text-secondary);
    box-shadow: var(--shadow-sm);
}

.btn-secondary::before {
    background: var(--text-secondary);
}

.btn-secondary:hover {
    color: white;
}

/* Form Elements */
.form-group {
    margin-bottom: var(--space-lg);
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    font-size: var(--font-size-sm);
}

.form-group label i {
    margin-right: var(--space-xs);
    color: var(--primary-color);
    width: 16px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--space-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    transition: var(--transition-base);
    background: var(--bg-white);
    color: var(--text-primary);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.form-group input::placeholder {
    color: var(--text-muted);
}

/* Form Sections */
.form-section {
    margin-bottom: var(--space-2xl);
    padding: var(--space-xl);
    background: var(--bg-gray-50);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--border-color);
}

.form-section h2 {
    color: var(--text-primary);
    margin-bottom: var(--space-lg);
    font-size: var(--font-size-xl);
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.form-section h2 i {
    color: var(--primary-color);
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

thead {
    background: var(--primary-gradient);
    color: white;
}

thead th {
    padding: var(--space-lg);
    text-align: left;
    font-weight: 600;
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

tbody tr {
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

tbody tr:hover {
    background: var(--bg-gray-50);
    transform: scale(1.01);
}

tbody tr:last-child {
    border-bottom: none;
}

tbody td {
    padding: var(--space-lg);
    vertical-align: middle;
    font-size: var(--font-size-sm);
}

/* Actions */
.actions {
    display: flex;
    gap: var(--space-sm);
    flex-wrap: wrap;
}

.actions a {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
    transition: var(--transition-fast);
    border: 1px solid transparent;
}

.actions a:nth-child(1) { /* View */
    color: var(--info-color);
    background: var(--info-light);
    border-color: var(--info-color);
}

.actions a:nth-child(2) { /* Edit */
    color: var(--warning-color);
    background: var(--warning-light);
    border-color: var(--warning-color);
}

.actions a:nth-child(3) { /* Status */
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
    border-color: var(--primary-color);
}

.actions a:nth-child(4) { /* Print */
    color: var(--text-secondary);
    background: var(--bg-gray-100);
    border-color: var(--text-secondary);
}

.actions a:nth-child(5) { /* ID Card */
    color: var(--success-color);
    background: var(--success-light);
    border-color: var(--success-color);
}

.actions a:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-2xl);
}

.stat-card {
    background: var(--bg-white);
    padding: var(--space-xl);
    border-radius: var(--radius-2xl);
    text-align: center;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
    transition: var(--transition-base);
    box-shadow: var(--shadow-md);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stat-card .stat-icon {
    position: absolute;
    top: var(--space-lg);
    right: var(--space-lg);
    font-size: var(--font-size-2xl);
    opacity: 0.2;
    color: var(--primary-color);
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--space-sm);
    line-height: 1;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    font-size: var(--font-size-xs);
    letter-spacing: 0.5px;
}

/* Filter Form */
.filter-form {
    background: var(--bg-white);
    padding: var(--space-xl);
    border-radius: var(--radius-2xl);
    margin-bottom: var(--space-2xl);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--space-lg);
    align-items: end;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.filter-form label {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.filter-form input,
.filter-form select {
    min-width: 150px;
}

.filter-form button,
.filter-form .btn {
    height: fit-content;
    margin-top: auto;
    justify-self: stretch;
}

/* Flash Messages */
.flash-success,
.flash-error {
    padding: var(--space-lg);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-xl);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 500;
    box-shadow: var(--shadow-md);
}

.flash-success {
    background: var(--success-light);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.flash-error {
    background: var(--error-light);
    color: var(--error-color);
    border: 1px solid var(--error-color);
}

/* Photo Upload */
.photo-upload-container {
    position: relative;
    width: 200px;
    height: 200px;
    margin: var(--space-lg) auto;
    border: 3px dashed var(--border-color);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    background: var(--bg-gray-50);
    transition: var(--transition-base);
    cursor: pointer;
}

.photo-upload-container:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.02);
}

.photo-upload-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 10;
}

.photo-upload-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: var(--primary-color);
    z-index: 1;
    font-weight: 600;
}

.photo-upload-placeholder i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--space-sm);
}

.photo-upload-preview {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 5;
}

/* Document Upload Grid */
.document-upload-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
    margin-bottom: var(--space-2xl);
}

.document-upload-item {
    background: var(--bg-white);
    padding: var(--space-xl);
    border-radius: var(--radius-2xl);
    border: 2px dashed var(--border-color);
    transition: var(--transition-base);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.document-upload-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: var(--transition-base);
}

.document-upload-item:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.document-upload-item:hover::before {
    transform: scaleX(1);
}

.document-upload-item label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    font-size: var(--font-size-base);
}

.document-upload-item label i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    margin-right: var(--space-sm);
}

.document-upload-item input[type="file"] {
    width: 100%;
    padding: var(--space-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-gray-50);
    font-size: var(--font-size-sm);
    transition: var(--transition-base);
    cursor: pointer;
}

.document-upload-item input[type="file"]:hover {
    border-color: var(--primary-color);
    background: var(--bg-white);
}

.document-upload-item input[type="file"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.document-upload-item small {
    display: block;
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    margin-top: var(--space-sm);
    font-style: italic;
}

/* File Upload States */
.document-upload-item input[type="file"]:valid {
    border-color: var(--success-color);
    background: var(--success-light);
}

.document-upload-item input[type="file"]:invalid {
    border-color: var(--error-color);
    background: var(--error-light);
}

/* Responsive Document Upload */
@media (max-width: 768px) {
    .document-upload-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .document-upload-item {
        padding: var(--space-lg);
    }
}

/* Dropdown Menu */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    min-width: 220px;
    overflow: hidden;
    opacity: 0;
    transform: translateY(-10px);
    transition: var(--transition-base);
    pointer-events: none;
}

.dropdown-menu[style*="block"] {
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-lg);
    color: var(--text-primary);
    text-decoration: none;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
    border-bottom: 1px solid var(--border-light);
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: var(--bg-gray-50);
    color: var(--primary-color);
}

.dropdown-item i {
    width: 16px;
    color: var(--primary-color);
}

/* Checkbox Styles */
input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
    cursor: pointer;
}

/* Enhanced Checkbox Groups */
.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-lg);
    margin-top: var(--space-lg);
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-lg);
    background: var(--bg-white);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: var(--transition-base);
    font-weight: 500;
    font-size: var(--font-size-sm);
    position: relative;
    overflow: hidden;
}

.checkbox-group label::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: var(--transition-base);
}

.checkbox-group label:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.checkbox-group label:hover::before {
    left: 100%;
}

.checkbox-group label i {
    color: var(--primary-color);
    font-size: var(--font-size-base);
    width: 20px;
    text-align: center;
}

.checkbox-group input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin: 0;
    position: relative;
}

.checkbox-group input[type="checkbox"]:checked + i {
    color: var(--success-color);
}

.checkbox-group label:has(input[type="checkbox"]:checked) {
    background: var(--success-light);
    border-color: var(--success-color);
    color: var(--success-color);
}

/* Fieldset Enhancement */
fieldset {
    border: 2px solid var(--border-color);
    border-radius: var(--radius-2xl);
    padding: var(--space-xl);
    margin: var(--space-xl) 0;
    background: var(--bg-gray-50);
    position: relative;
}

fieldset legend {
    background: var(--bg-white);
    padding: var(--space-sm) var(--space-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-xl);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

fieldset legend i {
    color: var(--primary-color);
}

/* Medical Conditions Specific Styling */
.medical-conditions {
    background: linear-gradient(135deg, rgba(72, 187, 120, 0.05), rgba(56, 178, 172, 0.05));
    border-color: rgba(72, 187, 120, 0.3);
}

.medical-conditions legend {
    background: linear-gradient(135deg, var(--success-color), #38b2ac);
    color: white;
    border-color: var(--success-color);
}

.medical-conditions .checkbox-group label {
    background: rgba(255, 255, 255, 0.9);
}

.medical-conditions .checkbox-group label:hover {
    background: rgba(255, 255, 255, 1);
    border-color: var(--success-color);
}

/* Responsive Checkbox Groups */
@media (max-width: 768px) {
    .checkbox-group {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .checkbox-group label {
        padding: var(--space-sm) var(--space-md);
        font-size: var(--font-size-xs);
    }
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: var(--success-light);
    color: var(--success-color);
}

.status-pending {
    background: var(--warning-light);
    color: var(--warning-color);
}

.status-graduated {
    background: var(--info-light);
    color: var(--info-color);
}

.status-transferred {
    background: var(--error-light);
    color: var(--error-color);
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        margin: var(--space-lg);
        padding: var(--space-lg);
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: var(--space-md);
    }

    nav {
        justify-content: center;
        flex-wrap: wrap;
    }

    .container {
        margin: var(--space-md);
        padding: var(--space-md);
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--space-md);
    }

    .filter-form {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    table {
        font-size: var(--font-size-xs);
    }

    thead th,
    tbody td {
        padding: var(--space-sm);
    }

    .actions {
        flex-direction: column;
        gap: var(--space-xs);
    }

    .actions a {
        justify-content: center;
        padding: var(--space-sm);
    }
}

@media (max-width: 480px) {
    .logo h1 {
        font-size: var(--font-size-xl);
    }

    .stat-number {
        font-size: var(--font-size-3xl);
    }

    .form-section {
        padding: var(--space-lg);
    }

    .btn {
        padding: var(--space-md) var(--space-lg);
        font-size: var(--font-size-sm);
    }
}

/* Print Styles */
@media print {
    body {
        background: white !important;
        color: black !important;
    }

    .no-print {
        display: none !important;
    }

    .container {
        box-shadow: none !important;
        border: none !important;
        margin: 0 !important;
        padding: 20px !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-xs); }
.mb-2 { margin-bottom: var(--space-sm); }
.mb-3 { margin-bottom: var(--space-md); }
.mb-4 { margin-bottom: var(--space-lg); }
.mb-5 { margin-bottom: var(--space-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-xs); }
.mt-2 { margin-top: var(--space-sm); }
.mt-3 { margin-top: var(--space-md); }
.mt-4 { margin-top: var(--space-lg); }
.mt-5 { margin-top: var(--space-xl); }

.hidden { display: none !important; }
.visible { display: block !important; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }

/* Tabbed Interface */
.tabs {
    display: flex;
    background: var(--bg-white);
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    border-bottom: none;
    margin-bottom: 0;
}

.tab {
    flex: 1;
    padding: var(--space-lg) var(--space-xl);
    background: var(--bg-gray-100);
    border: none;
    cursor: pointer;
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    transition: var(--transition-base);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    border-right: 1px solid var(--border-color);
}

.tab:last-child {
    border-right: none;
}

.tab:hover {
    background: var(--bg-white);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.tab.active {
    background: var(--bg-white);
    color: var(--primary-color);
    position: relative;
    z-index: 1;
}

.tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.tab i {
    font-size: var(--font-size-base);
}

/* Tab Content */
.tab-content {
    display: none;
    background: var(--bg-white);
    border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    border-top: none;
    padding: var(--space-2xl);
    position: relative;
}

.tab-content.active {
    display: block;
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Tab Navigation Buttons */
.tab-navigation-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--space-2xl);
    padding-top: var(--space-xl);
    border-top: 1px solid var(--border-color);
}

.tab-nav-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    background: var(--bg-gray-100);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    text-decoration: none;
    font-weight: 600;
    font-size: var(--font-size-sm);
    transition: var(--transition-base);
    cursor: pointer;
}

.tab-nav-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.tab-nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.tab-nav-btn.primary {
    background: var(--primary-gradient);
    color: white;
    border-color: var(--primary-color);
}

.tab-nav-btn.primary:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

/* Tab Progress Indicator */
.tab-progress {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-xl);
    padding: var(--space-lg);
    background: var(--bg-gray-50);
    border-radius: var(--radius-xl);
}

.tab-progress-step {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--border-color);
    transition: var(--transition-base);
}

.tab-progress-step.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

.tab-progress-step.completed {
    background: var(--success-color);
}

/* Responsive Tabs */
@media (max-width: 768px) {
    .tabs {
        flex-direction: column;
        border-radius: var(--radius-xl);
    }

    .tab {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        justify-content: flex-start;
        padding: var(--space-md) var(--space-lg);
    }

    .tab:last-child {
        border-bottom: none;
    }

    .tab.active::after {
        display: none;
    }

    .tab.active::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: var(--primary-gradient);
    }

    .tab-content {
        border-radius: var(--radius-xl);
        border: 1px solid var(--border-color);
        margin-top: var(--space-md);
    }

    .tab-navigation-buttons {
        flex-direction: column;
        gap: var(--space-md);
    }

    .tab-nav-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-2xl);
    padding: var(--space-xl);
    background: var(--bg-white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.page-title h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.page-title h1 i {
    color: var(--primary-color);
}

.page-subtitle {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin: 0;
}

.page-actions {
    display: flex;
    gap: var(--space-md);
    align-items: center;
    flex-wrap: wrap;
}

/* Enhanced Quick Stats */
.quick-stats {
    background: var(--bg-white);
    padding: var(--space-lg);
    border-radius: var(--radius-2xl);
    margin-bottom: var(--space-2xl);
    display: flex;
    justify-content: space-around;
    text-align: center;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.quick-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.quick-stat-item {
    flex: 1;
    padding: var(--space-md);
    position: relative;
}

.quick-stat-item:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 20%;
    height: 60%;
    width: 1px;
    background: var(--border-color);
}

.quick-stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--space-xs);
    line-height: 1;
}

.quick-stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

/* Enhanced Table Container */
.table-container {
    background: var(--bg-white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    position: relative;
}

.table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    z-index: 1;
}

/* Responsive adjustments for page header */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: var(--space-lg);
        text-align: center;
    }

    .page-actions {
        justify-content: center;
        width: 100%;
    }

    .quick-stats {
        flex-direction: column;
        gap: var(--space-md);
    }

    .quick-stat-item:not(:last-child)::after {
        display: none;
    }
}
