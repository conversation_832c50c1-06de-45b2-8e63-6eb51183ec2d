body {
    font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f8fb;
}

.hidden {
    display: none !important;
}

header {
    background: #35424a;
    color: #ffffff;
    padding: 10px 0;
    text-align: center;
}

h1 {
    margin: 0;
    color: #1a355e;
    margin-bottom: 18px;
}

.container {
    max-width: 900px;
    margin: 40px auto;
    background: #fff;
    padding: 40px 32px;
    border-radius: 12px;
    box-shadow: 0 4px 24px #b0c4d6;
}

form {
    background: #ffffff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

form input[type="text"],
form input[type="email"],
form input[type="number"] {
    width: 100%;
    padding: 10px;
    margin: 10px 0;
    border: 1px solid #b0c4d6;
    border-radius: 6px;
    font-size: 1em;
    background: #f7fafc;
}

form input[type="file"] {
    background: #fff;
}

form input[type="submit"] {
    background: #2980b9;
    color: #ffffff;
    border: none;
    padding: 12px 28px;
    border-radius: 6px;
    margin-top: 15px;
    cursor: pointer;
    font-size: 1.1em;
    transition: background 0.2s;
}

form input[type="submit"]:hover {
    background: #1a355e;
}

.tabs {
    display: flex;
    border-bottom: 2px solid #2980b9;
    margin-bottom: 20px;
}

.tab {
    padding: 12px 24px;
    cursor: pointer;
    border: none;
    background: none;
    font-size: 1.1em;
    color: #2980b9;
    transition: background 0.2s;
}

.tab.active {
    background: #2980b9;
    color: #fff;
    border-radius: 8px 8px 0 0;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.student-list {
    margin-top: 20px;
}

.student-list table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.student-list th, .student-list td {
    border: 1px solid #b0c4d6;
    text-align: left;
    padding: 10px;
}

.student-list th {
    background-color: #eaf1fb;
    color: #1a355e;
}

.form-section {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    font-weight: 500;
    color: #1a355e;
}

button, .form-actions button {
    background: #2980b9;
    color: #fff;
    border: none;
    padding: 12px 28px;
    border-radius: 6px;
    margin-top: 15px;
    cursor: pointer;
    font-size: 1.1em;
    transition: background 0.2s;
}

button:hover, .form-actions button:hover {
    background: #1a355e;
}

.flash-success {
    color: #27ae60;
    font-weight: 600;
}

.flash-error {
    color: #c0392b;
    font-weight: 600;
}

/* Photo Upload Component Styles - FINAL */
.photo-upload-container {
    position: relative;
    width: 180px;
    height: 180px;
    margin: auto;
    border: 2px dashed #ccc;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.photo-upload-container:hover {
    border-color: #007bff;
    background-color: #e9ecef;
}

.photo-upload-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 10;
}

.photo-upload-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #6c757d;
    z-index: 1;
}

.photo-upload-preview {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 5;
}

.hidden {
    display: none !important;
}