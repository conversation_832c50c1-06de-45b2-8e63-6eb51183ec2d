<!DOCTYPE html>
<html>
<head>
    <title>Student Registration</title>
    <link rel="stylesheet" href="/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        function showTab(tabIndex) {
            var tabs = document.querySelectorAll('.tab');
            var contents = document.querySelectorAll('.tab-content');
            tabs.forEach((tab, i) => {
                tab.classList.toggle('active', i === tabIndex);
                contents[i].classList.toggle('active', i === tabIndex);
            });
            updateNavButtons(tabIndex, tabs.length);
        }
        function updateNavButtons(tabIndex, totalTabs) {
            document.getElementById('prevBtn').style.display = tabIndex === 0 ? 'none' : 'inline-block';
            document.getElementById('nextBtn').style.display = tabIndex === totalTabs - 1 ? 'none' : 'inline-block';
        }
        document.addEventListener('DOMContentLoaded', function() {
            let currentTab = 0;
            showTab(currentTab);
            document.querySelectorAll('.tab').forEach((tab, i) => {
                tab.addEventListener('click', function() { currentTab = i; showTab(i); });
            });
            document.getElementById('nextBtn').addEventListener('click', function(e) {
                e.preventDefault();
                let tabs = document.querySelectorAll('.tab');
                if (currentTab < tabs.length - 1) {
                    currentTab++;
                    showTab(currentTab);
                }
            });
            document.getElementById('prevBtn').addEventListener('click', function(e) {
                e.preventDefault();
                if (currentTab > 0) {
                    currentTab--;
                    showTab(currentTab);
                }
            });
        });
    </script>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">
                <h1><i class="fas fa-graduation-cap"></i> Student Registration System</h1>
            </div>
            <nav>
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/register"><i class="fas fa-user-plus"></i> Register Student</a>
                <a href="/students"><i class="fas fa-users"></i> View Students</a>
                <a href="/login"><i class="fas fa-sign-in-alt"></i> Login</a>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <h1><i class="fas fa-user-plus"></i> Student Registration</h1>
            <form action="/register" method="POST" enctype="multipart/form-data">
                <div class="tabs">
                    <button type="button" class="tab active"><i class="fas fa-user"></i> Student Info</button>
                    <button type="button" class="tab"><i class="fas fa-users"></i> Parent Info</button>
                    <button type="button" class="tab"><i class="fas fa-user-shield"></i> Guardian</button>
                    <button type="button" class="tab"><i class="fas fa-bus"></i> Transport</button>
                    <button type="button" class="tab"><i class="fas fa-file-alt"></i> Documents</button>
                </div>
                <div class="tab-content active">
                    <div class="form-section">
                        <h2><i class="fas fa-clipboard-list"></i> Registration Information</h2>
                        <div class="form-group">
                            <label><i class="fas fa-calendar-alt"></i> Registration Date *</label>
                            <input type="date" name="registrationDate" required value="<%= new Date().toISOString().split('T')[0] %>">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-calendar-check"></i> Quarter/Term *</label>
                            <select name="quarter" required>
                                <option value="">Select Quarter/Term</option>
                                <option value="Quarter 1">Quarter 1</option>
                                <option value="Quarter 2">Quarter 2</option>
                                <option value="Quarter 3">Quarter 3</option>
                                <option value="Quarter 4">Quarter 4</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2><i class="fas fa-user"></i> Basic Information</h2>
                        <div class="form-group">
                            <label><i class="fas fa-camera"></i> Student Photo</label>
                            <input type="file" name="studentPic" accept="image/*">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-user"></i> Full Name *</label>
                            <input type="text" name="fullName" required placeholder="Enter full name">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-venus-mars"></i> Sex</label>
                            <select name="sex" required>
                                <option value="">Select Sex</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-calendar"></i> Date of Birth</label>
                            <input type="date" name="dob">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-birthday-cake"></i> Age</label>
                            <input type="number" name="age" min="1" max="100" placeholder="Enter age">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-layer-group"></i> Former Grade</label>
                            <input type="text" name="formerGrade" placeholder="Enter former grade">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-layer-group"></i> Registered for Grade</label>
                            <input type="text" name="registeredGrade" placeholder="Enter grade to register for">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-users"></i> Section</label>
                            <input type="text" name="section" placeholder="Enter section">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-language"></i> Mother Tongue / First Language</label>
                            <input type="text" name="motherTongue" placeholder="Enter mother tongue">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-tint"></i> Blood Type</label>
                            <input type="text" name="bloodType" placeholder="e.g., A+, B-, O+">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-weight"></i> Weight</label>
                            <input type="text" name="weight" placeholder="Enter weight (kg)">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-ruler-vertical"></i> Height</label>
                            <input type="text" name="height" placeholder="Enter height (cm)">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-hand-paper"></i> Hand Use</label>
                            <select name="handUse">
                                <option value="">Select hand preference</option>
                                <option value="Right">Right</option>
                                <option value="Left">Left</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2><i class="fas fa-map-marker-alt"></i> Address Information</h2>
                        <div class="form-group">
                            <label><i class="fas fa-city"></i> City</label>
                            <input type="text" name="address.city" placeholder="Enter city">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-building"></i> Sub City</label>
                            <input type="text" name="address.subCity" placeholder="Enter sub city">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-map"></i> Tabia</label>
                            <input type="text" name="address.tabia" placeholder="Enter tabia">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-home"></i> Ketena</label>
                            <input type="text" name="address.ketena" placeholder="Enter ketena">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-building"></i> Block</label>
                            <input type="text" name="address.block" placeholder="Enter block">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-flag"></i> Nationality</label>
                            <input type="text" name="address.nationality" placeholder="Enter nationality">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-notes-medical"></i> Any Serious Sickness (Optional)</label>
                            <input type="text" name="seriousSickness" placeholder="Enter any serious medical conditions">
                        </div>
                    </div>

                    <fieldset>
                        <legend><i class="fas fa-plus-circle"></i> Additional Information</legend>
                        <div class="checkbox-group">
                            <label><input type="checkbox" name="shortSight"> <i class="fas fa-eye"></i> Short Sight</label>
                            <label><input type="checkbox" name="longSight"> <i class="fas fa-eye"></i> Long Sight</label>
                            <label><input type="checkbox" name="allergic"> <i class="fas fa-allergies"></i> Allergic</label>
                            <label><input type="checkbox" name="autism"> <i class="fas fa-brain"></i> Autism</label>
                            <label><input type="checkbox" name="languageProblem"> <i class="fas fa-comment-slash"></i> Language Problem</label>
                        </div>
                    </fieldset>
                </div>
            <div class="tab-content">
                <div class="form-section">
                    <h2>Father</h2>
                    <div class="form-group"><label>Photo</label><input type="file" name="parentPics" accept="image/*"></div>
                    <div class="form-group"><label>Full Name</label><input type="text" name="fatherFullName"></div>
                    <div class="form-group"><label>Phone No</label><input type="text" name="fatherPhone"></div>
                    <div class="form-group"><label>Nationality</label><input type="text" name="fatherNationality"></div>
                    <div class="form-group"><label>Level of Education</label><input type="text" name="fatherEducation"></div>
                    <div class="form-group"><label>Occupation</label><input type="text" name="fatherOccupation"></div>
                    <div class="form-group"><label>Work Place</label><input type="text" name="fatherWorkplace"></div>
                    <h2>Mother</h2>
                    <div class="form-group"><label>Photo</label><input type="file" name="parentPics" accept="image/*"></div>
                    <div class="form-group"><label>Full Name</label><input type="text" name="motherFullName"></div>
                    <div class="form-group"><label>Phone No</label><input type="text" name="motherPhone"></div>
                    <div class="form-group"><label>Nationality</label><input type="text" name="motherNationality"></div>
                    <div class="form-group"><label>Level of Education</label><input type="text" name="motherEducation"></div>
                    <div class="form-group"><label>Occupation</label><input type="text" name="motherOccupation"></div>
                    <div class="form-group"><label>Work Place</label><input type="text" name="motherWorkplace"></div>
                </div>
            </div>
            <div class="tab-content">
                <div class="form-section">
                    <div class="form-group"><label>Guardian Name</label><input type="text" name="guardianName"></div>
                    <div class="form-group"><label>Relationship</label><input type="text" name="guardianRelationship"></div>
                    <div class="form-group"><label>Phone Number</label><input type="text" name="guardianPhone"></div>
                </div>
            </div>
            <div class="tab-content">
                <div class="form-section">
                    <div class="form-group"><label>Sub City</label><input type="text" name="transportSubCity"></div>
                    <div class="form-group"><label>Tabya</label><input type="text" name="transportTabya"></div>
                    <div class="form-group"><label>Which Bus</label><input type="text" name="transportBus"></div>
                    <div class="form-group"><label>Start Date</label><input type="date" name="transportStartDate"></div>
                </div>
            </div>
            <div class="tab-content">
                <div class="form-section">
                    <div class="form-group"><label>Birth Certificate (Upload)</label><input type="file" name="birthCertificate" accept="application/pdf,image/*"></div>
                    <div class="form-group"><label>Transcript (Upload)</label><input type="file" name="transcript" accept="application/pdf,image/*"></div>
                    <div class="form-group"><label>Parents ID (Upload)</label><input type="file" name="parentsID" accept="application/pdf,image/*"></div>
                    <div class="form-group"><label>Student Photo (Upload)</label><input type="file" name="studentPhoto" accept="image/*"></div>
                    <div class="form-group"><label>Father's Photo (Upload)</label><input type="file" name="fatherPhoto" accept="image/*"></div>
                    <div class="form-group"><label>Mother's Photo (Upload)</label><input type="file" name="motherPhoto" accept="image/*"></div>
                    <div class="form-group"><label>Would you like to buy a book upon registration?</label><input type="checkbox" name="buyBook"></div>
                </div>
                <div class="form-actions">
                    <button type="submit">Register</button>
                </div>
            </div>
                <div class="form-actions">
                    <div class="nav-buttons">
                        <button id="prevBtn" type="button" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Previous
                        </button>
                        <button id="nextBtn" type="button" class="btn">
                            Next <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </main>
</body>
</html>