<!DOCTYPE html>
<html>
<head>
    <title>Student Registration</title>
    <link rel="stylesheet" href="/styles.css">
    <style>
        .tabs { display: flex; border-bottom: 2px solid #2980b9; margin-bottom: 20px; }
        .tab { padding: 12px 24px; cursor: pointer; border: none; background: none; font-size: 1.1em; color: #2980b9; transition: background 0.2s; }
        .tab.active { background: #2980b9; color: #fff; border-radius: 8px 8px 0 0; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .form-section { margin-bottom: 20px; }
        .form-group { margin-bottom: 12px; }
        .form-group label { font-weight: 500; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; }
        .form-actions { text-align: right; }
    </style>
    <script>
        function showTab(tabIndex) {
            var tabs = document.querySelectorAll('.tab');
            var contents = document.querySelectorAll('.tab-content');
            tabs.forEach((tab, i) => {
                tab.classList.toggle('active', i === tabIndex);
                contents[i].classList.toggle('active', i === tabIndex);
            });
            updateNavButtons(tabIndex, tabs.length);
        }
        function updateNavButtons(tabIndex, totalTabs) {
            document.getElementById('prevBtn').style.display = tabIndex === 0 ? 'none' : 'inline-block';
            document.getElementById('nextBtn').style.display = tabIndex === totalTabs - 1 ? 'none' : 'inline-block';
        }
        document.addEventListener('DOMContentLoaded', function() {
            let currentTab = 0;
            showTab(currentTab);
            document.querySelectorAll('.tab').forEach((tab, i) => {
                tab.addEventListener('click', function() { currentTab = i; showTab(i); });
            });
            document.getElementById('nextBtn').addEventListener('click', function(e) {
                e.preventDefault();
                let tabs = document.querySelectorAll('.tab');
                if (currentTab < tabs.length - 1) {
                    currentTab++;
                    showTab(currentTab);
                }
            });
            document.getElementById('prevBtn').addEventListener('click', function(e) {
                e.preventDefault();
                if (currentTab > 0) {
                    currentTab--;
                    showTab(currentTab);
                }
            });
        });
    </script>
</head>
<body>
    <div class="container">
        <h1>Student Registration</h1>
        <form action="/register" method="POST" enctype="multipart/form-data">
            <div class="tabs">
                <button type="button" class="tab active">Student Info</button>
                <button type="button" class="tab">Parent Info</button>
                <button type="button" class="tab">Guardian</button>
                <button type="button" class="tab">Transport</button>
                <button type="button" class="tab">Documents</button>
            </div>
            <div class="tab-content active">
                <div class="form-section">
                    <div class="form-group"><label>Student Photo</label><input type="file" name="studentPic" accept="image/*"></div>
                    <div class="form-group"><label>Full Name</label><input type="text" name="fullName" required></div>
                    <div class="form-group"><label>Sex</label><select name="sex"><option>Male</option><option>Female</option></select></div>
                    <div class="form-group"><label>Date of Birth</label><input type="date" name="dob"></div>
                    <div class="form-group"><label>Age</label><input type="number" name="age"></div>
                    <div class="form-group"><label>Former Grade</label><input type="text" name="formerGrade"></div>
                    <div class="form-group"><label>Registered for Grade</label><input type="text" name="registeredGrade"></div>
                    <div class="form-group"><label>Section</label><input type="text" name="section"></div>
                    <div class="form-group"><label>Mother Tongue / First Language</label><input type="text" name="motherTongue"></div>
                    <div class="form-group"><label>Blood Type</label><input type="text" name="bloodType"></div>
                    <div class="form-group"><label>Weight</label><input type="text" name="weight"></div>
                    <div class="form-group"><label>Height</label><input type="text" name="height"></div>
                    <div class="form-group"><label>Hand Use</label><select name="handUse"><option>Right</option><option>Left</option></select></div>
                    <div class="form-group"><label>Address: City</label><input type="text" name="address.city"></div>
                    <div class="form-group"><label>Sub City</label><input type="text" name="address.subCity"></div>
                    <div class="form-group"><label>Tabia</label><input type="text" name="address.tabia"></div>
                    <div class="form-group"><label>Ketena</label><input type="text" name="address.ketena"></div>
                    <div class="form-group"><label>Block</label><input type="text" name="address.block"></div>
                    <div class="form-group"><label>Nationality</label><input type="text" name="address.nationality"></div>
                    <div class="form-group"><label>Any Serious Sickness (Optional)</label><input type="text" name="seriousSickness"></div>
                    <fieldset><legend>Others</legend>
                        <label><input type="checkbox" name="shortSight"> Short Sight</label>
                        <label><input type="checkbox" name="longSight"> Long Sight</label>
                        <label><input type="checkbox" name="allergic"> Allergic</label>
                        <label><input type="checkbox" name="autism"> Autism</label>
                        <label><input type="checkbox" name="languageProblem"> Language Problem</label>
                    </fieldset>
                </div>
            </div>
            <div class="tab-content">
                <div class="form-section">
                    <h2>Father</h2>
                    <div class="form-group"><label>Photo</label><input type="file" name="parentPics" accept="image/*"></div>
                    <div class="form-group"><label>Full Name</label><input type="text" name="fatherFullName"></div>
                    <div class="form-group"><label>Phone No</label><input type="text" name="fatherPhone"></div>
                    <div class="form-group"><label>Nationality</label><input type="text" name="fatherNationality"></div>
                    <div class="form-group"><label>Level of Education</label><input type="text" name="fatherEducation"></div>
                    <div class="form-group"><label>Occupation</label><input type="text" name="fatherOccupation"></div>
                    <div class="form-group"><label>Work Place</label><input type="text" name="fatherWorkplace"></div>
                    <h2>Mother</h2>
                    <div class="form-group"><label>Photo</label><input type="file" name="parentPics" accept="image/*"></div>
                    <div class="form-group"><label>Full Name</label><input type="text" name="motherFullName"></div>
                    <div class="form-group"><label>Phone No</label><input type="text" name="motherPhone"></div>
                    <div class="form-group"><label>Nationality</label><input type="text" name="motherNationality"></div>
                    <div class="form-group"><label>Level of Education</label><input type="text" name="motherEducation"></div>
                    <div class="form-group"><label>Occupation</label><input type="text" name="motherOccupation"></div>
                    <div class="form-group"><label>Work Place</label><input type="text" name="motherWorkplace"></div>
                </div>
            </div>
            <div class="tab-content">
                <div class="form-section">
                    <div class="form-group"><label>Guardian Name</label><input type="text" name="guardianName"></div>
                    <div class="form-group"><label>Relationship</label><input type="text" name="guardianRelationship"></div>
                    <div class="form-group"><label>Phone Number</label><input type="text" name="guardianPhone"></div>
                </div>
            </div>
            <div class="tab-content">
                <div class="form-section">
                    <div class="form-group"><label>Sub City</label><input type="text" name="transportSubCity"></div>
                    <div class="form-group"><label>Tabya</label><input type="text" name="transportTabya"></div>
                    <div class="form-group"><label>Which Bus</label><input type="text" name="transportBus"></div>
                    <div class="form-group"><label>Start Date</label><input type="date" name="transportStartDate"></div>
                </div>
            </div>
            <div class="tab-content">
                <div class="form-section">
                    <div class="form-group"><label>Birth Certificate (Upload)</label><input type="file" name="birthCertificate" accept="application/pdf,image/*"></div>
                    <div class="form-group"><label>Transcript (Upload)</label><input type="file" name="transcript" accept="application/pdf,image/*"></div>
                    <div class="form-group"><label>Parents ID (Upload)</label><input type="file" name="parentsID" accept="application/pdf,image/*"></div>
                    <div class="form-group"><label>Student Photo (Upload)</label><input type="file" name="studentPhoto" accept="image/*"></div>
                    <div class="form-group"><label>Father's Photo (Upload)</label><input type="file" name="fatherPhoto" accept="image/*"></div>
                    <div class="form-group"><label>Mother's Photo (Upload)</label><input type="file" name="motherPhoto" accept="image/*"></div>
                    <div class="form-group"><label>Would you like to buy a book upon registration?</label><input type="checkbox" name="buyBook"></div>
                </div>
                <div class="form-actions">
                    <button type="submit">Register</button>
                </div>
            </div>
            <div class="form-actions" style="margin-top:24px;">
                <button id="prevBtn" type="button">Previous</button>
                <button id="nextBtn" type="button">Next</button>
            </div>
        </form>
    </div>
</body>
</html>