<!DOCTYPE html>
<html>
<head>
    <title>Student ID Card - <%= student.fullName %></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .id-card {
            width: 350px;
            height: 220px;
            background: linear-gradient(135deg, #2980b9, #3498db);
            border-radius: 15px;
            margin: 20px auto;
            padding: 20px;
            color: white;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }
        .id-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }
        .school-name {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        .card-content {
            display: flex;
            gap: 15px;
            position: relative;
            z-index: 1;
        }
        .student-photo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid white;
            object-fit: cover;
            background: rgba(255,255,255,0.2);
        }
        .student-info {
            flex: 1;
        }
        .student-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .student-details {
            font-size: 12px;
            line-height: 1.4;
        }
        .student-id {
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            margin-top: 10px;
            text-align: center;
        }
        .print-btn {
            text-align: center;
            margin: 20px 0;
        }
        .print-btn button {
            background: #2980b9;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .print-btn button:hover {
            background: #1f5f8b;
        }
        @media print {
            body { background: white; }
            .print-btn { display: none; }
            .id-card { margin: 0; box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="print-btn">
        <button onclick="window.print()">Print ID Card</button>
        <button onclick="window.history.back()">Back</button>
    </div>
    
    <div class="id-card">
        <div class="school-name">Student Registration System</div>
        <div class="card-content">
            <% if (student.studentPic) { %>
                <img src="/uploads/<%= student.studentPic %>" alt="Student Photo" class="student-photo">
            <% } else { %>
                <div class="student-photo"></div>
            <% } %>
            <div class="student-info">
                <div class="student-name"><%= student.fullName %></div>
                <div class="student-details">
                    <div><strong>Grade:</strong> <%= student.registeredGrade %></div>
                    <div><strong>Section:</strong> <%= student.section %></div>
                    <div><strong>Sex:</strong> <%= student.sex %></div>
                    <% if (student.dob) { %>
                        <div><strong>DOB:</strong> <%= new Date(student.dob).toLocaleDateString() %></div>
                    <% } %>
                </div>
                <div class="student-id">
                    ID: STU<%= student._id.toString().slice(-6).toUpperCase() %>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
