<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Student</title>
    <link rel="stylesheet" href="/styles.css">
</head>
<body>
    <div class="container">
        <h1>Edit Student</h1>
        <form action="/students/<%= student._id %>" method="POST" enctype="multipart/form-data">
            <label>Student Photo: <input type="file" name="studentPic" accept="image/*"></label>
            <label>Full Name: <input type="text" name="fullName" value="<%= student.fullName %>" required></label>
            <label>Sex: <select name="sex">
                <option <%= student.sex === 'Male' ? 'selected' : '' %>>Male</option>
                <option <%= student.sex === 'Female' ? 'selected' : '' %>>Female</option>
            </select></label>
            <label>Date of Birth: <input type="date" name="dob" value="<%= student.dob ? student.dob.toISOString().substring(0,10) : '' %>"></label>
            <label>Age: <input type="number" name="age" value="<%= student.age %>"></label>
            <label>Former Grade: <input type="text" name="formerGrade" value="<%= student.formerGrade %>"></label>
            <label>Registered for Grade: <input type="text" name="registeredGrade" value="<%= student.registeredGrade %>"></label>
            <label>Section: <input type="text" name="section" value="<%= student.section %>"></label>
            <label>Mother Tongue/First Language: <input type="text" name="motherTongue" value="<%= student.motherTongue %>"></label>
            <label>Blood Type: <input type="text" name="bloodType" value="<%= student.bloodType %>"></label>
            <label>Weight: <input type="text" name="weight" value="<%= student.weight %>"></label>
            <label>Height: <input type="text" name="height" value="<%= student.height %>"></label>
            <label>Hand Use: <select name="handUse">
                <option <%= student.handUse === 'Right' ? 'selected' : '' %>>Right</option>
                <option <%= student.handUse === 'Left' ? 'selected' : '' %>>Left</option>
            </select></label>
            <label>Address: City <input type="text" name="address.city" value="<%= student.address.city %>"></label>
            <label>Sub City <input type="text" name="address.subCity" value="<%= student.address.subCity %>"></label>
            <label>Tabia <input type="text" name="address.tabia" value="<%= student.address.tabia %>"></label>
            <label>Ketena <input type="text" name="address.ketena" value="<%= student.address.ketena %>"></label>
            <label>Block <input type="text" name="address.block" value="<%= student.address.block %>"></label>
            <label>Nationality <input type="text" name="address.nationality" value="<%= student.address.nationality %>"></label>
            <label>Any Serious Sickness (Optional): <input type="text" name="seriousSickness" value="<%= student.seriousSickness %>"></label>
            <fieldset>
                <legend>Others</legend>
                <label><input type="checkbox" name="shortSight" <%= student.others.shortSight ? 'checked' : '' %>> Short Sight</label>
                <label><input type="checkbox" name="longSight" <%= student.others.longSight ? 'checked' : '' %>> Long Sight</label>
                <label><input type="checkbox" name="allergic" <%= student.others.allergic ? 'checked' : '' %>> Allergic</label>
                <label><input type="checkbox" name="autism" <%= student.others.autism ? 'checked' : '' %>> Autism</label>
                <label><input type="checkbox" name="languageProblem" <%= student.others.languageProblem ? 'checked' : '' %>> Language Problem</label>
            </fieldset>
            <button type="submit">Update</button>
        </form>
        <a href="/students">Back to Student List</a>
    </div>
</body>
</html>