<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Registration System</title>
    <link rel="stylesheet" href="/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">
                <h1><i class="fas fa-graduation-cap"></i> Student Registration System</h1>
            </div>
            <nav>
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/register"><i class="fas fa-user-plus"></i> Register Student</a>
                <a href="/students"><i class="fas fa-users"></i> View Students</a>
                <% if (typeof username !== 'undefined' && username) { %>
                    <span class="user-info">Welcome, <%= username %>!</span>
                    <a href="/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
                <% } else { %>
                    <a href="/login"><i class="fas fa-sign-in-alt"></i> Login</a>
                <% } %>
            </nav>
        </div>
    </header>
    <main>
        <div class="container">
            <% if (success_msg && success_msg.length > 0) { %>
                <div class="flash-success"><i class="fas fa-check-circle"></i> <%= success_msg %></div>
            <% } %>
            <% if (error_msg && error_msg.length > 0) { %>
                <div class="flash-error"><i class="fas fa-exclamation-circle"></i> <%= error_msg %></div>
            <% } %>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <h1><i class="fas fa-users"></i> Students Management</h1>
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <a href="/dashboard" class="btn btn-secondary">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="/register" class="btn">
                        <i class="fas fa-plus"></i> Register New Student
                    </a>
                    <div class="dropdown" style="position: relative;">
                        <button class="btn btn-secondary" onclick="toggleDropdown('exportDropdown')">
                            <i class="fas fa-download"></i> Export & Print
                        </button>
                        <div id="exportDropdown" class="dropdown-menu" style="display: none; position: absolute; top: 100%; right: 0; background: white; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); z-index: 1000; min-width: 200px;">
                            <a href="#" onclick="exportToCSV()" class="dropdown-item">
                                <i class="fas fa-file-csv"></i> Export to CSV
                            </a>
                            <button onclick="printSelectedIDCards()" class="dropdown-item" style="border: none; background: none; width: 100%; text-align: left;">
                                <i class="fas fa-id-card"></i> Print Selected ID Cards
                            </button>
                            <button onclick="printAllIDCards()" class="dropdown-item" style="border: none; background: none; width: 100%; text-align: left;">
                                <i class="fas fa-print"></i> Print All ID Cards
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats Summary -->
            <div style="background: rgba(255, 255, 255, 0.7); padding: 1rem; border-radius: 15px; margin-bottom: 2rem; display: flex; justify-content: space-around; text-align: center;">
                <div>
                    <div style="font-size: 1.5rem; font-weight: 700; color: #667eea;"><%= students.length %></div>
                    <div style="font-size: 0.9rem; color: #666;">Total Students</div>
                </div>
                <div>
                    <div style="font-size: 1.5rem; font-weight: 700; color: #27ae60;"><%= students.filter(s => s.sex === 'Male').length %></div>
                    <div style="font-size: 0.9rem; color: #666;">Male</div>
                </div>
                <div>
                    <div style="font-size: 1.5rem; font-weight: 700; color: #e91e63;"><%= students.filter(s => s.sex === 'Female').length %></div>
                    <div style="font-size: 0.9rem; color: #666;">Female</div>
                </div>
                <div>
                    <div style="font-size: 1.5rem; font-weight: 700; color: #f39c12;"><%= students.filter(s => s.quarter === 'Quarter 1').length %></div>
                    <div style="font-size: 0.9rem; color: #666;">Q1</div>
                </div>
                <div>
                    <div style="font-size: 1.5rem; font-weight: 700; color: #9b59b6;"><%= students.filter(s => s.quarter === 'Quarter 2').length %></div>
                    <div style="font-size: 0.9rem; color: #666;">Q2</div>
                </div>
                <div>
                    <div style="font-size: 1.5rem; font-weight: 700; color: #3498db;"><%= students.filter(s => s.quarter === 'Quarter 3').length %></div>
                    <div style="font-size: 0.9rem; color: #666;">Q3</div>
                </div>
                <div>
                    <div style="font-size: 1.5rem; font-weight: 700; color: #e74c3c;"><%= students.filter(s => s.quarter === 'Quarter 4').length %></div>
                    <div style="font-size: 0.9rem; color: #666;">Q4</div>
                </div>
            </div>

            <form method="get" action="/students" class="filter-form">
                <label>
                    <i class="fas fa-user"></i> Name
                    <input type="text" name="name" value="<%= typeof name !== 'undefined' ? name : '' %>" placeholder="Search by name">
                </label>
                <label>
                    <i class="fas fa-layer-group"></i> Grade
                    <input type="text" name="grade" value="<%= typeof grade !== 'undefined' ? grade : '' %>" placeholder="Enter grade">
                </label>
                <label>
                    <i class="fas fa-users"></i> Section
                    <input type="text" name="section" value="<%= typeof section !== 'undefined' ? section : '' %>" placeholder="Enter section">
                </label>
                <label>
                    <i class="fas fa-id-card"></i> Student ID
                    <input type="text" name="studentId" value="<%= typeof studentId !== 'undefined' ? studentId : '' %>" placeholder="Enter student ID">
                </label>
                <label>
                    <i class="fas fa-calendar-check"></i> Quarter
                    <select name="quarter">
                        <option value="">All Quarters</option>
                        <option value="Quarter 1" <%= typeof quarter !== 'undefined' && quarter === 'Quarter 1' ? 'selected' : '' %>>Quarter 1</option>
                        <option value="Quarter 2" <%= typeof quarter !== 'undefined' && quarter === 'Quarter 2' ? 'selected' : '' %>>Quarter 2</option>
                        <option value="Quarter 3" <%= typeof quarter !== 'undefined' && quarter === 'Quarter 3' ? 'selected' : '' %>>Quarter 3</option>
                        <option value="Quarter 4" <%= typeof quarter !== 'undefined' && quarter === 'Quarter 4' ? 'selected' : '' %>>Quarter 4</option>
                    </select>
                </label>
                <label>
                    <i class="fas fa-venus-mars"></i> Sex
                    <select name="sex">
                        <option value="">All</option>
                        <option value="Male" <%= typeof sex !== 'undefined' && sex === 'Male' ? 'selected' : '' %>>Male</option>
                        <option value="Female" <%= typeof sex !== 'undefined' && sex === 'Female' ? 'selected' : '' %>>Female</option>
                    </select>
                </label>
                <button type="submit"><i class="fas fa-filter"></i> Filter</button>
                <a href="/students" class="btn btn-secondary"><i class="fas fa-times"></i> Clear</a>
            </form>
            <form id="bulkActionsForm">
                <table>
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleAllCheckboxes()">
                            </th>
                            <th><i class="fas fa-image"></i> Photo</th>
                            <th><i class="fas fa-user"></i> Full Name</th>
                            <th><i class="fas fa-id-card"></i> Student ID</th>
                            <th><i class="fas fa-layer-group"></i> Grade</th>
                            <th><i class="fas fa-users"></i> Section</th>
                            <th><i class="fas fa-calendar-check"></i> Quarter</th>
                            <th><i class="fas fa-calendar-alt"></i> Reg. Date</th>
                            <th><i class="fas fa-venus-mars"></i> Sex</th>
                            <th><i class="fas fa-cogs"></i> Actions</th>
                        </tr>
                    </thead>
                <tbody>
                    <% if (students.length === 0) { %>
                        <tr>
                            <td colspan="10" style="text-align: center; padding: 2rem; color: #666;">
                                <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                                No students found. <a href="/register">Register the first student</a>
                            </td>
                        </tr>
                    <% } else { %>
                        <% students.forEach(student => { %>
                            <tr>
                                <td>
                                    <input type="checkbox" name="studentIds" value="<%= student._id %>" class="student-checkbox">
                                </td>
                                <td>
                                    <% if (student.studentPic) { %>
                                        <img src="/uploads/<%= student.studentPic %>" width="60" height="60" style="border-radius: 50%; object-fit: cover;" />
                                    <% } else { %>
                                        <div style="width: 60px; height: 60px; background: #e1e8ed; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-user" style="color: #666;"></i>
                                        </div>
                                    <% } %>
                                </td>
                                <td><strong><%= student.fullName %></strong></td>
                                <td>
                                    <span style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.85rem; font-weight: 600;">
                                        <%= student.studentId || 'Generating...' %>
                                    </span>
                                </td>
                                <td><%= student.registeredGrade || 'N/A' %></td>
                                <td><%= student.section || 'N/A' %></td>
                                <td>
                                    <% if (student.quarter) { %>
                                        <span style="background: linear-gradient(135deg, #27ae60, #229954); color: white; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.85rem;">
                                            <%= student.quarter %>
                                        </span>
                                    <% } else { %>
                                        N/A
                                    <% } %>
                                </td>
                                <td>
                                    <% if (student.registrationDate) { %>
                                        <%= new Date(student.registrationDate).toLocaleDateString() %>
                                    <% } else { %>
                                        N/A
                                    <% } %>
                                </td>
                                <td>
                                    <% if (student.sex === 'Male') { %>
                                        <i class="fas fa-mars" style="color: #3498db;"></i> Male
                                    <% } else if (student.sex === 'Female') { %>
                                        <i class="fas fa-venus" style="color: #e91e63;"></i> Female
                                    <% } else { %>
                                        N/A
                                    <% } %>
                                </td>
                                <td>
                                    <div class="actions">
                                        <a href="/students/<%= student._id %>"><i class="fas fa-eye"></i> View</a>
                                        <a href="/students/<%= student._id %>/edit"><i class="fas fa-edit"></i> Edit</a>
                                        <a href="/students/<%= student._id %>/print"><i class="fas fa-print"></i> Print</a>
                                        <a href="/students/<%= student._id %>/id-card"><i class="fas fa-id-card"></i> ID Card</a>
                                    </div>
                                </td>
                            </tr>
                        <% }) %>
                    <% } %>
                </tbody>
            </table>
            </form>

            <% if (students.length > 0) { %>
                <div style="text-align: center; margin-top: 2rem; padding: 1.5rem; background: rgba(255, 255, 255, 0.5); border-radius: 15px;">
                    <p style="color: #666; margin-bottom: 1rem;">
                        Showing <%= students.length %> student<%= students.length !== 1 ? 's' : '' %>
                        <% if (name || grade || section || studentId || quarter || sex) { %>
                            with applied filters
                        <% } %>
                    </p>
                    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                        <a href="/register" class="btn">
                            <i class="fas fa-plus"></i> Register New Student
                        </a>
                        <a href="/dashboard" class="btn btn-secondary">
                            <i class="fas fa-chart-bar"></i> View Dashboard
                        </a>
                        <% if (name || grade || section || studentId || quarter || sex) { %>
                            <a href="/students" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Clear Filters
                            </a>
                        <% } %>
                    </div>
                </div>
            <% } %>
        </div>
    </main>

    <script>
        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('exportDropdown');
            const button = event.target.closest('button');
            if (!button || !button.onclick || button.onclick.toString().indexOf('toggleDropdown') === -1) {
                dropdown.style.display = 'none';
            }
        });

        function toggleAllCheckboxes() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.student-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        function getSelectedStudentIds() {
            const checkboxes = document.querySelectorAll('.student-checkbox:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        function printSelectedIDCards() {
            const selectedIds = getSelectedStudentIds();
            if (selectedIds.length === 0) {
                alert('Please select at least one student to print ID cards.');
                return;
            }

            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/students/print/id-cards';
            form.target = '_blank';

            selectedIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'studentIds';
                input.value = id;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function printAllIDCards() {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/students/print/id-cards';
            form.target = '_blank';

            // Add current filter parameters
            const urlParams = new URLSearchParams(window.location.search);
            for (const [key, value] of urlParams) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                form.appendChild(input);
            }

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function exportToCSV() {
            const urlParams = new URLSearchParams(window.location.search);
            const csvUrl = '/students/export/csv' + (urlParams.toString() ? '?' + urlParams.toString() : '');
            window.open(csvUrl, '_blank');
        }
    </script>

    <style>
        .dropdown-menu {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            z-index: 1000;
            min-width: 200px;
        }

        .dropdown-item {
            display: block;
            padding: 0.75rem 1rem;
            color: #2c3e50;
            text-decoration: none;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
            color: #667eea;
        }

        .dropdown-item i {
            margin-right: 0.5rem;
            width: 16px;
        }

        .student-checkbox {
            width: auto;
            margin: 0;
        }

        #selectAll {
            width: auto;
            margin: 0;
        }
    </style>
</body>
</html>