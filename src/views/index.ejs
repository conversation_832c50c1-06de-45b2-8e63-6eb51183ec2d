<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Registration System</title>
    <link rel="stylesheet" href="/styles.css">
</head>
<body>
    <header>
        <h1>Student Registration System</h1>
        <nav>
            <a href="/register">Register Student</a>
            <a href="/students">View Students</a>
            <% if (typeof username !== 'undefined' && username) { %>
                <span>Welcome, <%= username %>!</span>
                <a href="/logout">Logout</a>
            <% } else { %>
                <a href="/login">Login</a>
            <% } %>
        </nav>
    </header>
    <main>
        <h1>Registered Students</h1>
        <form method="get" action="/students">
            <label>Grade: <input type="text" name="grade" value="<%= typeof grade !== 'undefined' ? grade : '' %>"></label>
            <label>Section: <input type="text" name="section" value="<%= typeof section !== 'undefined' ? section : '' %>"></label>
            <label>Sex: <select name="sex">
                <option value="">All</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
            </select></label>
            <button type="submit">Filter</button>
        </form>
        <table>
            <thead>
                <tr>
                    <th>Photo</th>
                    <th>Full Name</th>
                    <th>Grade</th>
                    <th>Section</th>
                    <th>Sex</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <% students.forEach(student => { %>
                    <tr>
                        <td>
                            <% if (student.studentPic) { %>
                                <img src="/uploads/<%= student.studentPic %>" width="50" height="50" />
                            <% } %>
                        </td>
                        <td><%= student.fullName %></td>
                        <td><%= student.registeredGrade %></td>
                        <td><%= student.section %></td>
                        <td><%= student.sex %></td>
                        <td>
                            <a href="/students/<%= student._id %>">View</a> |
                            <a href="/students/<%= student._id %>/edit">Edit</a> |
                            <a href="/students/<%= student._id %>/id-card">ID Card</a>
                        </td>
                    </tr>
                <% }) %>
            </tbody>
        </table>
        <a href="/register"><button>Register New Student</button></a>
    </main>
</body>
</html>