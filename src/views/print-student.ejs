<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Student Data - <%= student.fullName %></title>
    <link rel="stylesheet" href="/styles.css">
    <style>
        @media print {
            body { 
                background: white !important; 
                font-size: 12px;
                line-height: 1.4;
            }
            .no-print { display: none !important; }
            .print-container { 
                max-width: none !important; 
                margin: 0 !important; 
                padding: 20px !important;
                box-shadow: none !important;
                border: none !important;
            }
            .print-header {
                text-align: center;
                border-bottom: 2px solid #000;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }
            .print-section {
                margin-bottom: 25px;
                page-break-inside: avoid;
            }
            .print-section h3 {
                background: #f0f0f0 !important;
                padding: 8px !important;
                margin-bottom: 15px !important;
                border: 1px solid #000 !important;
                font-size: 14px !important;
            }
            .print-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 15px;
            }
            .print-field {
                margin-bottom: 8px;
                border-bottom: 1px dotted #ccc;
                padding-bottom: 3px;
            }
            .print-label {
                font-weight: bold;
                display: inline-block;
                width: 120px;
            }
            .student-photo-print {
                width: 120px;
                height: 120px;
                border: 2px solid #000;
                float: right;
                margin-left: 20px;
                object-fit: cover;
            }
        }
        
        .print-container {
            max-width: 800px;
            margin: 2rem auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .print-actions {
            text-align: center;
            margin-bottom: 2rem;
            gap: 1rem;
            display: flex;
            justify-content: center;
        }
        
        .print-header {
            text-align: center;
            border-bottom: 2px solid #2c3e50;
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }
        
        .print-header h1 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .print-header p {
            color: #666;
            margin: 0;
        }
        
        .print-section {
            margin-bottom: 2rem;
        }
        
        .print-section h3 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 8px;
        }
        
        .print-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .print-field {
            margin-bottom: 0.8rem;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .print-label {
            font-weight: 600;
            color: #2c3e50;
            display: block;
            margin-bottom: 0.2rem;
        }
        
        .print-value {
            color: #666;
        }
        
        .student-photo-container {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .student-photo-print {
            width: 150px;
            height: 150px;
            border: 3px solid #2c3e50;
            border-radius: 10px;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="print-actions no-print">
            <button onclick="window.print()" class="btn">
                <i class="fas fa-print"></i> Print Student Data
            </button>
            <button onclick="window.history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back
            </button>
        </div>
        
        <div class="print-header">
            <h1>Student Registration System</h1>
            <p>Student Information Report</p>
            <p>Generated on: <%= new Date().toLocaleDateString() %></p>
        </div>
        
        <!-- Student Photo -->
        <% if (student.studentPic) { %>
            <div class="student-photo-container">
                <img src="/uploads/<%= student.studentPic %>" alt="<%= student.fullName %>" class="student-photo-print">
            </div>
        <% } %>
        
        <!-- Basic Information -->
        <div class="print-section">
            <h3><i class="fas fa-user"></i> Basic Information</h3>
            <div class="print-grid">
                <div class="print-field">
                    <span class="print-label">Student ID:</span>
                    <span class="print-value"><%= student.studentId || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Full Name:</span>
                    <span class="print-value"><%= student.fullName %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Sex:</span>
                    <span class="print-value"><%= student.sex || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Date of Birth:</span>
                    <span class="print-value"><%= student.dob ? new Date(student.dob).toLocaleDateString() : 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Age:</span>
                    <span class="print-value"><%= student.age || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Blood Type:</span>
                    <span class="print-value"><%= student.bloodType || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Weight:</span>
                    <span class="print-value"><%= student.weight || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Height:</span>
                    <span class="print-value"><%= student.height || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Hand Use:</span>
                    <span class="print-value"><%= student.handUse || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Mother Tongue:</span>
                    <span class="print-value"><%= student.motherTongue || 'N/A' %></span>
                </div>
            </div>
        </div>
        
        <!-- Academic Information -->
        <div class="print-section">
            <h3><i class="fas fa-graduation-cap"></i> Academic Information</h3>
            <div class="print-grid">
                <div class="print-field">
                    <span class="print-label">Former Grade:</span>
                    <span class="print-value"><%= student.formerGrade || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Registered Grade:</span>
                    <span class="print-value"><%= student.registeredGrade || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Section:</span>
                    <span class="print-value"><%= student.section || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Quarter:</span>
                    <span class="print-value"><%= student.quarter || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Registration Date:</span>
                    <span class="print-value"><%= student.registrationDate ? new Date(student.registrationDate).toLocaleDateString() : 'N/A' %></span>
                </div>
            </div>
        </div>
        
        <!-- Address Information -->
        <% if (student.address) { %>
            <div class="print-section">
                <h3><i class="fas fa-map-marker-alt"></i> Address Information</h3>
                <div class="print-grid">
                    <div class="print-field">
                        <span class="print-label">City:</span>
                        <span class="print-value"><%= student.address.city || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Sub City:</span>
                        <span class="print-value"><%= student.address.subCity || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Tabia:</span>
                        <span class="print-value"><%= student.address.tabia || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Ketena:</span>
                        <span class="print-value"><%= student.address.ketena || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Block:</span>
                        <span class="print-value"><%= student.address.block || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Nationality:</span>
                        <span class="print-value"><%= student.address.nationality || 'N/A' %></span>
                    </div>
                </div>
            </div>
        <% } %>
        
        <!-- Parent Information -->
        <div class="print-section">
            <h3><i class="fas fa-users"></i> Parent Information</h3>
            
            <!-- Father -->
            <h4 style="color: #2c3e50; margin-bottom: 1rem;">Father's Information</h4>
            <div class="print-grid">
                <div class="print-field">
                    <span class="print-label">Name:</span>
                    <span class="print-value"><%= student.father?.fullName || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Phone:</span>
                    <span class="print-value"><%= student.father?.phone || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Nationality:</span>
                    <span class="print-value"><%= student.father?.nationality || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Education:</span>
                    <span class="print-value"><%= student.father?.education || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Occupation:</span>
                    <span class="print-value"><%= student.father?.occupation || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Workplace:</span>
                    <span class="print-value"><%= student.father?.workplace || 'N/A' %></span>
                </div>
            </div>
            
            <!-- Mother -->
            <h4 style="color: #2c3e50; margin: 1.5rem 0 1rem;">Mother's Information</h4>
            <div class="print-grid">
                <div class="print-field">
                    <span class="print-label">Name:</span>
                    <span class="print-value"><%= student.mother?.fullName || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Phone:</span>
                    <span class="print-value"><%= student.mother?.phone || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Nationality:</span>
                    <span class="print-value"><%= student.mother?.nationality || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Education:</span>
                    <span class="print-value"><%= student.mother?.education || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Occupation:</span>
                    <span class="print-value"><%= student.mother?.occupation || 'N/A' %></span>
                </div>
                <div class="print-field">
                    <span class="print-label">Workplace:</span>
                    <span class="print-value"><%= student.mother?.workplace || 'N/A' %></span>
                </div>
            </div>
        </div>
        
        <!-- Guardian Information -->
        <% if (student.guardian?.name) { %>
            <div class="print-section">
                <h3><i class="fas fa-user-shield"></i> Guardian Information</h3>
                <div class="print-grid">
                    <div class="print-field">
                        <span class="print-label">Name:</span>
                        <span class="print-value"><%= student.guardian.name %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Relationship:</span>
                        <span class="print-value"><%= student.guardian.relationship %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Phone:</span>
                        <span class="print-value"><%= student.guardian.phone %></span>
                    </div>
                </div>
            </div>
        <% } %>
        
        <!-- Transportation -->
        <% if (student.transport && (student.transport.subCity || student.transport.bus)) { %>
            <div class="print-section">
                <h3><i class="fas fa-bus"></i> Transportation Information</h3>
                <div class="print-grid">
                    <div class="print-field">
                        <span class="print-label">Sub City:</span>
                        <span class="print-value"><%= student.transport.subCity || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Tabya:</span>
                        <span class="print-value"><%= student.transport.tabya || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Bus:</span>
                        <span class="print-value"><%= student.transport.bus || 'N/A' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Start Date:</span>
                        <span class="print-value"><%= student.transport.startDate ? new Date(student.transport.startDate).toLocaleDateString() : 'N/A' %></span>
                    </div>
                </div>
            </div>
        <% } %>
        
        <!-- Medical Information -->
        <div class="print-section">
            <h3><i class="fas fa-notes-medical"></i> Medical Information</h3>
            <% if (student.seriousSickness) { %>
                <div class="print-field">
                    <span class="print-label">Serious Sickness:</span>
                    <span class="print-value"><%= student.seriousSickness %></span>
                </div>
            <% } %>
            
            <% if (student.others) { %>
                <div style="margin-top: 1rem;">
                    <span class="print-label">Special Conditions:</span>
                    <div style="margin-top: 0.5rem;">
                        <% if (student.others.shortSight) { %><span style="background: #e3f2fd; padding: 0.2rem 0.5rem; margin: 0.2rem; border-radius: 3px; display: inline-block;">Short Sight</span><% } %>
                        <% if (student.others.longSight) { %><span style="background: #e3f2fd; padding: 0.2rem 0.5rem; margin: 0.2rem; border-radius: 3px; display: inline-block;">Long Sight</span><% } %>
                        <% if (student.others.allergic) { %><span style="background: #fff3e0; padding: 0.2rem 0.5rem; margin: 0.2rem; border-radius: 3px; display: inline-block;">Allergic</span><% } %>
                        <% if (student.others.autism) { %><span style="background: #f3e5f5; padding: 0.2rem 0.5rem; margin: 0.2rem; border-radius: 3px; display: inline-block;">Autism</span><% } %>
                        <% if (student.others.languageProblem) { %><span style="background: #ffebee; padding: 0.2rem 0.5rem; margin: 0.2rem; border-radius: 3px; display: inline-block;">Language Problem</span><% } %>
                    </div>
                </div>
            <% } %>
        </div>
        
        <!-- Documents -->
        <% if (student.documents) { %>
            <div class="print-section">
                <h3><i class="fas fa-file-alt"></i> Documents Status</h3>
                <div class="print-grid">
                    <div class="print-field">
                        <span class="print-label">Birth Certificate:</span>
                        <span class="print-value"><%= student.documents.birthCertificate ? 'Submitted' : 'Not Submitted' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Transcript:</span>
                        <span class="print-value"><%= student.documents.transcript ? 'Submitted' : 'Not Submitted' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Parents ID:</span>
                        <span class="print-value"><%= student.documents.parentsID ? 'Submitted' : 'Not Submitted' %></span>
                    </div>
                    <div class="print-field">
                        <span class="print-label">Book Purchase:</span>
                        <span class="print-value"><%= student.documents.buyBook ? 'Yes' : 'No' %></span>
                    </div>
                </div>
            </div>
        <% } %>
    </div>
</body>
</html>
