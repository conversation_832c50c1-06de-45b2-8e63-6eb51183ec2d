<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - SHEGE SRS</title>
    <link rel="stylesheet" href="/styles-enhanced.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo">
                <h1><i class="fas fa-graduation-cap"></i> SHEGE SRS</h1>
                <p class="logo-subtitle">Student Registration System</p>
            </div>
            <nav>
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/register"><i class="fas fa-user-plus"></i> Register Student</a>
                <a href="/students"><i class="fas fa-users"></i> View Students</a>
                <% if (typeof username !== 'undefined' && username) { %>
                    <span class="user-info">Welcome, <%= username %>!</span>
                    <a href="/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
                <% } else { %>
                    <a href="/login"><i class="fas fa-sign-in-alt"></i> Login</a>
                <% } %>
            </nav>
        </div>
    </header>
    
    <main>
        <div class="container">
            <% if (success_msg && success_msg.length > 0) { %>
                <div class="flash-success"><i class="fas fa-check-circle"></i> <%= success_msg %></div>
            <% } %>
            <% if (error_msg && error_msg.length > 0) { %>
                <div class="flash-error"><i class="fas fa-exclamation-circle"></i> <%= error_msg %></div>
            <% } %>
            
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
                    <p class="page-subtitle">Overview of student registration system</p>
                </div>
                <div class="page-actions">
                    <a href="/register" class="btn">
                        <i class="fas fa-plus"></i> Register Student
                    </a>
                    <a href="/students" class="btn btn-secondary">
                        <i class="fas fa-users"></i> View Students
                    </a>
                </div>
            </div>
            
            <!-- Main Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><%= stats.total || 0 %></div>
                    <div class="stat-label">Total Students</div>
                    <div class="stat-icon"><i class="fas fa-users"></i></div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= stats.active || 0 %></div>
                    <div class="stat-label">Active Students</div>
                    <div class="stat-icon"><i class="fas fa-user-check"></i></div>
                </div>
                <div class="stat-card" style="background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(230, 126, 34, 0.1)); border-color: rgba(243, 156, 18, 0.2);">
                    <div class="stat-number" style="color: #f39c12;"><%= stats.pending || 0 %></div>
                    <div class="stat-label">Pending Students</div>
                    <div class="stat-icon" style="color: #f39c12;"><i class="fas fa-user-clock"></i></div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= stats.thisWeek || 0 %></div>
                    <div class="stat-label">This Week</div>
                    <div class="stat-icon"><i class="fas fa-calendar-week"></i></div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= stats.male || 0 %></div>
                    <div class="stat-label">Male Students</div>
                    <div class="stat-icon"><i class="fas fa-mars"></i></div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= stats.female || 0 %></div>
                    <div class="stat-label">Female Students</div>
                    <div class="stat-icon"><i class="fas fa-venus"></i></div>
                </div>
            </div>
            
            <!-- Quarter Statistics -->
            <div class="card" style="margin-bottom: 2rem;">
                <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                    <h2 style="margin: 0;"><i class="fas fa-calendar-check"></i> Quarter Distribution</h2>
                </div>
                <div style="padding: 2rem;">
                    <div class="quarter-stats">
                        <div class="quarter-card">
                            <div class="quarter-number"><%= stats.quarter1 || 0 %></div>
                            <div class="quarter-label">Quarter 1</div>
                            <div class="quarter-bar">
                                <div class="quarter-fill" style="width: <%= stats.total > 0 ? (stats.quarter1 / stats.total * 100) : 0 %>%; background: #e74c3c;"></div>
                            </div>
                        </div>
                        <div class="quarter-card">
                            <div class="quarter-number"><%= stats.quarter2 || 0 %></div>
                            <div class="quarter-label">Quarter 2</div>
                            <div class="quarter-bar">
                                <div class="quarter-fill" style="width: <%= stats.total > 0 ? (stats.quarter2 / stats.total * 100) : 0 %>%; background: #f39c12;"></div>
                            </div>
                        </div>
                        <div class="quarter-card">
                            <div class="quarter-number"><%= stats.quarter3 || 0 %></div>
                            <div class="quarter-label">Quarter 3</div>
                            <div class="quarter-bar">
                                <div class="quarter-fill" style="width: <%= stats.total > 0 ? (stats.quarter3 / stats.total * 100) : 0 %>%; background: #27ae60;"></div>
                            </div>
                        </div>
                        <div class="quarter-card">
                            <div class="quarter-number"><%= stats.quarter4 || 0 %></div>
                            <div class="quarter-label">Quarter 4</div>
                            <div class="quarter-bar">
                                <div class="quarter-fill" style="width: <%= stats.total > 0 ? (stats.quarter4 / stats.total * 100) : 0 %>%; background: #3498db;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Registrations, Pending Students, and Grade Distribution -->
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                <!-- Recent Registrations -->
                <div class="card">
                    <div style="background: linear-gradient(135deg, #27ae60, #229954); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                        <h3 style="margin: 0;"><i class="fas fa-clock"></i> Recent Registrations</h3>
                    </div>
                    <div style="padding: 1.5rem;">
                        <% if (recentStudents && recentStudents.length > 0) { %>
                            <div class="recent-list">
                                <% recentStudents.forEach(student => { %>
                                    <div class="recent-item">
                                        <div class="recent-info">
                                            <div class="recent-name"><%= student.fullName %></div>
                                            <div class="recent-details">
                                                <%= student.registeredGrade || 'N/A' %> - <%= student.section || 'N/A' %>
                                                <span class="recent-quarter"><%= student.quarter || 'N/A' %></span>
                                            </div>
                                        </div>
                                        <div class="recent-date">
                                            <%= student.registrationDate ? new Date(student.registrationDate).toLocaleDateString() : 'N/A' %>
                                        </div>
                                    </div>
                                <% }) %>
                            </div>
                            <div style="text-align: center; margin-top: 1rem;">
                                <a href="/students" class="btn btn-secondary">
                                    <i class="fas fa-eye"></i> View All Students
                                </a>
                            </div>
                        <% } else { %>
                            <div style="text-align: center; padding: 2rem; color: #666;">
                                <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                                No students registered yet.
                                <br><br>
                                <a href="/register" class="btn">
                                    <i class="fas fa-plus"></i> Register First Student
                                </a>
                            </div>
                        <% } %>
                    </div>
                </div>

                <!-- Pending Students -->
                <div class="card">
                    <div style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                        <h3 style="margin: 0;"><i class="fas fa-user-clock"></i> Pending Students</h3>
                    </div>
                    <div style="padding: 1.5rem;">
                        <% if (pendingStudents && pendingStudents.length > 0) { %>
                            <div class="recent-list">
                                <% pendingStudents.forEach(student => { %>
                                    <div class="recent-item">
                                        <div class="recent-info">
                                            <div class="recent-name"><%= student.fullName %></div>
                                            <div class="recent-details">
                                                <%= student.registeredGrade || 'N/A' %> - <%= student.section || 'N/A' %>
                                                <% if (student.statusReason) { %>
                                                    <br><small style="color: #e67e22;">Reason: <%= student.statusReason %></small>
                                                <% } %>
                                            </div>
                                        </div>
                                        <div class="recent-date">
                                            <%= student.statusDate ? new Date(student.statusDate).toLocaleDateString() : 'N/A' %>
                                        </div>
                                    </div>
                                <% }) %>
                            </div>
                            <div style="text-align: center; margin-top: 1rem;">
                                <a href="/students?status=pending" class="btn btn-secondary">
                                    <i class="fas fa-eye"></i> View All Pending
                                </a>
                            </div>
                        <% } else { %>
                            <div style="text-align: center; padding: 2rem; color: #666;">
                                <i class="fas fa-user-check" style="font-size: 3rem; margin-bottom: 1rem; display: block; color: #27ae60;"></i>
                                No pending students.
                                <br>All students are active!
                            </div>
                        <% } %>
                    </div>
                </div>

                <!-- Grade Distribution -->
                <div class="card">
                    <div style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                        <h3 style="margin: 0;"><i class="fas fa-chart-bar"></i> Grade Distribution</h3>
                    </div>
                    <div style="padding: 1.5rem;">
                        <% if (gradeStats && Object.keys(gradeStats).length > 0) { %>
                            <div class="grade-list">
                                <% Object.entries(gradeStats).forEach(([grade, count]) => { %>
                                    <div class="grade-item">
                                        <div class="grade-info">
                                            <span class="grade-name">Grade <%= grade %></span>
                                            <span class="grade-count"><%= count %> students</span>
                                        </div>
                                        <div class="grade-bar">
                                            <div class="grade-fill" style="width: <%= totalStudents > 0 ? (count / totalStudents * 100) : 0 %>%;"></div>
                                        </div>
                                    </div>
                                <% }) %>
                            </div>
                        <% } else { %>
                            <div style="text-align: center; padding: 2rem; color: #666;">
                                <i class="fas fa-chart-bar" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                                No grade data available yet.
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card">
                <div style="background: linear-gradient(135deg, #9b59b6, #8e44ad); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
                    <h3 style="margin: 0;"><i class="fas fa-bolt"></i> Quick Actions</h3>
                </div>
                <div style="padding: 2rem;">
                    <div class="quick-actions">
                        <a href="/register" class="action-card">
                            <div class="action-icon"><i class="fas fa-user-plus"></i></div>
                            <div class="action-title">Register New Student</div>
                            <div class="action-desc">Add a new student to the system</div>
                        </a>
                        <a href="/students" class="action-card">
                            <div class="action-icon"><i class="fas fa-search"></i></div>
                            <div class="action-title">Search Students</div>
                            <div class="action-desc">Find and manage existing students</div>
                        </a>
                        <a href="/students?quarter=Quarter%201" class="action-card">
                            <div class="action-icon"><i class="fas fa-filter"></i></div>
                            <div class="action-title">View by Quarter</div>
                            <div class="action-desc">Filter students by quarter/term</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
