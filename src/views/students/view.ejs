<%- include('../partials/header', { title: 'Student Details' }) %>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Student Details</h1>
        <div>
            <a href="/students" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            <% if (isAdmin) { %>
                <a href="/students/<%= student._id %>/edit" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Edit
                </a>
            <% } %>
        </div>
    </div>

    <% if (success_msg) { %>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <%= success_msg %>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <% } %>

    <div class="row">
        <!-- Student Photo -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <% if (student.studentPic) { %>
                        <img src="/uploads/<%= student.studentPic %>" alt="<%= student.fullName %>" class="img-fluid rounded-circle mb-3" style="width: 200px; height: 200px; object-fit: cover;">
                    <% } else { %>
                        <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 200px; height: 200px;">
                            <i class="fas fa-user fa-5x"></i>
                        </div>
                    <% } %>
                    <h4 class="card-title"><%= student.fullName %></h4>
                    <p class="card-text text-muted">
                        <%= student.registeredGrade %> - <%= student.section %><br>
                        Student ID: <%= student.studentId || 'N/A' %>
                    </p>
                </div>
            </div>
        </div>

        <!-- Student Information -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Personal Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Full Name:</strong> <%= student.fullName %></p>
                            <p><strong>Date of Birth:</strong> <%= student.dateOfBirth ? new Date(student.dateOfBirth).toLocaleDateString() : 'N/A' %></p>
                            <p><strong>Gender:</strong> <%= student.sex %></p>
                            <p><strong>Blood Type:</strong> <%= student.bloodType || 'N/A' %></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Phone:</strong> <%= student.phoneNumber %></p>
                            <p><strong>Email:</strong> <%= student.email || 'N/A' %></p>
                            <p><strong>Address:</strong> <%= student.address || 'N/A' %></p>
                            <p><strong>Nationality:</strong> <%= student.nationality || 'N/A' %></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Parent Information -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Parent Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Father's Information -->
                        <div class="col-md-6">
                            <h6>Father's Information</h6>
                            <p><strong>Name:</strong> <%= student.father?.fullName || 'N/A' %></p>
                            <p><strong>Phone:</strong> <%= student.father?.phone || 'N/A' %></p>
                            <p><strong>Occupation:</strong> <%= student.father?.occupation || 'N/A' %></p>
                        </div>
                        
                        <!-- Mother's Information -->
                        <div class="col-md-6">
                            <h6>Mother's Information</h6>
                            <p><strong>Name:</strong> <%= student.mother?.fullName || 'N/A' %></p>
                            <p><strong>Phone:</strong> <%= student.mother?.phone || 'N/A' %></p>
                            <p><strong>Occupation:</strong> <%= student.mother?.occupation || 'N/A' %></p>
                        </div>
                    </div>
                    
                    <!-- Guardian Information -->
                    <% if (student.guardian?.name) { %>
                        <hr>
                        <h6>Guardian Information</h6>
                        <p><strong>Name:</strong> <%= student.guardian.name %></p>
                        <p><strong>Relationship:</strong> <%= student.guardian.relationship %></p>
                        <p><strong>Phone:</strong> <%= student.guardian.phone %></p>
                    <% } %>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Additional Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Transportation</h6>
                            <% if (student.transport) { %>
                                <p><strong>Sub-City:</strong> <%= student.transport.subCity || 'N/A' %></p>
                                <p><strong>Tabya:</strong> <%= student.transport.tabya || 'N/A' %></p>
                                <p><strong>Bus:</strong> <%= student.transport.bus || 'N/A' %></p>
                                <p><strong>Start Date:</strong> <%= student.transport.startDate ? new Date(student.transport.startDate).toLocaleDateString() : 'N/A' %></p>
                            <% } else { %>
                                <p>No transportation information available.</p>
                            <% } %>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>Documents</h6>
                            <% if (student.documents) { %>
                                <ul class="list-unstyled">
                                    <li><i class="<%= student.documents.birthCertificate ? 'fas fa-check text-success' : 'fas fa-times text-danger' %>"></i> Birth Certificate</li>
                                    <li><i class="<%= student.documents.transcript ? 'fas fa-check text-success' : 'fas fa-times text-danger' %>"></i> Transcript</li>
                                    <li><i class="<%= student.documents.parentsID ? 'fas fa-check text-success' : 'fas fa-times text-danger' %>"></i> Parents' ID</li>
                                    <li><i class="<%= student.documents.studentPhoto ? 'fas fa-check text-success' : 'fas fa-times text-danger' %>"></i> Student Photo</li>
                                    <li><i class="<%= student.documents.buyBook ? 'fas fa-check text-success' : 'fas fa-times text-danger' %>"></i> Book Purchase</li>
                                </ul>
                            <% } else { %>
                                <p>No document information available.</p>
                            <% } %>
                        </div>
                    </div>
                    
                    <!-- Special Notes -->
                    <% if (student.others) { %>
                        <hr>
                        <h6>Special Notes</h6>
                        <ul class="list-unstyled">
                            <% if (student.others.shortSight) { %><li><i class="fas fa-eye text-info"></i> Short Sight</li><% } %>
                            <% if (student.others.longSight) { %><li><i class="fas fa-eye text-info"></i> Long Sight</li><% } %>
                            <% if (student.others.allergic) { %><li><i class="fas fa-allergies text-warning"></i> Allergies</li><% } %>
                            <% if (student.others.autism) { %><li><i class="fas fa-brain text-info"></i> Autism</li><% } %>
                            <% if (student.others.languageProblem) { %><li><i class="fas fa-comment-slash text-danger"></i> Language Problem</li><% } %>
                        </ul>
                    <% } %>
                </div>
            </div>
        </div>
    </div>
</div>

<%- include('../partials/footer') %>
