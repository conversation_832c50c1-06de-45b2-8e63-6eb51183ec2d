const mongoose = require('mongoose');

const parentSchema = new mongoose.Schema({
    fullName: String,
    phone: String,
    nationality: String,
    education: String,
    occupation: String,
    workplace: String,
    photo: String // path to uploaded photo
});

const guardianSchema = new mongoose.Schema({
    name: String,
    relationship: String,
    phone: String
});

const transportSchema = new mongoose.Schema({
    subCity: String,
    tabya: String,
    bus: String,
    startDate: Date
});

const documentSchema = new mongoose.Schema({
    birthCertificate: Boolean,
    transcript: Boolean,
    parentsID: Boolean,
    studentPhoto: Boolean,
    fatherPhoto: Boolean,
    motherPhoto: Boolean,
    buyBook: Boolean
});

const studentSchema = new mongoose.Schema({
    studentPic: String, // path to uploaded student photo
    parentPics: [String], // paths to uploaded parent photos
    fullName: { type: String, required: true },
    sex: String,
    dob: Date,
    age: Number,
    formerGrade: String,
    registeredGrade: String,
    section: String,
    motherTongue: String,
    bloodType: String,
    weight: String,
    height: String,
    handUse: String,
    address: {
        city: String,
        subCity: String,
        tabia: String,
        ketena: String,
        block: String,
        nationality: String
    },
    seriousSickness: String,
    others: {
        shortSight: Boolean,
        longSight: Boolean,
        allergic: Boolean,
        autism: Boolean,
        languageProblem: Boolean
    },
    father: parentSchema,
    mother: parentSchema,
    guardian: guardianSchema,
    transport: transportSchema,
    documents: documentSchema
});

const Student = mongoose.model('Student', studentSchema);

module.exports = Student;