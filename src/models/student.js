const mongoose = require('mongoose');

const parentSchema = new mongoose.Schema({
    fullName: String,
    phone: String,
    nationality: String,
    education: String,
    occupation: String,
    workplace: String,
    photo: String // path to uploaded photo
});

const guardianSchema = new mongoose.Schema({
    name: String,
    relationship: String,
    phone: String
});

const transportSchema = new mongoose.Schema({
    subCity: String,
    tabya: String,
    bus: String,
    startDate: Date
});

const documentSchema = new mongoose.Schema({
    birthCertificate: String, // file path
    transcript: String, // file path
    parentsID: String, // file path
    studentPhoto: String, // file path
    fatherPhoto: String, // file path
    motherPhoto: String, // file path
    buyBook: Boolean
});

const studentSchema = new mongoose.Schema({
    studentPic: String, // path to uploaded student photo
    parentPics: [String], // paths to uploaded parent photos
    fullName: { type: String, required: true },
    sex: String,
    dob: Date,
    age: Number,
    formerGrade: String,
    registeredGrade: String,
    section: String,
    motherTongue: String,
    bloodType: String,
    weight: String,
    height: String,
    handUse: String,

    // Registration Information
    registrationDate: { type: Date, default: Date.now },
    quarter: {
        type: String,
        enum: ['Quarter 1', 'Quarter 2', 'Quarter 3', 'Quarter 4'],
        required: true
    },
    studentId: { type: String, unique: true }, // Custom student ID

    address: {
        city: String,
        subCity: String,
        tabia: String,
        ketena: String,
        block: String,
        nationality: String
    },
    seriousSickness: String,
    others: {
        shortSight: Boolean,
        longSight: Boolean,
        allergic: Boolean,
        autism: Boolean,
        languageProblem: Boolean
    },
    father: parentSchema,
    mother: parentSchema,
    guardian: guardianSchema,
    transport: transportSchema,
    documents: documentSchema
}, {
    timestamps: true // This will add createdAt and updatedAt fields
});

// Pre-save middleware to generate unique student ID
studentSchema.pre('save', async function(next) {
    if (!this.studentId) {
        const year = new Date().getFullYear();
        const quarter = this.quarter ? this.quarter.replace('Quarter ', 'Q') : 'Q1';

        // Find the last student with similar ID pattern
        const lastStudent = await this.constructor.findOne({
            studentId: new RegExp(`^STU${year}${quarter}`)
        }).sort({ studentId: -1 });

        let nextNumber = 1;
        if (lastStudent && lastStudent.studentId) {
            const lastNumber = parseInt(lastStudent.studentId.slice(-4));
            nextNumber = lastNumber + 1;
        }

        // Generate ID: STU2024Q1001, STU2024Q1002, etc.
        this.studentId = `STU${year}${quarter}${nextNumber.toString().padStart(3, '0')}`;
    }
    next();
});

const Student = mongoose.model('Student', studentSchema);

module.exports = Student;