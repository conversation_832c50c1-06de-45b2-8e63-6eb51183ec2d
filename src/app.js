require('dotenv').config();
const express = require('express');
const path = require('path');
const session = require('express-session');
const flash = require('connect-flash');
const methodOverride = require('method-override');
const connectDB = require('./config/db');
const systemConfig = require('./config/system');

const app = express();

// Connect to MongoDB
connectDB();

// Middleware
app.use(express.urlencoded({ extended: true }));
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));
app.use(methodOverride('_method'));

// Session configuration
app.use(session({
    secret: process.env.SESSION_SECRET || 'your_strong_secret_key_here',
    resave: false,
    saveUninitialized: false,
    cookie: {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
}));
app.use(flash());

// Middleware to make user data available in all templates
app.use((req, res, next) => {
    res.locals.username = req.session.username;
    res.locals.isAdmin = req.session.isAdmin;
    res.locals.success_msg = req.flash('success_msg');
    res.locals.error_msg = req.flash('error_msg');

    // Make system config available to all views
    res.locals.systemConfig = systemConfig;
    res.locals.systemName = systemConfig.systemName;
    res.locals.systemFullName = systemConfig.systemFullName;
    res.locals.schoolName = systemConfig.schoolName;

    next();
});

// EJS setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Global flash messages
app.use((req, res, next) => {
    res.locals.success_msg = req.flash('success_msg');
    res.locals.error_msg = req.flash('error_msg');
    next();
});

// Routes
const studentRoutes = require('./routes/studentRoutes');

// Application routes - student routes handle their own authentication
app.use('/', studentRoutes);

// Admin routes
try {
    const adminRoutes = require('./routes/adminRoutes');
    app.use('/admin', adminRoutes);
} catch (err) {
    console.log('Admin routes not found, skipping...');
}

// 404 Handler
app.use((req, res) => {
    res.status(404).render('error', {
        title: 'Page Not Found',
        message: 'The page you are looking for does not exist.'
    });
});

// Error handler
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).render('error', {
        title: 'Server Error',
        message: 'Something went wrong on our end. Please try again later.'
    });
});

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});